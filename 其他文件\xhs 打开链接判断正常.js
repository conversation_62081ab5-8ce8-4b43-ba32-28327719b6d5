"ui";

// 全局变量，用于标记是否已经在运行
let 正在运行 = false;

// 创建UI界面
ui.layout(
    <vertical padding="16">
        <text textSize="24sp" textColor="#FF5722" gravity="center" margin="0 0 0 16">小红书自动点赞工具</text>
        
        <text textSize="16sp" textColor="#666666" margin="0 16 0 0">链接文件路径</text>
        <input id="文件路径" text="/mnt/shared/Pictures/" hint="输入链接文件路径" />
        
        <text textSize="16sp" textColor="#666666" margin="0 16 0 0">链接文件名</text>
        <input id="链接文件名" text="links.txt" hint="输入链接文件名" />
        
        <text textSize="16sp" textColor="#666666" margin="0 16 0 0">最大操作数量</text>
        <input id="最大操作数量" text="10" inputType="number" hint="输入最大操作数量" />
        
        <text textSize="16sp" textColor="#666666" margin="0 16 0 0">操作间隔时间 (秒)</text>
        <input id="操作间隔时间" text="5" inputType="number" hint="输入操作间隔时间(秒)" />
        
        <button id="开始按钮" text="开始操作" textColor="#ffffff" bg="#FF5722" margin="0 32 0 0" />
        
        <text id="状态文本" textSize="14sp" textColor="#333333" margin="0 16 0 0" />
    </vertical>
);

// 点击开始按钮的事件
ui.开始按钮.click(function() {
    // 防止重复点击
    if (正在运行) {
        toast("任务已在运行中，请等待完成");
        return;
    }
    
    正在运行 = true;
    
    // 获取用户输入
    let 文件路径 = ui.文件路径.text();
    let 文件名 = ui.链接文件名.text();
    let 最大操作数 = parseInt(ui.最大操作数量.text());
    let 操作间隔 = parseInt(ui.操作间隔时间.text());
    
    // 更新状态
    ui.状态文本.setText("开始执行，即将隐藏界面...");
    
    // 隐藏界面
    ui.layout(
        <frame>
            <text text="正在后台运行..." textSize="12sp" textColor="#888888" gravity="center"/>
        </frame>
    );
    
    // 创建单个工作线程
    let 工作线程 = threads.start(function() {
        try {
            // 请求截屏权限
            if (!requestScreenCapture()) {
                toast("请求截屏权限失败");
                正在运行 = false;
                return;
            }
            
            // 执行主要功能
            执行自动点赞(文件路径, 文件名, 最大操作数, 操作间隔);
        } catch (e) {
            console.error("执行过程中出错: " + e);
        } finally {
            // 无论如何都要重置运行状态
            正在运行 = false;
        }
    });
});

/**
 * 执行自动点赞主功能
 * @param {string} 文件路径 - 文件所在路径
 * @param {string} 文件名 - 文件名
 * @param {number} 最大操作数 - 最大操作链接数
 * @param {number} 操作间隔 - 操作间隔时间(秒)
 */
function 执行自动点赞(文件路径, 文件名, 最大操作数, 操作间隔) {
    // 读取链接文件
    let 链接列表 = 读取链接文件(文件路径 + 文件名);
    if (!链接列表 || 链接列表.length === 0) {
        toast("链接文件读取失败或为空");
        return;
    }
    
    // 限制操作数量
    let 实际操作数 = Math.min(链接列表.length, 最大操作数);
    toast("共读取到 " + 链接列表.length + " 条链接，计划处理 " + 实际操作数 + " 条");
    
    // 循环处理每个链接
    for (let i = 0; i < 实际操作数; i++) {
        try {
            let 链接 = 链接列表[i];
            toast("处理第 " + (i + 1) + " 条链接: " + 链接);
            
            // 打开链接
            打开链接(链接);
            
            // 等待指定时间
            sleep(操作间隔 * 1000);
        } catch (e) {
            console.error("处理链接时出错: " + e);
            sleep(2000);
        }
    }
    
    toast("全部操作已完成");
}

/**
 * 读取链接文件
 * @param {string} 完整路径 - 链接文件的完整路径
 * @returns {Array} 链接数组
 */
function 读取链接文件(完整路径) {
    try {
        let 文件 = files.read(完整路径);
        let 链接列表 = 文件.split("\n");
        
        // 过滤空行
        链接列表 = 链接列表.filter(链接 => 链接.trim() !== "");
        
        return 链接列表;
    } catch (e) {
        console.error("读取链接文件失败: " + e);
        return [];
    }
}

/**
 * 打开链接并处理弹窗
 * @param {string} 链接 - 要打开的链接
 */
function 打开链接(链接) {
    try {
        // 使用浏览器打开链接
        app.openUrl(链接);
        console.log("已打开链接: " + 链接);
        sleep(3000); // 等待页面加载
        
        // 处理可能的弹窗，直到进入小红书
        处理打开流程();
    } catch (e) {
        console.error("打开链接失败: " + e);
    }
}

/**
 * 处理从浏览器打开到小红书的全流程
 */
function 处理打开流程() {
    // 设置最大尝试次数
    let 最大尝试次数 = 10;
    let 当前尝试次数 = 0;
    
    while (当前尝试次数 < 最大尝试次数) {
        // 获取当前所有文本元素
        let 所有文本 = textMatches(".*").find();
        let 文本数组 = [];
        
        // 收集所有文本及其位置
        所有文本.forEach(function(元素) {
            if (元素.text() && 元素.text().trim() !== "") {
                文本数组.push({
                    文本: 元素.text().trim(), // 去除首尾空格，确保精准匹配
                    bounds: 元素.bounds()
                });
            }
        });
        
        // 定义需要点击的关键词 - 精准匹配用
        let 关键词列表 = [
            "始终", "Chrome", "确定", "展开", "打开", "继续",
            "在不登录账号的情况下使用", "同意", "知道了",
            "浏览", "允许", "确认", "继续访问", "我同意",
            // 添加更多可能的精准按钮文本
            "打开方式", "选择浏览器", "使用浏览器打开", "使用Chrome打开",
            "仅本次", "总是", "取消", "是", "否"
        ];
        
        let 已点击 = false;
        
        // 检查每个关键词
        for (let i = 0; i < 关键词列表.length; i++) {
            let 关键词 = 关键词列表[i];
            
            // 寻找精准匹配关键词的文本
            for (let j = 0; j < 文本数组.length; j++) {
                let 项目 = 文本数组[j];
                
                // 改为精准匹配
                if (项目.文本 === 关键词) {
                    console.log("找到精准匹配关键词: " + 关键词);
                    
                    // 获取元素中心坐标
                    let x = 项目.bounds.centerX();
                    let y = 项目.bounds.centerY();
                    
                    // 点击该位置
                    click(x, y);
                    console.log("点击坐标: " + x + ", " + y);
                    
                    已点击 = true;
                    sleep(1000); // 等待点击后的反应
                    break;
                }
            }
            
            if (已点击) {
                break;
            }
        }
        
        // 检查是否已经进入小红书应用
        if (检查是否进入小红书()) {
            console.log("已成功进入小红书");
            return true;
        }
        
        // 如果没有找到任何可点击的元素，增加尝试计数
        if (!已点击) {
            当前尝试次数++;
            sleep(1000);
        }
    }
    
    console.log("达到最大尝试次数，未能成功进入小红书");
    return false;
}

/**
 * 检查是否已进入小红书
 * @returns {boolean} 是否在小红书中
 */
function 检查是否进入小红书() {
    // 使用包名检查当前应用是否为小红书
    // 小红书的包名是 com.xingin.xhs
    return currentPackage() === "com.xingin.xhs" || 
           // 备用检查方法，防止包名获取失败
           getPackageName("小红书") === currentPackage();
}
