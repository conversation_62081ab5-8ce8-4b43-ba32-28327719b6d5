// 测试启动小红书函数
console.log("=== 测试启动小红书函数 ===");

console.log("\n📱 函数功能:");
console.log("• 函数名: 启动小红书(等待时间)");
console.log("• 包名: com.xingin.xhs");
console.log("• 默认等待时间: 3000ms (3秒)");
console.log("• 返回值: boolean (是否成功启动)");

console.log("\n🔧 启动方法 (按优先级):");
console.log("1. app.launch() - AutoJS官方API");
console.log("2. Intent启动 - 使用具体Activity");
console.log("3. Shell命令 - am start命令");
console.log("4. 备用方法 - 通过设置页面切换");

console.log("\n✅ 功能特点:");
console.log("• 多重启动方法，提高成功率");
console.log("• 自动检测应用是否真正启动");
console.log("• 智能切换到前台");
console.log("• 完善的错误处理");
console.log("• 详细的日志输出");

console.log("\n📋 使用示例:");
console.log("// 基本使用");
console.log("let 启动成功 = 启动小红书();");
console.log("");
console.log("// 自定义等待时间");
console.log("let 启动成功 = 启动小红书(5000); // 等待5秒");
console.log("");
console.log("// 检查启动结果");
console.log("if (启动成功) {");
console.log("    console.log('小红书启动成功');");
console.log("} else {");
console.log("    console.log('小红书启动失败');");
console.log("}");

console.log("\n🎯 应用场景:");
console.log("• 脚本开始时启动小红书");
console.log("• 应用崩溃后重新启动");
console.log("• 切换应用后回到小红书");
console.log("• 自动化流程中的应用启动");

console.log("\n⚡ 技术细节:");
console.log("• 使用currentPackage()检测当前应用");
console.log("• 支持多种启动方式的降级处理");
console.log("• 异常处理确保脚本稳定性");
console.log("• 可配置的等待时间适应不同设备");

console.log("\n🔍 检测逻辑:");
console.log("1. 调用启动方法");
console.log("2. 等待指定时间");
console.log("3. 检查当前前台应用包名");
console.log("4. 如果不是小红书，尝试切换");
console.log("5. 失败则尝试下一种启动方法");

console.log("\n测试完成 ✨");
