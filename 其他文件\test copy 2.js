/**
 * 小红书元素获取测试脚本 - 支持无障碍和Root模式
 * 
 * 此脚本演示如何使用 uiautomator dump 命令获取界面元素
 * 并解析出元素的文本内容和坐标
 */

// 操作模式控制变量: 1=无障碍模式, 2=Root Shell模式
const 操作模式 = 2; // 默认使用Root Shell模式

// 检查操作模式并输出提示
if (操作模式 === 1) {
    console.log("当前使用无障碍模式操作，请确保已开启无障碍服务");
    // 检查无障碍服务是否已启用
    if (!auto.service) {
        console.log("无障碍服务未启用，尝试启动...");
        auto.waitFor();
    }
} else if (操作模式 === 2) {
    console.log("当前使用Root Shell模式操作，请确保已获取Root权限");
    // 检查Root权限
    if (!shell("su -c 'echo root_test'", true).result.includes("root_test")) {
        console.log("警告：未获取到 root 权限，脚本可能无法正常工作");
    } else {
        console.log("Root权限检查通过");
    }
} else {
    console.log("错误：未知的操作模式，请设置为1(无障碍)或2(Root Shell)");
}

/**
 * 使用 uiautomator dump 获取当前界面的 XML 结构
 * @returns {string|null} XML 内容或 null（如果失败）
 */
function 获取界面XML() {
    console.log("开始获取界面 XML...");
    
    // 使用 root 权限执行 uiautomator dump 命令
    let result = shell("su -c 'uiautomator dump /sdcard/window_dump.xml'", true);
    
    if (result.code === 0) {
        console.log("界面 XML 导出成功");
        
        // 读取导出的 XML 文件
        try {
            let xmlContent = files.read("/sdcard/window_dump.xml");
            console.log("成功读取 XML 文件，大小: " + xmlContent.length + " 字节");
            return xmlContent;
        } catch (e) {
            console.error("读取 XML 文件失败: " + e.message);
            return null;
        }
    } else {
        console.error("界面 XML 导出失败: " + result.error);
        return null;
    }
}

/**
 * 从 XML 中提取所有文本元素及其坐标
 * @param {string} xmlContent - XML 内容
 * @returns {Array} 元素数组，每个元素包含文本和坐标
 */
function 提取文本元素(xmlContent) {
    console.log("开始解析 XML 中的文本元素...");
    let 元素列表 = [];
    let 文本正则 = /(<node [^>]*text="([^"]*)"[^>]*bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"[^>]*>)/g;
    let 匹配结果;
    let 空文本计数 = 0;
    while ((匹配结果 = 文本正则.exec(xmlContent)) !== null) {
        let nodeStr = 匹配结果[1];
        let 文本 = 匹配结果[2];
        if (!文本 || 文本.trim() === "") {
            空文本计数++;
            continue;
        }
        let 左 = parseInt(匹配结果[3]);
        let 上 = parseInt(匹配结果[4]);
        let 右 = parseInt(匹配结果[5]);
        let 下 = parseInt(匹配结果[6]);
        // 解析所有属性
        let 属性 = {};
        let 属性正则 = /(\w+)="([^"]*)"/g;
        let 属性匹配;
        while ((属性匹配 = 属性正则.exec(nodeStr)) !== null) {
            属性[属性匹配[1]] = 属性匹配[2];
        }
        元素列表.push({
            文本: 文本,
            坐标: {
                左: 左,
                上: 上,
                右: 右,
                下: 下,
                中心X: Math.floor((左 + 右) / 2),
                中心Y: Math.floor((上 + 下) / 2)
            },
            原始属性: 属性
        });
    }
    console.log("共找到 " + 元素列表.length + " 个有效文本元素 (已过滤 " + 空文本计数 + " 个空文本元素)");
    return 元素列表;
}

/**
 * 从 XML 中提取特定文本的元素
 * @param {string} xmlContent - XML 内容
 * @param {string} 目标文本 - 要查找的文本（部分匹配）
 * @returns {Object|null} 找到的元素或 null
 */
function 查找特定文本元素(xmlContent, 目标文本) {
    console.log("查找包含文本 '" + 目标文本 + "' 的元素...");
    
    let 所有元素 = 提取文本元素(xmlContent);
    
    // 查找包含目标文本的元素
    for (let i = 0; i < 所有元素.length; i++) {
        if (所有元素[i].文本.includes(目标文本)) {
            console.log("找到匹配元素: " + JSON.stringify(所有元素[i]));
            return 所有元素[i];
        }
    }
    
    console.log("未找到包含文本 '" + 目标文本 + "' 的元素");
    return null;
}

/**
 * 使用 shell 命令模拟点击
 * @param {number} x - X 坐标
 * @param {number} y - Y 坐标
 * @returns {boolean} 是否成功
 */
function shell点击(x, y) {
    console.log("执行 shell 点击: (" + x + ", " + y + ")");
    
    let result = shell("su -c 'input tap " + x + " " + y + "'", true);
    let 成功 = result.code === 0;
    
    console.log(成功 ? "点击成功" : "点击失败: " + result.error);
    return 成功;
}

/**
 * 查找并点击特定文本的元素
 * @param {string} 目标文本 - 要查找的文本
 * @returns {boolean} 是否成功
 */
function 查找并点击(目标文本) {
    let xmlContent = 获取界面XML();
    if (!xmlContent) return false;
    
    let 元素 = 查找特定文本元素(xmlContent, 目标文本);
    if (!元素) return false;
    
    return shell点击(元素.坐标.中心X, 元素.坐标.中心Y);
}

/**
 * 查找点赞按钮并点击
 * @returns {boolean} 是否成功
 */
function 查找点赞按钮() {
    let xmlContent = 获取界面XML();
    if (!xmlContent) return false;
    
    // 先尝试查找包含"点赞"的元素
    let 点赞元素 = 查找特定文本元素(xmlContent, "点赞");
    
    if (点赞元素) {
        return shell点击(点赞元素.坐标.中心X, 点赞元素.坐标.中心Y);
    }
    
    // 如果没找到，可能需要查找特定的图标或其他标识
    console.log("未找到点赞按钮");
    return false;
}

/**
 * 打印当前界面的所有文本元素
 */
function 打印所有文本元素() {
    let xmlContent = 获取界面XML();
    if (!xmlContent) return;
    
    let 元素列表 = 提取文本元素(xmlContent);
    
    console.log("==== 当前界面文本元素 ====");
    for (let i = 0; i < 元素列表.length; i++) {
        let 元素 = 元素列表[i];
        // 跳过空文本内容
        if (!元素.文本 || 元素.文本.trim() === "") continue;
        
        console.log((i + 1) + ". 文本: [" + 元素.文本 + "], 坐标: (" + 
                   元素.坐标.中心X + ", " + 元素.坐标.中心Y + ")");
    }
    console.log("==== 共 " + 元素列表.length + " 个有效元素 ====");
    
    // 新增：标记核心元素
    标记核心元素(元素列表);
}

/**
 * 查找元素
 * @param {string} 选择器 - 元素选择器，如text("文本")或id("id")
 * @returns {UiObject|null} 找到的元素或null
 */
function 查找元素(选择器) {
    if (操作模式 === 1) {
        // 无障碍模式
        try {
            let 元素 = 选择器.findOne(1000);
            return 元素 || null;
        } catch (e) {
            console.error("查找元素出错: " + e.message);
            return null;
        }
    } else {
        // Root Shell模式下无法直接使用无障碍选择器
        console.log("Root Shell模式下不支持直接使用选择器查找元素");
        return null;
    }
}

/**
 * 查找文本元素并点击
 * @param {string} 文本 - 要查找的文本
 * @returns {boolean} 是否成功点击
 */
function 查找文本并点击(文本) {
    console.log("查找并点击文本: " + 文本);
    
    if (操作模式 === 1) {
        // 无障碍模式
        let 元素 = text(文本).findOne(3000);
        if (元素) {
            元素.click();
            return true;
        }
        return false;
    } else {
        // Root Shell模式
        return 查找并点击(文本);
    }
}

// 主函数
function main() {
    console.log("===== 开始测试 " + (操作模式 === 1 ? "无障碍" : "Root Shell") + " 模式 =====");
    
    // 打印当前界面的所有文本元素
    if (操作模式 === 2) {
        打印所有文本元素();
    } else {
        console.log("无障碍模式下，使用内置函数获取界面元素");
    }
    
    // 演示基本操作
    console.log("\n===== 演示基本操作 =====");
    
    // 1. 获取当前应用
    let 当前应用 = 获取当前应用();
    console.log("当前应用: " + 当前应用);
    
    // // 2. 演示点击操作
    // console.log("\n点击屏幕中心");
    // 点击(device.width / 2, device.height / 2);
    // 等待(1000);
    
    // // 3. 演示滑动操作
    // console.log("\n向上滑动");
    // 上滑();
    // 等待(1000);
    
    // console.log("\n向下滑动");
    // 下滑();
    // 等待(1000);
    
    // // 4. 演示返回操作
    // console.log("\n按返回键");
    // 返回();
    // 等待(1000);
    
    // // 5. 如果是无障碍模式，演示查找文本并点击
    // if (操作模式 === 1) {
    //     console.log("\n尝试查找并点击包含'设置'的元素");
    //     查找文本并点击("设置");
    //     等待(2000);
    //     返回();
    // }
    
    // console.log("\n===== 测试完成 =====");
}

/**
 * 常用操控函数封装 - 使用 shell 命令替代无障碍服务
 * 以下函数都使用 root 权限执行 shell 命令
 */

/**
 * 返回键操作
 * @returns {boolean} 是否执行成功
 */
function 返回() {
    console.log("执行返回操作");
    
    if (操作模式 === 1) {
        // 无障碍模式
        back();
        return true;
    } else {
        // Root Shell模式
        let result = shell("su -c 'input keyevent 4'", true); // KEYCODE_BACK = 4
        let 成功 = result.code === 0;
        console.log(成功 ? "返回操作成功" : "返回操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 回到主页
 * @returns {boolean} 是否执行成功
 */
function 主页() {
    console.log("执行回到主页操作");
    
    if (操作模式 === 1) {
        // 无障碍模式
        home();
        return true;
    } else {
        // Root Shell模式
        let result = shell("su -c 'input keyevent 3'", true); // KEYCODE_HOME = 3
        let 成功 = result.code === 0;
        console.log(成功 ? "主页操作成功" : "主页操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 打开最近任务
 * @returns {boolean} 是否执行成功
 */
function 最近任务() {
    console.log("执行打开最近任务操作");
    
    if (操作模式 === 1) {
        // 无障碍模式
        recents();
        return true;
    } else {
        // Root Shell模式
        let result = shell("su -c 'input keyevent 187'", true); // KEYCODE_APP_SWITCH = 187
        let 成功 = result.code === 0;
        console.log(成功 ? "最近任务操作成功" : "最近任务操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 点击屏幕
 * @param {number} x - X 坐标
 * @param {number} y - Y 坐标
 * @returns {boolean} 是否执行成功
 */
function 点击(x, y) {
    console.log("执行点击操作: (" + x + ", " + y + ")");
    
    if (操作模式 === 1) {
        // 无障碍模式
        click(x, y);
        return true;
    } else {
        // Root Shell模式
        let result = shell("su -c 'input tap " + x + " " + y + "'", true);
        let 成功 = result.code === 0;
        console.log(成功 ? "点击操作成功" : "点击操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 长按屏幕
 * @param {number} x - X 坐标
 * @param {number} y - Y 坐标
 * @param {number} 时长 - 长按时长(毫秒)，默认1000ms
 * @returns {boolean} 是否执行成功
 */
function 长按(x, y, 时长 = 1000) {
    console.log("执行长按操作: (" + x + ", " + y + "), 时长: " + 时长 + "ms");
    
    if (操作模式 === 1) {
        // 无障碍模式
        press(x, y, 时长);
        return true;
    } else {
        // Root Shell模式 - 使用 swipe 命令在同一位置停留来模拟长按
        let result = shell("su -c 'input swipe " + x + " " + y + " " + x + " " + y + " " + 时长 + "'", true);
        let 成功 = result.code === 0;
        console.log(成功 ? "长按操作成功" : "长按操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 滑动屏幕
 * @param {number} 起点x - 起点X坐标
 * @param {number} 起点y - 起点Y坐标
 * @param {number} 终点x - 终点X坐标
 * @param {number} 终点y - 终点Y坐标
 * @param {number} 时长 - 滑动时长(毫秒)，默认500ms
 * @returns {boolean} 是否执行成功
 */
function 滑动(起点x, 起点y, 终点x, 终点y, 时长 = 500) {
    console.log("执行滑动操作: (" + 起点x + ", " + 起点y + ") -> (" + 终点x + ", " + 终点y + "), 时长: " + 时长 + "ms");
    
    if (操作模式 === 1) {
        // 无障碍模式
        swipe(起点x, 起点y, 终点x, 终点y, 时长);
        return true;
    } else {
        // Root Shell模式
        let result = shell("su -c 'input swipe " + 起点x + " " + 起点y + " " + 终点x + " " + 终点y + " " + 时长 + "'", true);
        let 成功 = result.code === 0;
        console.log(成功 ? "滑动操作成功" : "滑动操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 上滑屏幕
 * @param {number} 距离 - 滑动距离，默认屏幕高度的1/3
 * @param {number} 时长 - 滑动时长(毫秒)，默认500ms
 * @returns {boolean} 是否执行成功
 */
function 上滑(距离 = null, 时长 = 500) {
    let 屏幕宽度 = device.width;
    let 屏幕高度 = device.height;
    
    // 如果未指定距离，默认为屏幕高度的1/3
    距离 = 距离 || Math.floor(屏幕高度 / 3);
    
    let 起点x = Math.floor(屏幕宽度 / 2);
    let 起点y = Math.floor(屏幕高度 * 0.7);
    let 终点y = 起点y - 距离;
    
    return 滑动(起点x, 起点y, 起点x, 终点y, 时长);
}

/**
 * 下滑屏幕
 * @param {number} 距离 - 滑动距离，默认屏幕高度的1/3
 * @param {number} 时长 - 滑动时长(毫秒)，默认500ms
 * @returns {boolean} 是否执行成功
 */
function 下滑(距离 = null, 时长 = 500) {
    let 屏幕宽度 = device.width;
    let 屏幕高度 = device.height;
    
    // 如果未指定距离，默认为屏幕高度的1/3
    距离 = 距离 || Math.floor(屏幕高度 / 3);
    
    let 起点x = Math.floor(屏幕宽度 / 2);
    let 起点y = Math.floor(屏幕高度 * 0.3);
    let 终点y = 起点y + 距离;
    
    return 滑动(起点x, 起点y, 起点x, 终点y, 时长);
}

/**
 * 左滑屏幕
 * @param {number} 距离 - 滑动距离，默认屏幕宽度的1/3
 * @param {number} 时长 - 滑动时长(毫秒)，默认500ms
 * @returns {boolean} 是否执行成功
 */
function 左滑(距离 = null, 时长 = 500) {
    let 屏幕宽度 = device.width;
    let 屏幕高度 = device.height;
    
    // 如果未指定距离，默认为屏幕宽度的1/3
    距离 = 距离 || Math.floor(屏幕宽度 / 3);
    
    let 起点y = Math.floor(屏幕高度 / 2);
    let 起点x = Math.floor(屏幕宽度 * 0.7);
    let 终点x = 起点x - 距离;
    
    return 滑动(起点x, 起点y, 终点x, 起点y, 时长);
}

/**
 * 右滑屏幕
 * @param {number} 距离 - 滑动距离，默认屏幕宽度的1/3
 * @param {number} 时长 - 滑动时长(毫秒)，默认500ms
 * @returns {boolean} 是否执行成功
 */
function 右滑(距离 = null, 时长 = 500) {
    let 屏幕宽度 = device.width;
    let 屏幕高度 = device.height;
    
    // 如果未指定距离，默认为屏幕宽度的1/3
    距离 = 距离 || Math.floor(屏幕宽度 / 3);
    
    let 起点y = Math.floor(屏幕高度 / 2);
    let 起点x = Math.floor(屏幕宽度 * 0.3);
    let 终点x = 起点x + 距离;
    
    return 滑动(起点x, 起点y, 终点x, 起点y, 时长);
}

/**
 * 输入文本
 * @param {string} 文本 - 要输入的文本
 * @returns {boolean} 是否执行成功
 */
function 输入文本(文本) {
    console.log("执行输入文本操作: " + 文本);
    
    if (操作模式 === 1) {
        // 无障碍模式
        input(文本);
        return true;
    } else {
        // Root Shell模式
        let result = shell("su -c 'input text \"" + 文本.replace(/"/g, '\\"') + "\"'", true);
        let 成功 = result.code === 0;
        console.log(成功 ? "输入文本成功" : "输入文本失败: " + result.error);
        return 成功;
    }
}

/**
 * 按下按键
 * @param {number} 按键码 - 按键的keycode
 * @returns {boolean} 是否执行成功
 */
function 按键(按键码) {
    console.log("执行按键操作: " + 按键码);
    
    if (操作模式 === 1) {
        // 无障碍模式
        keycode(按键码);
        return true;
    } else {
        // Root Shell模式
        let result = shell("su -c 'input keyevent " + 按键码 + "'", true);
        let 成功 = result.code === 0;
        console.log(成功 ? "按键操作成功" : "按键操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 常用按键码
 */
const 按键码 = {
    返回: 4,      // KEYCODE_BACK
    主页: 3,      // KEYCODE_HOME
    菜单: 82,     // KEYCODE_MENU
    搜索: 84,     // KEYCODE_SEARCH
    电源: 26,     // KEYCODE_POWER
    相机: 27,     // KEYCODE_CAMERA
    最近任务: 187, // KEYCODE_APP_SWITCH
    音量加: 24,   // KEYCODE_VOLUME_UP
    音量减: 25,   // KEYCODE_VOLUME_DOWN
    静音: 164,    // KEYCODE_VOLUME_MUTE
    亮度加: 221,  // KEYCODE_BRIGHTNESS_UP
    亮度减: 220,  // KEYCODE_BRIGHTNESS_DOWN
    上: 19,       // KEYCODE_DPAD_UP
    下: 20,       // KEYCODE_DPAD_DOWN
    左: 21,       // KEYCODE_DPAD_LEFT
    右: 22,       // KEYCODE_DPAD_RIGHT
    确定: 23,     // KEYCODE_DPAD_CENTER
    通话: 5,      // KEYCODE_CALL
    挂断: 6,      // KEYCODE_ENDCALL
    锁屏: 223     // KEYCODE_SLEEP
};

/**
 * 启动应用
 * @param {string} 包名 - 应用包名
 * @returns {boolean} 是否执行成功
 */
function 启动应用(包名) {
    console.log("启动应用: " + 包名);
    
    if (操作模式 === 1) {
        // 无障碍模式
        app.launch(包名);
        return true;
    } else {
        // Root Shell模式
        let result = shell("su -c 'am start -n " + 包名 + "/.MainActivity'", true);
        // 如果上面的命令失败，尝试使用更通用的方式
        if (result.code !== 0) {
            result = shell("su -c 'monkey -p " + 包名 + " -c android.intent.category.LAUNCHER 1'", true);
        }
        let 成功 = result.code === 0;
        console.log(成功 ? "启动应用成功" : "启动应用失败: " + result.error);
        return 成功;
    }
}

/**
 * 关闭应用
 * @param {string} 包名 - 应用包名
 * @returns {boolean} 是否执行成功
 */
function 关闭应用(包名) {
    console.log("关闭应用: " + 包名);
    
    if (操作模式 === 1) {
        // 无障碍模式
        app.openAppSetting(包名);
        sleep(1000);
        let 强行停止 = text("强行停止").findOne(2000);
        if (强行停止) {
            强行停止.click();
            sleep(1000);
            let 确认 = text("确定").findOne(2000) || 
                      text("确认").findOne() || 
                      text("强行停止").findOne();
            if (确认) {
                确认.click();
                sleep(1000);
                back();
                return true;
            }
        }
        back();
        return false;
    } else {
        // Root Shell模式
        let result = shell("su -c 'am force-stop " + 包名 + "'", true);
        let 成功 = result.code === 0;
        console.log(成功 ? "关闭应用成功" : "关闭应用失败: " + result.error);
        return 成功;
    }
}

/**
 * 获取当前应用包名
 * @returns {string} 当前应用包名
 */
function 获取当前应用() {
    console.log("获取当前应用包名");
    
    if (操作模式 === 1) {
        // 无障碍模式
        let 包名 = currentPackage();
        console.log("当前应用包名: " + 包名);
        return 包名;
    } else {
        // Root Shell模式
        let result = shell("su -c 'dumpsys window | grep mCurrentFocus'", true);
        if (result.code === 0) {
            let match = result.result.match(/mCurrentFocus.+?{.+?(\S+)\/(\S+)}/);
            if (match) {
                console.log("当前应用包名: " + match[1]);
                return match[1];
            }
        }
        console.log("获取当前应用包名失败");
        return null;
    }
}

/**
 * 等待指定时间
 * @param {number} 毫秒 - 等待时间(毫秒)
 */
function 等待(毫秒) {
    console.log("等待 " + 毫秒 + " 毫秒");
    sleep(毫秒);
}

/**
 * 检查应用是否安装
 * @param {string} 包名 - 应用包名
 * @returns {boolean} 是否已安装
 */
function 应用已安装(包名) {
    console.log("检查应用是否安装: " + 包名);
    let result = shell("su -c 'pm list packages | grep " + 包名 + "'", true);
    let 已安装 = result.code === 0 && result.result.includes(包名);
    console.log(已安装 ? "应用已安装" : "应用未安装");
    return 已安装;
}

/**
 * 标记核心元素
 * @param {Array} 元素列表 - 元素数组
 */
function 标记核心元素(元素列表) {
    let 屏幕高度 = device.height;
    let 屏幕宽度 = device.width;

    // 1. 找顶部用户名（图文）
    let 顶部用户名 = null;
    for (let 元素 of 元素列表) {
        if (
            元素.坐标.中心Y < 屏幕高度 * 0.3 &&
            元素.文本.length >= 2 && 元素.文本.length <= 12 &&
            !/^\d+$/.test(元素.文本) &&
            !["关注", "说点什么...", "评论", "发弹幕", "分享"].some(k => 元素.文本.includes(k))
        ) {
            顶部用户名 = 元素;
            break;
        }
    }

    // 只在视频时找底部昵称和互动数
    let 底部用户名 = null;
    let 点赞 = null, 收藏 = null, 评论 = null;

    if (!顶部用户名) {
        // 1. 找"关注"或"已关注"按钮
        let 关注按钮 = 元素列表.find(e =>
            e.坐标.中心Y > 屏幕高度 * 0.5 &&
            (e.文本 === "关注" || e.文本 === "已关注")
        );

        // 2. 昵称候选
        let 昵称候选 = 元素列表.filter(e =>
            e.坐标.中心Y > 屏幕高度 * 0.5 &&
            e.文本.length >= 2 && e.文本.length <= 12 &&
            !/^[\d.]+万?$/.test(e.文本) &&
            !["关注", "说点什么...", "评论", "发弹幕", "分享", "相关搜索", "课程咨询"].some(k => e.文本.includes(k)) &&
            !e.文本.includes(":")
        );

        if (昵称候选.length > 0) {
            if (关注按钮) {
                // 只选X在关注按钮左侧的，且Y最接近
                let 左侧候选 = 昵称候选
                    .filter(e => e.坐标.中心X < 关注按钮.坐标.中心X)
                    .sort((a, b) => {
                        // Y越接近关注按钮越优先
                        let dyA = Math.abs(a.坐标.中心Y - 关注按钮.坐标.中心Y);
                        let dyB = Math.abs(b.坐标.中心Y - 关注按钮.坐标.中心Y);
                        if (dyA !== dyB) return dyA - dyB;
                        // X越靠近关注按钮越优先
                        return 关注按钮.坐标.中心X - a.坐标.中心X - (关注按钮.坐标.中心X - b.坐标.中心X);
                    });
                if (左侧候选.length > 0) {
                    底部用户名 = 左侧候选[0];
                } else {
                    // 没有左侧的，退回原有Y最大X最小
                    昵称候选.sort((a, b) => {
                        if (b.坐标.中心Y !== a.坐标.中心Y) {
                            return b.坐标.中心Y - a.坐标.中心Y;
                        }
                        return a.坐标.中心X - b.坐标.中心X;
                    });
                    底部用户名 = 昵称候选[0];
                }
            } else {
                // 没有关注按钮，退回原有Y最大X最小
                昵称候选.sort((a, b) => {
                    if (b.坐标.中心Y !== a.坐标.中心Y) {
                        return b.坐标.中心Y - a.坐标.中心Y;
                    }
                    return a.坐标.中心X - b.坐标.中心X;
                });
                底部用户名 = 昵称候选[0];
            }
        }

        // 3. 点赞/收藏/评论数：底部剩下的数字（含"万"）
        let 底部元素 = 元素列表.filter(e => e.坐标.中心Y > 屏幕高度 * 0.7);
        let 互动数字 = 底部元素.filter(e =>
            /^[\d.]+万?$/.test(e.文本)
        );
        // 排除昵称
        if (底部用户名) {
            互动数字 = 互动数字.filter(e => e.文本 !== 底部用户名.文本);
        }
        // 按X坐标排序
        互动数字.sort((a, b) => a.坐标.中心X - b.坐标.中心X);
        点赞 = 互动数字[0] || null;
        收藏 = 互动数字[1] || null;
        评论 = 互动数字[2] || null;
    } else {
        // 图文页底部数字（精准：说点什么...右侧依次为点赞/收藏/评论）
        let 说点什么 = 元素列表.find(e =>
            e.文本 === "说点什么..." && e.坐标.中心Y > 屏幕高度 * 0.8
        );
        let 分界X = 说点什么 ? 说点什么.坐标.中心X : 0;
        let 底部数字 = 元素列表.filter(e =>
            e.坐标.中心Y > 屏幕高度 * 0.8 &&
            (/^[\d.]+万$/.test(e.文本) || /^\d+$/.test(e.文本) || ["点赞", "收藏", "评论", "赞"].includes(e.文本)) &&
            e.坐标.中心X > 分界X
        );
        底部数字.sort((a, b) => a.坐标.中心X - b.坐标.中心X);
        点赞 = 底部数字[0] || null;
        收藏 = 底部数字[1] || null;
        评论 = 底部数字[2] || null;
        // 如果为文本，视为0
        if (点赞 && (点赞.文本 === "点赞" || 点赞.文本 === "赞")) 点赞.文本 = "0";
        if (收藏 && 收藏.文本 === "收藏") 收藏.文本 = "0";
        if (评论 && 评论.文本 === "评论") 评论.文本 = "0";
    }

    // 打印标记结果
    if (顶部用户名) {
        打印元素详细信息(顶部用户名, "图文用户名");
    }
    if (底部用户名) {
        打印元素详细信息(底部用户名, "用户昵称");
    }
    if (点赞) {
        打印元素详细信息(点赞, "点赞数");
    }
    if (收藏) {
        打印元素详细信息(收藏, "收藏数");
    }
    if (评论) {
        打印元素详细信息(评论, "评论数");
    }
    console.log(`内容类型: ${顶部用户名 ? "图文" : "视频"}`);
}

function 打印元素详细信息(元素, 标签) {
    if (!元素) return;
    let 属性文本 = '';
    if (元素.原始属性) {
        for (let k in 元素.原始属性) {
            属性文本 += `${k}: ${元素.原始属性[k]}, `;
        }
        属性文本 = 属性文本.replace(/, $/, '');
    }
    console.log(`${标签}: [${元素.文本}], 坐标: (${元素.坐标.中心X}, ${元素.坐标.中心Y})${属性文本 ? ' | ' + 属性文本 : ''}`);
}

// 执行主函数
main();
