// 声明使用UI模式
"ui";

// 在文件开头添加一个标志，表示是否是首次运行
let 是否首次运行 = true;

// 检查AutoJS环境
if (typeof auto === 'undefined') {
    throw new Error("请在AutoJS环境中运行此脚本");
}




/**
 * 从文本中提取数字
 * 优化版本：提高提取效率，减少日志输出
 * 
 * @param {string} text - 包含数字的文本
 * @returns {number|null} - 提取的数字，失败返回null
 */
function 提取数字(text) {
    if (!text) return null;
    
    try {
        // 先移除所有空格，确保能处理"点赞 1445"这种格式
        let cleanText = text.replace(/\s+/g, "");
        
        // 匹配数字部分（包括小数和千分位）
        let 数字匹配 = cleanText.match(/\d+(\.\d+)?/);
        if (数字匹配) {
            // 转换为数字
            let 提取结果 = parseFloat(数字匹配[0]);
            return 提取结果;
        }
        
        // 如果是纯数字文本，直接解析
        if (/^\d+$/.test(text)) {
            let 提取结果 = parseInt(text);
            return 提取结果;
        }
        
        return null;
    } catch (e) {
        return null;
    }
}

/**
 * 获取互动元素（点赞、收藏、评论）
 * 通过查找Button元素的desc属性获取互动信息
 * 优化版本：提高查询效率，减少日志输出
 * 
 * @returns {Object|null} - 包含点赞、收藏、评论信息的对象，获取失败返回null
 */
function 获取互动元素() {
    try {
        // 初始化返回结果
        let 结果 = {
            点赞: null,
            收藏: null,
            评论: null,
            点赞元素: null,
            收藏元素: null,
            评论元素: null,
            是否视频: false
        };
        
        // 获取屏幕尺寸
        let 屏幕宽度 = device.width;
        let 屏幕高度 = device.height;
        
        // 定义视频互动区域（通常在屏幕右侧）
        let 视频互动区域 = {
            left: Math.floor(屏幕宽度 * 0.7),  // 屏幕右侧30%区域
            top: Math.floor(屏幕高度 * 0.3),   // 从屏幕30%位置开始
            right: 屏幕宽度,
            bottom: 屏幕高度 * 0.8             // 到屏幕80%位置结束
        };
        
        // 定义普通互动区域（通常在屏幕下半部分）
        let 普通互动区域 = {
            left: 0,
            top: Math.floor(屏幕高度 * 0.5),  // 从屏幕中间开始
            right: 屏幕宽度,
            bottom: 屏幕高度
        };
        
        // 首先检查是否为视频页面 - 使用更高效的方法
        let 视频区域按钮 = className("android.widget.Button")
            .boundsInside(视频互动区域.left, 视频互动区域.top, 视频互动区域.right, 视频互动区域.bottom)
            .find();
        
        let 是否视频页面 = 视频区域按钮.length > 0;
        结果.是否视频 = 是否视频页面;
        
        // 根据页面类型选择互动区域
        let 互动区域 = 是否视频页面 ? 视频互动区域 : 普通互动区域;
        
        // 使用更精确的选择器直接查找互动元素
        // 1. 查找点赞元素
        let 点赞元素 = className("android.widget.Button")
            .boundsInside(互动区域.left, 互动区域.top, 互动区域.right, 互动区域.bottom)
            .descMatches(".*点赞.*")
            .findOne(500);
        
        if (点赞元素) {
            结果.点赞元素 = 点赞元素;
            let desc = 点赞元素.desc();
            结果.点赞 = 提取数字(desc);
        }
        
        // 2. 查找收藏元素
        let 收藏元素 = className("android.widget.Button")
            .boundsInside(互动区域.left, 互动区域.top, 互动区域.right, 互动区域.bottom)
            .descMatches(".*收藏.*")
            .findOne(500);
        
        if (收藏元素) {
            结果.收藏元素 = 收藏元素;
            let desc = 收藏元素.desc();
            结果.收藏 = 提取数字(desc);
        }
        
        // 3. 查找评论元素
        let 评论元素 = className("android.widget.Button")
            .boundsInside(互动区域.left, 互动区域.top, 互动区域.right, 互动区域.bottom)
            .descMatches(".*评论.*")
            .findOne(500);
        
        if (评论元素) {
            结果.评论元素 = 评论元素;
            let desc = 评论元素.desc();
            结果.评论 = 提取数字(desc);
        }
        
        // 如果在指定区域没有找到互动元素，尝试扩大搜索范围
        if (!结果.点赞元素 && !结果.收藏元素 && !结果.评论元素) {
            // 尝试在整个屏幕查找
            点赞元素 = className("android.widget.Button").descMatches(".*点赞.*").findOne(500);
            收藏元素 = className("android.widget.Button").descMatches(".*收藏.*").findOne(500);
            评论元素 = className("android.widget.Button").descMatches(".*评论.*").findOne(500);
            
            if (点赞元素) {
                结果.点赞元素 = 点赞元素;
                结果.点赞 = 提取数字(点赞元素.desc());
            }
            
            if (收藏元素) {
                结果.收藏元素 = 收藏元素;
                结果.收藏 = 提取数字(收藏元素.desc());
            }
            
            if (评论元素) {
                结果.评论元素 = 评论元素;
                结果.评论 = 提取数字(评论元素.desc());
            }
        }
        
        // 如果仍然没有找到互动元素，尝试使用text属性查找
        if (!结果.点赞元素 && !结果.收藏元素 && !结果.评论元素) {
            // 查找可能包含点赞、收藏、评论的文本
            let 点赞文本 = textMatches(".*点赞.*").findOne(300);
            let 收藏文本 = textMatches(".*收藏.*").findOne(300);
            let 评论文本 = textMatches(".*评论.*").findOne(300);
            
            if (点赞文本) {
                结果.点赞 = 提取数字(点赞文本.text());
            }
            
            if (收藏文本) {
                结果.收藏 = 提取数字(收藏文本.text());
            }
            
            if (评论文本) {
                结果.评论 = 提取数字(评论文本.text());
            }
        }
        
        // 视频页面特殊处理：如果没有找到点赞数，尝试查找纯数字文本
        if (是否视频页面 && 结果.点赞 === null) {
            let 可能的点赞数字 = textMatches("^\\d+$")
                .boundsInside(视频互动区域.left, 视频互动区域.top, 视频互动区域.right, 视频互动区域.bottom)
                .find();
            
            // 通常第一个纯数字是点赞数
            if (可能的点赞数字.length > 0) {
                for (let i = 0; i < 可能的点赞数字.length; i++) {
                    let 元素 = 可能的点赞数字[i];
                    let 文本 = 元素.text();
                    
                    // 尝试解析为数字
                    let 点赞数 = parseInt(文本);
                    if (!isNaN(点赞数)) {
                        结果.点赞 = 点赞数;
                        break;
                    }
                }
            }
        }
        
        // 检查是否找到任何互动元素
        if (结果.点赞 !== null || 结果.收藏 !== null || 结果.评论 !== null) {
            return 结果;
        }
        
        return null;
    } catch (e) {
        return null;
    }
}

/**
 * 全局变量，存储互动元素信息
 */
let 互动元素信息 = {
    点赞: null,
    收藏: null,
    评论: null,
    点赞元素: null,
    收藏元素: null,
    评论元素: null
};

/**
 * 等待指定时间
 * 
 * @param {number} 毫秒 - 等待的毫秒数
 */
function 等待(毫秒) {
    sleep(毫秒);
}

/**
 * 随机等待一段时间
 * 
 * @param {number} 最小毫秒 - 最小等待时间
 * @param {number} 最大毫秒 - 最大等待时间
 */
function 随机等待(最小毫秒, 最大毫秒) {
    let 等待时间 = 最小毫秒 + Math.floor(Math.random() * (最大毫秒 - 最小毫秒));
    console.log("随机等待 " + 等待时间 + " 毫秒");
    等待(等待时间);
}

/**
 * 处理权限弹窗
 * 检查并点击各种常见的权限按钮
 * 
 * @returns {boolean} - 是否找到并点击了权限按钮
 */
function 处理权限弹窗() {
    console.log("检查权限弹窗...");
    
    // 常见的权限按钮文本
    const 权限按钮文本列表 = ["允许", "始终允许", "确定", "继续", "同意", "确认", "好的"];
    
    // 遍历所有可能的按钮文本
    for (let i = 0; i < 权限按钮文本列表.length; i++) {
        let 按钮文本 = 权限按钮文本列表[i];
        console.log(`查找按钮: ${按钮文本}`);
        
        // 查找按钮
        let 按钮 = text(按钮文本).findOne(1000);
        if (按钮 && 按钮.clickable()) {
            console.log(`找到按钮 "${按钮文本}"，点击`);
            按钮.click();
            sleep(500);
            return true;
        }
    }
    
    // 如果没有找到可点击的文本按钮，尝试查找包含这些文本的任何元素
    for (let i = 0; i < 权限按钮文本列表.length; i++) {
        let 按钮文本 = 权限按钮文本列表[i];
        let 元素 = textContains(按钮文本).findOne(500);
        if (元素) {
            console.log(`找到包含 "${按钮文本}" 的元素，尝试点击`);
            let 成功 = 元素.click();
            if (!成功) {
                // 尝试点击元素中心位置
                let 位置 = 元素.bounds();
                if (位置) {
                    click(位置.centerX(), 位置.centerY());
                    console.log(`通过坐标点击: (${位置.centerX()}, ${位置.centerY()})`);
                }
            }
            sleep(500);
            return true;
        }
    }
    
    console.log("未找到权限弹窗");
    return false;
}

/**
 * 检查账号异常提示
 * 检测是否出现需要更换账号的提示，如账号下线、登录过期等
 * 优化版本：减少查询次数，提高执行效率
 * 
 * @returns {boolean} - 是否检测到账号异常提示
 */
function 检查账号异常提示() {
    console.log("检查账号异常提示...");
    
    try {
        // 一次性获取页面上所有文本元素，避免多次查询
        let 所有文本元素 = textMatches(".*").find();
        console.log(`找到 ${所有文本元素.length} 个文本元素`);
        
        // 常见的账号异常关键词
        const 异常关键词 = [
            "账号下线", "下线提示", "登录过期", "请重新登录", 
            "其他设备登录", "账号已被冻结", "账号异常", "违反社区规定"
        ];
        
        // 登录页面关键词
        const 登录页面关键词 = [
            "手机号登录", "用户协议", "隐私政策", "一键登录", "注册"
        ];
        
        // 检查所有文本元素是否包含异常关键词
        for (let i = 0; i < 所有文本元素.length; i++) {
            let 文本内容 = 所有文本元素[i].text();
            if (!文本内容) continue;
            
            // 检查账号异常关键词
            for (let j = 0; j < 异常关键词.length; j++) {
                if (文本内容.includes(异常关键词[j])) {
                    console.log(`检测到账号异常提示: "${文本内容}" 包含关键词 "${异常关键词[j]}"`);
                    return true;
                }
            }
            
            // 检查登录页面关键词
            for (let j = 0; j < 登录页面关键词.length; j++) {
                if (文本内容.includes(登录页面关键词[j])) {
                    console.log(`检测到登录页面: "${文本内容}" 包含关键词 "${登录页面关键词[j]}"`);
                    return true;
                }
            }
        }
        
        // 检查是否同时存在"小红书"和"生活指南"文本，这是登录页面的特征
        let 有小红书文本 = false;
        let 有生活指南文本 = false;
        
        for (let i = 0; i < 所有文本元素.length; i++) {
            let 文本内容 = 所有文本元素[i].text();
            if (!文本内容) continue;
            
            if (文本内容.includes("小红书")) {
                有小红书文本 = true;
            }
            if (文本内容.includes("生活指南")) {
                有生活指南文本 = true;
            }
            
            // 如果两者都找到了，可以提前返回
            if (有小红书文本 && 有生活指南文本) {
                console.log("检测到登录页面: 同时存在'小红书'和'生活指南'文本");
                return true;
            }
        }
        
        // 检查"知道了"按钮特殊情况
        let 知道了按钮 = false;
        for (let i = 0; i < 所有文本元素.length; i++) {
            if (所有文本元素[i].text() === "知道了") {
                知道了按钮 = true;
                break;
            }
        }
        
        if (知道了按钮) {
            console.log("检测到'知道了'按钮，检查是否有账号异常提示");
            
            // 再次遍历文本元素，查找可能的异常提示
            for (let i = 0; i < 所有文本元素.length; i++) {
                let 文本内容 = 所有文本元素[i].text();
                if (!文本内容 || 文本内容 === "知道了") continue;
                
                // 检查是否包含下线、登录、账号等关键词
                if (文本内容.includes("下线") || 
                    文本内容.includes("登录") || 
                    文本内容.includes("账号") ||
                    文本内容.includes("异常")) {
                    console.log(`检测到可能的账号异常提示: "${文本内容}"`);
                    return true;
                }
            }
        }
        
        // 如果上述检查都未发现异常，则认为没有账号异常
        console.log("未检测到账号异常提示");
        return false;
    } catch (e) {
        console.error("检查账号异常提示出错: " + e.message);
        // 出错时返回false，避免误判
        return false;
    }
}

// 在适当的位置调用检查账号异常提示函数
// 例如，可以在打开小红书、打开链接后调用

/**
 * 打开小红书应用
 */
function 打开小红书() {
    console.log("打开小红书应用");
    let 应用包名 = "com.xingin.xhs";
    
    try {
        app.launch(应用包名);
        等待(5000); // 等待应用启动
        
        // 处理可能出现的权限弹窗
        处理权限弹窗();
        
        // 检查账号异常提示
        // if (检查账号异常提示()) {
        //     console.log("检测到账号异常提示，需要更换账号，结束当前操作");
        //     toast("检测到账号异常，需要更换账号");
        //     return false; // 直接返回失败，中断操作
        // }
        
        console.log("小红书应用已启动");
        return true;
    } catch (e) {
        console.error("打开小红书失败: " + e.message);
        return false;
    }
}

/**
 * 返回主界面
 * 多次按返回键，直到回到主界面
 * 优化版：减少查询次数和等待时间
 * 
 * @returns {boolean} - 是否成功返回主界面
 */
function 返回主界面() {
    console.log("返回主界面");
    
    // 首页可能出现的关键词
    const 首页关键词 = ["首页", "发现", "关注", "直播", "短剧", "附近", "热门", "消息", "我", "推荐", "穿搭", "情感"];
    
    // 最多按5次返回键
    for (let i = 0; i < 5; i++) {
        // 检查是否已在主界面
        let 找到的关键词 = 0;
        
        try {
            // 查找所有文本元素，减少超时时间
            let 所有文本 = textMatches(".*").find();
            
            // 只记录找到的元素数量，不输出日志
            // console.log(`找到 ${所有文本.length} 个文本元素`);
            
            // 检查有多少关键词存在
            for (let j = 0; j < 所有文本.length; j++) {
                let 文本内容 = 所有文本[j].text();
                if (!文本内容) continue;
                
                // 使用some方法更高效地检查是否包含任何关键词
                if (首页关键词.some(关键词 => 文本内容.includes(关键词))) {
                    找到的关键词++;
                    
                    // 减少日志输出
                    // console.log(`找到关键词: ${文本内容}`);
                    
                    // 如果找到3个或以上关键词，认为已在首页（降低标准以加快判断）
                    if (找到的关键词 >= 3) {
                        console.log(`已找到 ${找到的关键词} 个首页关键词，确认在首页`);
                        return true;
                    }
                }
            }
        } catch (e) {
            console.error("检查首页元素出错: " + e.message);
        }
        
        // 按返回键
        console.log("按返回键");
        back();
        等待(1500);
    }
    
    // 最后检查一次
    let 找到的关键词 = 0;
    let 所有文本 = textMatches(".*").find();
    for (let j = 0; j < 所有文本.length; j++) {
        let 文本内容 = 所有文本[j].text();
        if (!文本内容) continue;
        
        if (首页关键词.some(关键词 => 文本内容.includes(关键词))) {
            找到的关键词++;
        }
    }
    
    if (找到的关键词 >= 5) {
        console.log(`已找到 ${找到的关键词} 个首页关键词，确认在首页`);
        return true;
    }
    
    console.log("未能确认返回首页");
    return false;
}

/**
 * 点击首篇文章
 * 
 * @returns {boolean} - 是否成功点击
 */
function 点击首篇文章() {
    console.log("点击首页文章");
    
    try {
        // 等待首页加载
        等待(3000);
        
        // 随机化点击坐标
        let x = 250 + Math.floor(Math.random() * 40) - 20;  // 250 ± 20
        let y = 500 + Math.floor(Math.random() * 40) - 20;  // 500 ± 20
        
        console.log(`点击坐标: (${x}, ${y})`);
        click(x, y);
        
        // 等待文章加载
        等待(5000);
        
        return true;
    } catch (e) {
        console.error("点击首篇文章出错: " + e.message);
        return false;
    }
}

/**
 * 打开链接
 * 
 * @param {string} 链接 - 要打开的链接
 * @returns {boolean} - 是否成功打开
 */
function 打开链接(链接) {
    console.log("打开链接: " + 链接);
    
    try {
        // 解析链接获取笔记ID
/*         let noteId = 提取笔记ID(链接);
        if (!noteId) {
            console.error("无法从链接中提取笔记ID");
            return false;
        }
        
        console.log("提取到笔记ID: " + noteId);
        // 使用笔记ID打开小红书
        let 结果 = 小红书打开指定链接(noteId); */
        
        结果=在浏览器检测App内打开(链接)
        return 结果;
    } catch (e) {
        console.error("打开链接出错: " + e.message);
        return false;
    }
}


function 在浏览器检测App内打开(链接) {
    console.log("使用浏览器打开链接: " + 链接);
    app.openUrl(链接);
    toast("已用浏览器打开链接");
    
    // 最大尝试次数和超时时间
    const 最大尝试次数 = 5;
    const 每次尝试间隔 = 1500; // 2秒
    const 总超时时间 = 30000; // 30秒
    
    // 记录开始时间
    const 开始时间 = new Date().getTime();
    let 尝试次数 = 0;
    
    while (尝试次数 < 最大尝试次数 && (new Date().getTime() - 开始时间) < 总超时时间) {
        尝试次数++;
        console.log(`第 ${尝试次数} 次尝试打开小红书...`);
        
        // 等待页面加载
        console.log("等待页面加载...");
        sleep(每次尝试间隔);
        
        // 处理可能出现的权限弹窗
        let 权限处理结果 = 处理权限弹窗();
        if (权限处理结果) {
           // console.log("处理了权限弹窗，继续检查");
        }
        
        // 检查是否已经在小红书App中
        let pkg = typeof currentPackage === "function" ? currentPackage() : "";
        if (pkg === "com.xingin.xhs") {
            //console.log("已成功打开小红书App");
            return true;
        }
        
        // 检查是否有"App内打开"按钮
        console.log("检查是否有'App内打开'按钮...");
        let appButton = text('App内打开').findOne(2000);
        
        if (appButton) {
            console.log("找到'App内打开'按钮，点击");
            appButton.click();
            //toast("已点击'App内打开'按钮");
            
            // 等待小红书App启动
            sleep(3000);
            
            // 再次检查是否已经在小红书App中
            pkg = typeof currentPackage === "function" ? currentPackage() : "";
            if (pkg === "com.xingin.xhs") {
                //console.log("已成功打开小红书App");
                return true;
            }
        } else {
            //console.log("未找到'App内打开'按钮，检查其他可能的按钮...");
            
            // 尝试查找其他可能的按钮文本
            const 可能的按钮文本 = ["打开小红书", "打开APP", "在应用中打开", "打开应用"];
            
            for (let i = 0; i < 可能的按钮文本.length; i++) {
                let 按钮文本 = 可能的按钮文本[i];
                let 其他按钮 = text(按钮文本).findOne(1000);
                
                if (其他按钮) {
                    console.log(`找到'${按钮文本}'按钮，点击`);
                    其他按钮.click();
                    sleep(3000);
                    break;
                }
            }
        }
    }
    
    // 检查最终结果
    let pkg = typeof currentPackage === "function" ? currentPackage() : "";
    if (pkg === "com.xingin.xhs") {
        //console.log("最终确认：已成功打开小红书App");
        return true;
    } else {
        //console.log(`尝试 ${尝试次数} 次后未能自动打开小红书App`);
        toast("请手动点击'App内打开'按钮");
        return false;
    }
}




/**
 * 从URL中提取笔记ID
 * 支持短链接、长链接和直接的笔记ID
 * 
 * @param {string} url - 小红书URL或笔记ID
 * @returns {string|null} - 笔记ID，失败返回null
 */
function 提取笔记ID(url) {
    try {
        console.log("开始提取笔记ID...");
        
        // 确保url是字符串类型
        url = String(url);
        
        // 如果链接为空
        if (!url) {
            console.error("无效的链接");
            return null;
        }
        
        // 情况1: 直接是笔记ID
        if (/^[a-zA-Z0-9_]+$/.test(url) && !url.includes(".")) {
            console.log("检测到笔记ID，直接返回");
            return url;
        }
        
        // 情况2: 小红书长链接
        if (url.indexOf("xiaohongshu.com") !== -1) {
            console.log("检测到小红书长链接，提取ID");
            
            // 直接尝试匹配ID模式（通常是24位字符）
            let idMatch = url.match(/([a-zA-Z0-9]{24})/);
            if (idMatch && idMatch[1]) {
                console.log("通过ID模式匹配成功: " + idMatch[1]);
                return idMatch[1];
            }
            
            // 尝试提取 /explore/[noteId]
            let match = url.match(/\/explore\/([a-zA-Z0-9_]+)/);
            if (match && match[1]) {
                console.log("通过explore路径匹配成功: " + match[1]);
                return match[1];
            }
            
            // 尝试提取 /discovery/item/[noteId]
            match = url.match(/\/discovery\/item\/([a-zA-Z0-9_]+)/);
            if (match && match[1]) {
                // 如果ID包含参数，只取问号前面的部分
                let id = match[1].split('?')[0];
                console.log("通过discovery路径匹配成功: " + id);
                return id;
            }
            
            console.error("未能从长链接提取笔记ID");
            return null;
        }
        
        // 情况3: 小红书短链接，需要先解析
        if (url.indexOf("xhslink.com") !== -1) {
            console.log("检测到小红书短链接，开始解析...");
            
            try {
                // 发送HTTP请求获取重定向URL
                let response = http.get(url, {
                    followRedirect: true,  // 自动跟随重定向
                    headers: {
                        "User-Agent": "Mozilla/5.0 (Linux; Android 10; Redmi Note 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.104 Mobile Safari/537.36"
                    }
                });
                
                // 从最终URL中提取ID
                let finalUrl = response.url;
                console.log("短链接解析结果: " + finalUrl);
                
                // 再次调用提取笔记ID函数处理解析后的URL
                return 提取笔记ID(finalUrl);
            } catch (e) {
                console.error("解析短链接失败: " + e.message);
                return null;
            }
        }
        
        // 尝试直接从URL中提取24位ID格式
        let idMatch = url.match(/([a-zA-Z0-9]{24})/);
        if (idMatch && idMatch[1]) {
            console.log("从URL中直接提取到ID: " + idMatch[1]);
            return idMatch[1];
        }
        
        // 情况4: 其他未识别的链接类型
        console.error("未识别的链接类型，无法提取ID: " + url);
        return null;
    } catch (e) {
        console.error("提取笔记ID出错: " + e.message);
        return null;
    }
}

/**
 * 执行点赞操作
 * 
 * @returns {boolean} - 是否成功点赞
 */
function 执行点赞() {
    console.log("执行点赞操作");
    
    try {
        if (!互动元素信息.点赞元素) {
            console.log("未找到点赞元素");
            return false;
        }
        
        // 检查是否已点赞
        let desc = 互动元素信息.点赞元素.desc();
        if (desc && desc.includes("已点赞")) {
            console.log("已经点过赞了");
            return true;
        }
        
        // 点击点赞按钮
        console.log("点击点赞按钮");
        互动元素信息.点赞元素.click();
        等待(1000);
        
        return true;
    } catch (e) {
        console.error("执行点赞出错: " + e.message);
        return false;
    }
}

/**
 * 执行收藏操作
 * 
 * @returns {boolean} - 是否成功收藏
 */
function 执行收藏() {
    console.log("执行收藏操作");
    
    try {
        if (!互动元素信息.收藏元素) {
            console.log("未找到收藏元素");
            return false;
        }
        
        // 检查是否已收藏
        let desc = 互动元素信息.收藏元素.desc();
        if (desc && desc.includes("已收藏")) {
            console.log("已经收藏过了");
            return true;
        }
        
        // 点击收藏按钮
        console.log("点击收藏按钮");
        互动元素信息.收藏元素.click();
        等待(1000);
        
        return true;
    } catch (e) {
        console.error("执行收藏出错: " + e.message);
        return false;
    }
}

/**
 * 清除存储的设备令牌
 * 强制重新生成设备令牌
 */
function 清除设备令牌() {
    console.log("清除存储的设备令牌");
    storages.create("小红书操作").remove("设备令牌");
    API配置.设备令牌 = "";
    是否首次运行 = true;
}

/**
 * 获取特定配置项的值
 * 
 * @param {string} 配置名称 - 配置项名称
 * @param {any} 默认值 - 如果配置不存在，返回的默认值
 * @returns {any} - 配置项的值
 */
function 获取配置项(配置名称, 默认值) {
    // 先尝试直接从存储中获取
    let storage = storages.create("小红书操作");
    let 配置值 = storage.get(配置名称);
    
    // 如果直接存储中有值，直接返回
    if (配置值 !== undefined && 配置值 !== null) {
        console.log(`获取配置项 ${配置名称}: ${配置值}`);
        return 配置值;
    }
    
    // 尝试从所有配置中获取
    let 所有配置 = storage.get("所有配置");
    if (所有配置) {
        // 遍历所有配置项
        for (let 配置组名 in 所有配置) {
            let 配置组 = 所有配置[配置组名];
            // 如果配置组中包含指定的配置项
            if (配置组 && 配置组[配置名称] !== undefined) {
                console.log(`从配置组 ${配置组名} 中获取配置项 ${配置名称}: ${配置组[配置名称]}`);
                return 配置组[配置名称];
            }
        }
    }
    
    // 尝试从当前配置中获取
    let 当前配置 = storage.get("当前配置");
    if (当前配置 && 当前配置.config && 当前配置.config[配置名称] !== undefined) {
        console.log(`从当前配置中获取配置项 ${配置名称}: ${当前配置.config[配置名称]}`);
        return 当前配置.config[配置名称];
    }
    
    // 如果都没有找到，返回默认值
    console.log(`未找到配置项 ${配置名称}，使用默认值: ${默认值}`);
    return 默认值;
}



/**
 * 获取作者信息
 * 通过查找作者元素获取作者名称
 * 优化版本：提高查询效率，减少日志输出
 * 
 * @returns {string|null} - 作者名称，获取失败返回null
 */
function 获取作者信息() {
    try {
        // 查找包含"作者"的Button元素
        let 作者元素 = className("android.widget.Button").descMatches(".*作者.*").findOne(300);
        
        if (作者元素) {
            let desc = 作者元素.desc();
            
            // 从描述中提取作者名称
            let 作者名称匹配 = desc.match(/作者,(.+)/);
            if (作者名称匹配 && 作者名称匹配[1]) {
                let 作者名称 = 作者名称匹配[1].trim();
                return 作者名称;
            }
        }
        
        // 尝试其他方式查找作者信息
        // 1. 查找包含@的文本
        let 包含艾特的文本 = textMatches("@.*").findOne(300);
        if (包含艾特的文本) {
            return 包含艾特的文本.text();
        }
        
        // 2. 查找可能的作者名称（短文本，通常在页面上方）
        let 屏幕高度 = device.height;
        let 可能的作者元素列表 = className("android.widget.TextView")
            .boundsInside(0, 0, device.width, Math.floor(屏幕高度 * 0.3))
            .find();
        
        // 筛选可能的作者名称
        for (let i = 0; i < 可能的作者元素列表.length; i++) {
            let 元素 = 可能的作者元素列表[i];
            let 文本 = 元素.text();
            
            // 作者名称通常较短，且不包含特殊字符
            if (文本 && 文本.length > 0 && 文本.length < 20 && 
                !文本.includes("点赞") && !文本.includes("收藏") && 
                !文本.includes("评论") && !文本.match(/^\d+$/)) {
                return 文本;
            }
        }
        
        return null;
    } catch (e) {
        return null;
    }
}

/**
 * 启动UI界面
 * 显示设置窗口,允许用户配置链接文件名、最大操作数量和操作间隔时间
 */
function 启动UI界面() {
    // 创建UI布局
    ui.layout(
        <vertical padding="16">
            <text textSize="24sp" textColor="#FF5722" gravity="center" margin="0 0 0 16">小红书自动点赞工具</text>
            
            <text textSize="16sp" textColor="#666666" margin="0 16 0 0">链接文件名 (存放于/mnt/shared/Pictures/)</text>
            <input id="链接文件名" text="links.txt" hint="输入链接文件名" />
            
            <text textSize="16sp" textColor="#666666" margin="0 16 0 0">最大操作数量</text>
            <input id="最大操作数量" text="10" inputType="number" hint="输入最大操作数量" />
            
            <text textSize="16sp" textColor="#666666" margin="0 16 0 0">操作间隔时间 (秒)</text>
            <input id="操作间隔时间" text="5" inputType="number" hint="输入操作间隔时间(秒)" />
            
            <button id="开始按钮" text="开始操作" textColor="#ffffff" bg="#FF5722" margin="0 32 0 0" />
            
            <text id="状态文本" textSize="14sp" textColor="#333333" margin="0 16 0 0" />
        </vertical>
    );
    
    // 设置按钮点击事件
    ui.开始按钮.on("click", function() {
        let 链接文件名 = ui.链接文件名.getText().toString();
        let 最大操作数量 = parseInt(ui.最大操作数量.getText().toString());
        let 操作间隔时间 = parseInt(ui.操作间隔时间.getText().toString()) * 1000; // 转换为毫秒
        
        if(!链接文件名) {
            toast("请输入链接文件名");
            return;
        }
        
        if(isNaN(最大操作数量) || 最大操作数量 <= 0) {
            toast("请输入有效的最大操作数量");
            return;
        }
        
        if(isNaN(操作间隔时间) || 操作间隔时间 <= 0) {
            toast("请输入有效的操作间隔时间");
            return;
        }
        
        // 开始执行自动点赞操作
        threads.start(function() {
            执行自动点赞(链接文件名, 最大操作数量, 操作间隔时间);
        });
    });
}

/**
 * 执行自动点赞操作
 * 
 * @param {string} 链接文件名 - 链接文件名
 * @param {number} 最大操作数量 - 最大操作数量
 * @param {number} 操作间隔时间 - 操作间隔时间(毫秒)
 */
function 执行自动点赞(链接文件名, 最大操作数量, 操作间隔时间) {
    ui.run(() => {
        ui.状态文本.setText("正在准备操作...");
    });
    
    // 读取链接文件
    let 链接列表 = 读取链接文件(链接文件名);
    if(!链接列表 || 链接列表.length === 0) {
        ui.run(() => {
            ui.状态文本.setText("链接文件为空或不存在");
        });
        return;
    }
    
    ui.run(() => {
        ui.状态文本.setText(`读取到${链接列表.length}个链接,准备开始操作...`);
    });
    
    // 打开小红书
    if(!打开小红书()) {
        ui.run(() => {
            ui.状态文本.setText("打开小红书失败");
        });
        return;
    }
    
    // 记录已操作数量
    let 已操作数量 = 0;
    
    // 遍历链接列表
    for(let i = 0; i < 链接列表.length && 已操作数量 < 最大操作数量; i++) {
        let 链接 = 链接列表[i];
        if(!链接) continue;
        
        ui.run(() => {
            ui.状态文本.setText(`正在处理第${i+1}个链接(${已操作数量+1}/${最大操作数量})`);
        });
        
        // 打开链接
        if(打开链接(链接)) {
            // 等待页面加载
            等待(3000);
            
            // 获取互动元素
            互动元素信息 = 获取互动元素();
            if(互动元素信息) {
                // 执行点赞
                执行点赞();
                已操作数量++;
                
                ui.run(() => {
                    ui.状态文本.setText(`成功点赞第${i+1}个链接(${已操作数量}/${最大操作数量})`);
                });
            } else {
                ui.run(() => {
                    ui.状态文本.setText(`无法获取第${i+1}个链接的互动元素`);
                });
            }
            
            // 返回主界面
            返回主界面();
        } else {
            ui.run(() => {
                ui.状态文本.setText(`打开第${i+1}个链接失败`);
            });
        }
        
        // 操作间隔
        随机等待(操作间隔时间, 操作间隔时间 + 2000);
    }
    
    ui.run(() => {
        ui.状态文本.setText(`操作完成,共成功点赞${已操作数量}篇文章`);
    });
}

/**
 * 读取链接文件
 * 
 * @param {string} 文件名 - 链接文件名
 * @returns {Array} - 链接列表
 */
function 读取链接文件(文件名) {
    try {
        // 文件路径
        let 文件路径 = "/mnt/shared/Pictures/" + 文件名;
        
        // 检查文件是否存在
        if(!files.exists(文件路径)) {
            console.error("链接文件不存在: " + 文件路径);
            return [];
        }
        
        // 读取文件内容
        let 文件内容 = files.read(文件路径);
        
        // 按行分割并过滤空行
        let 链接列表 = 文件内容.split("\n")
            .map(行 => 行.trim())
            .filter(行 => 行.length > 0);
        
        console.log(`成功读取${链接列表.length}个链接`);
        return 链接列表;
    } catch(e) {
        console.error("读取链接文件出错: " + e.message);
        return [];
    }
}

// 启动UI界面
console.log("正在启动小红书自动点赞工具...");

// 确保无障碍服务已启用
if(!auto.service) {
    console.log("正在申请无障碍服务权限...");
    auto.waitFor();
}

// 启动UI界面
启动UI界面();

