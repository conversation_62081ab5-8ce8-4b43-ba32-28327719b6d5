"ui";

// 全局变量，用于标记是否已经在运行
let 正在运行 = false;

// 创建UI界面
ui.layout(
    <vertical padding="16">
        <text textSize="24sp" textColor="#FF5722" gravity="center" margin="0 0 0 16">小红书自动点赞工具</text>
        
        <text textSize="16sp" textColor="#666666" margin="0 16 0 0">链接文件路径</text>
        <input id="文件路径" text="/mnt/shared/Pictures/" hint="输入链接文件路径" />
        
        <text textSize="16sp" textColor="#666666" margin="0 16 0 0">链接文件名</text>
        <input id="链接文件名" text="links.txt" hint="输入链接文件名" />
        
        <text textSize="16sp" textColor="#666666" margin="0 16 0 0">最大操作数量</text>
        <input id="最大操作数量" text="10" inputType="number" hint="输入最大操作数量" />
        
        <text textSize="16sp" textColor="#666666" margin="0 16 0 0">操作间隔时间 (秒)</text>
        <input id="操作间隔时间" text="5" inputType="number" hint="输入操作间隔时间(秒)" />
        
        <button id="开始按钮" text="开始操作" textColor="#ffffff" bg="#FF5722" margin="0 32 0 0" />
        
        <text id="状态文本" textSize="14sp" textColor="#333333" margin="0 16 0 0" />
    </vertical>
);

// 点击开始按钮的事件
ui.开始按钮.click(function() {
    // 防止重复点击
    if (正在运行) {
        toast("任务已在运行中，请等待完成");
        return;
    }
    
    正在运行 = true;
    
    // 获取用户输入
    let 文件路径 = ui.文件路径.text();
    let 文件名 = ui.链接文件名.text();
    let 最大操作数 = parseInt(ui.最大操作数量.text());
    let 操作间隔 = parseInt(ui.操作间隔时间.text());
    
    // 更新状态
    ui.状态文本.setText("开始执行，即将隐藏界面...");
    
    // 隐藏界面
    ui.layout(
        <frame>
            <text text="正在后台运行..." textSize="12sp" textColor="#888888" gravity="center"/>
        </frame>
    );
    
    // 创建单个工作线程
    let 工作线程 = threads.start(function() {
        try {
            // 请求截屏权限
            if (!requestScreenCapture()) {
                toast("请求截屏权限失败");
                正在运行 = false;
                return;
            }
            
            // 执行主要功能
            执行自动点赞(文件路径, 文件名, 最大操作数, 操作间隔);
        } catch (e) {
            console.error("执行过程中出错: " + e);
        } finally {
            // 无论如何都要重置运行状态
            正在运行 = false;
        }
    });
});

/**
 * 强制退出小红书应用
 * 使用AutoJS官方API关闭小红书应用，无需root权限
 * @returns {boolean} - 是否成功退出
 */
function 强制退出小红书() {
    console.log("强制退出小红书应用");
    
    try {
        // 方法1: 使用app.killApp方法(AutoJS 6.0.0+)
        if (app.killApp && typeof app.killApp === 'function') {
            let result = app.killApp("com.xingin.xhs");
            console.log("使用app.killApp退出小红书: " + (result ? "成功" : "失败"));
            sleep(1000);
            return result;
        }
        
        // 方法2: 使用app.openAppSetting打开应用设置，然后模拟点击"强行停止"按钮
        console.log("尝试通过应用设置强制停止小红书");
        app.openAppSetting("com.xingin.xhs");
        sleep(1000); // 等待设置页面打开
        
        // 查找"强行停止"按钮并点击
        let 强行停止按钮 = textMatches(/(强.停止|强制.*停止|结束运行|停止运行|force.*stop)/).findOne(2000);
        if (强行停止按钮) {
            强行停止按钮.click();
            sleep(1000);
            
            // 查找确认对话框中的"确定"按钮
            let 确认按钮 = textMatches(/(确定|确认|是|OK|强行停止)/).findOne(2000);
            if (确认按钮) {
                确认按钮.click();
                sleep(1000);
                console.log("已成功强制停止小红书");
                
                // 返回到之前的界面
                back();
                sleep(500);
                return true;
            }
        } else {
            // 如果没找到强行停止按钮，可能应用已经不在运行
            console.log("未找到强行停止按钮，应用可能已经不在运行");
            back(); // 返回到之前的界面
            sleep(500);
            return true;
        }
        
        console.log("无法通过应用设置强制停止小红书");
        // 返回到之前的界面
        back();
        sleep(500);
        return false;
    } catch (e) {
        console.error("强制退出小红书出错: " + e.message);
        // 尝试返回到之前的界面
        back();
        sleep(500);
        return false;
    }
}

/**
 * 执行自动点赞主功能
 * @param {string} 文件路径 - 文件所在路径
 * @param {string} 文件名 - 文件名
 * @param {number} 最大操作数 - 最大操作链接数
 * @param {number} 操作间隔 - 操作间隔时间(秒)
 */
function 执行自动点赞(文件路径, 文件名, 最大操作数, 操作间隔) {
    // 读取链接文件
    let 链接列表 = 读取链接文件(文件路径 + 文件名);
    if (!链接列表 || 链接列表.length === 0) {
        toast("链接文件读取失败或为空");
        return;
    }
    
    // 限制操作数量
    let 实际操作数 = Math.min(链接列表.length, 最大操作数);
    toast("共读取到 " + 链接列表.length + " 条链接，计划处理 " + 实际操作数 + " 条");
    
    // 循环处理每个链接
    for (let i = 0; i < 实际操作数; i++) {
        try {
            let 链接 = 链接列表[i];
            toast("处理第 " + (i + 1) + " 条链接: " + 链接);
            
            // 在打开链接前先强制退出小红书
            强制退出小红书();
            
            // 打开链接并获取页面信息
            app.openUrl(链接);
            console.log("已打开链接: " + 链接);
            sleep(3000); // 等待页面加载
            
            // 处理可能的弹窗，直到进入小红书
            处理打开流程();
            
            // 获取链接页面信息
            let 链接页面信息 = 获取页面信息();
            // 只输出需要的信息
            console.log("链接页面信息: 标题=[" + 链接页面信息.标题 + "], 内容=[" + 链接页面信息.内容 + "], 作者=[" + 链接页面信息.用户名 + "]");
            
            // 返回主界面
            if (!返回主界面()) {
                console.log("返回主界面失败");
                continue;
            }
            
            // 点击首页第一篇文章
            if (!点击首篇文章()) {
                console.log("点击首页文章失败");
                continue;
            }
            
            // 获取首页文章信息
            let 首页文章信息 = 获取页面信息();
            // 只输出需要的信息
            console.log("首页文章信息: 标题=[" + 首页文章信息.标题 + "], 内容=[" + 首页文章信息.内容 + "], 作者=[" + 首页文章信息.用户名 + "]");
            
            // 比较两篇文章是否为同一篇
            let 是同一篇文章 = 比较页面信息(链接页面信息, 首页文章信息);
            
            if (是同一篇文章) {
                console.log("确认是同一篇文章，执行点赞");
                // 执行点赞操作
                if (执行点赞()) {
                    console.log("点赞成功");
                    toast("点赞成功");
                } else {
                    console.log("点赞失败");
                    toast("点赞失败");
                }
            } else {
                console.log("不是同一篇文章，跳过点赞");
                toast("不是同一篇文章，跳过点赞");
            }
            
            // 返回主界面，准备处理下一个链接
            返回主界面();
            
            // 等待指定时间
            console.log(`等待${操作间隔}秒后处理下一条链接`);
            sleep(操作间隔 * 1000);
            
        } catch (e) {
            console.error("处理链接时出错: " + e);
            sleep(2000);
        }
    }
    
    toast("全部操作已完成");
}

/**
 * 读取链接文件
 * @param {string} 完整路径 - 链接文件的完整路径
 * @returns {Array} 链接数组
 */
function 读取链接文件(完整路径) {
    try {
        let 文件 = files.read(完整路径);
        let 链接列表 = 文件.split("\n");
        
        // 过滤空行
        链接列表 = 链接列表.filter(链接 => 链接.trim() !== "");
        
        return 链接列表;
    } catch (e) {
        console.error("读取链接文件失败: " + e);
        return [];
    }
}

/**
 * 打开链接并处理弹窗
 * @param {string} 链接 - 要打开的链接
 */
function 打开链接(链接) {
    try {
        // 使用浏览器打开链接
        app.openUrl(链接);
        console.log("已打开链接: " + 链接);
        sleep(3000); // 等待页面加载
        
        // 处理可能的弹窗，直到进入小红书
        处理打开流程();
    } catch (e) {
        console.error("打开链接失败: " + e);
    }
}

/**
 * 处理从浏览器打开到小红书的全流程
 */
function 处理打开流程() {
    // 设置最大尝试次数
    let 最大尝试次数 = 10;
    let 当前尝试次数 = 0;
    
    while (当前尝试次数 < 最大尝试次数) {
        // 获取当前所有文本元素
        let 所有文本 = textMatches(".*").find();
        let 文本数组 = [];
        
        // 收集所有文本及其位置
        所有文本.forEach(function(元素) {
            if (元素.text() && 元素.text().trim() !== "") {
                文本数组.push({
                    文本: 元素.text().trim(), // 去除首尾空格，确保精准匹配
                    bounds: 元素.bounds()
                });
            }
        });
        
        // 定义需要点击的关键词 - 精准匹配用
        let 关键词列表 = [
            "始终", "Chrome", "确定", "展开", "打开", "继续",
            "在不登录账号的情况下使用", "同意", "知道了",
            "浏览", "允许", "确认", "继续访问", "我同意",
            // 添加更多可能的精准按钮文本
            "打开方式", "选择浏览器", "使用浏览器打开", "使用Chrome打开",
            "仅本次", "总是", "取消", "是", "否"
        ];
        
        let 已点击 = false;
        
        // 检查每个关键词
        for (let i = 0; i < 关键词列表.length; i++) {
            let 关键词 = 关键词列表[i];
            
            // 寻找精准匹配关键词的文本
            for (let j = 0; j < 文本数组.length; j++) {
                let 项目 = 文本数组[j];
                
                // 改为精准匹配
                if (项目.文本 === 关键词) {
                    console.log("找到精准匹配关键词: " + 关键词);
                    
                    // 获取元素中心坐标
                    let x = 项目.bounds.centerX();
                    let y = 项目.bounds.centerY();
                    
                    // 点击该位置
                    click(x, y);
                    console.log("点击坐标: " + x + ", " + y);
                    
                    已点击 = true;
                    sleep(1000); // 等待点击后的反应
                    break;
                }
            }
            
            if (已点击) {
                break;
            }
        }
        
        // 检查是否已经进入小红书应用
        if (检查是否进入小红书()) {
            console.log("已成功进入小红书");
            return true;
        }
        
        // 如果没有找到任何可点击的元素，增加尝试计数
        if (!已点击) {
            当前尝试次数++;
            sleep(1000);
        }
    }
    
    console.log("达到最大尝试次数，未能成功进入小红书");
    return false;
}

/**
 * 检查是否已进入小红书
 * @returns {boolean} 是否在小红书中
 */
function 检查是否进入小红书() {
    // 使用包名检查当前应用是否为小红书
    // 小红书的包名是 com.xingin.xhs
    return currentPackage() === "com.xingin.xhs" || 
           // 备用检查方法，防止包名获取失败
           getPackageName("小红书") === currentPackage();
}


//====================================


/**
 * 从文本中提取数字
 * @param {string} text - 包含数字的文本
 * @returns {number|null} - 提取的数字，失败返回null
 */
function 提取数字(text) {
    if (!text) return null;
    
    try {
        // 先移除所有空格，确保能处理"点赞 1445"这种格式
        let cleanText = text.replace(/\s+/g, "");
        
        // 匹配数字部分（包括小数和千分位）
        let 数字匹配 = cleanText.match(/\d+(\.\d+)?/);
        if (数字匹配) {
            // 转换为数字
            let 提取结果 = parseFloat(数字匹配[0]);
            return 提取结果;
        }
        
        // 如果是纯数字文本，直接解析
        if (/^\d+$/.test(text)) {
            let 提取结果 = parseInt(text);
            return 提取结果;
        }
        
        return null;
    } catch (e) {
        return null;
    }
}


/**
 * 获取互动元素（点赞、收藏、评论）
 * @returns {Object|null} - 包含点赞、收藏、评论信息的对象，获取失败返回null
 */
function 获取互动元素() {
    try {
        // 初始化返回结果
        let 结果 = {
            点赞: null,
            收藏: null,
            评论: null,
            点赞元素: null,
            收藏元素: null,
            评论元素: null,
            是否视频: false
        };
        
        // 获取屏幕尺寸
        let 屏幕宽度 = device.width;
        let 屏幕高度 = device.height;
        
        // 定义视频互动区域（通常在屏幕右侧）
        let 视频互动区域 = {
            left: Math.floor(屏幕宽度 * 0.7),  // 屏幕右侧30%区域
            top: Math.floor(屏幕高度 * 0.3),   // 从屏幕30%位置开始
            right: 屏幕宽度,
            bottom: 屏幕高度 * 0.8             // 到屏幕80%位置结束
        };
        
        // 定义普通互动区域（通常在屏幕下半部分）
        let 普通互动区域 = {
            left: 0,
            top: Math.floor(屏幕高度 * 0.5),  // 从屏幕中间开始
            right: 屏幕宽度,
            bottom: 屏幕高度
        };
        
        // 首先检查是否为视频页面
        let 视频区域按钮 = className("android.widget.Button")
            .boundsInside(视频互动区域.left, 视频互动区域.top, 视频互动区域.right, 视频互动区域.bottom)
            .find();
        
        let 是否视频页面 = 视频区域按钮.length > 0;
        结果.是否视频 = 是否视频页面;
        
        // 根据页面类型选择互动区域
        let 互动区域 = 是否视频页面 ? 视频互动区域 : 普通互动区域;
        
        // 查找点赞元素
        let 点赞元素 = className("android.widget.Button")
            .boundsInside(互动区域.left, 互动区域.top, 互动区域.right, 互动区域.bottom)
            .descMatches(".*点赞.*")
            .findOne(500);
        
        if (点赞元素) {
            结果.点赞元素 = 点赞元素;
            let desc = 点赞元素.desc();
            结果.点赞 = 提取数字(desc);
        }
        
        // 查找收藏元素
        let 收藏元素 = className("android.widget.Button")
            .boundsInside(互动区域.left, 互动区域.top, 互动区域.right, 互动区域.bottom)
            .descMatches(".*收藏.*")
            .findOne(500);
        
        if (收藏元素) {
            结果.收藏元素 = 收藏元素;
            let desc = 收藏元素.desc();
            结果.收藏 = 提取数字(desc);
        }
        
        // 如果在指定区域没有找到互动元素，尝试扩大搜索范围
        if (!结果.点赞元素 && !结果.收藏元素) {
            // 尝试在整个屏幕查找
            点赞元素 = className("android.widget.Button").descMatches(".*点赞.*").findOne(500);
            收藏元素 = className("android.widget.Button").descMatches(".*收藏.*").findOne(500);
            
            if (点赞元素) {
                结果.点赞元素 = 点赞元素;
                结果.点赞 = 提取数字(点赞元素.desc());
            }
            
            if (收藏元素) {
                结果.收藏元素 = 收藏元素;
                结果.收藏 = 提取数字(收藏元素.desc());
            }
        }
        
        // 检查是否找到任何互动元素
        if (结果.点赞 !== null || 结果.收藏 !== null) {
            return 结果;
        }
        
        return null;
    } catch (e) {
        return null;
    }
}


/**
 * 获取作者信息
 * @returns {string|null} - 作者名称，获取失败返回null
 */
function 获取作者信息() {
    try {
        // 查找包含"作者"的Button元素
        let 作者元素 = className("android.widget.Button").descMatches(".*作者.*").findOne(300);
        
        if (作者元素) {
            let desc = 作者元素.desc();
            
            // 从描述中提取作者名称
            let 作者名称匹配 = desc.match(/作者,(.+)/);
            if (作者名称匹配 && 作者名称匹配[1]) {
                let 作者名称 = 作者名称匹配[1].trim();
                return 作者名称;
            }
        }
        
        // 尝试其他方式查找作者信息
        // 查找包含@的文本
        let 包含艾特的文本 = textMatches("@.*").findOne(300);
        if (包含艾特的文本) {
            return 包含艾特的文本.text();
        }
        
        // 查找可能的作者名称（短文本，通常在页面上方）
        let 屏幕高度 = device.height;
        let 可能的作者元素列表 = className("android.widget.TextView")
            .boundsInside(0, 0, device.width, Math.floor(屏幕高度 * 0.3))
            .find();
        
        // 筛选可能的作者名称
        for (let i = 0; i < 可能的作者元素列表.length; i++) {
            let 元素 = 可能的作者元素列表[i];
            let 文本 = 元素.text();
            
            // 作者名称通常较短，且不包含特殊字符
            if (文本 && 文本.length > 0 && 文本.length < 20 && 
                !文本.includes("点赞") && !文本.includes("收藏") && 
                !文本.includes("评论") && !文本.match(/^\d+$/)) {
                return 文本;
            }
        }
        
        return null;
    } catch (e) {
        return null;
    }
}


/**
 * 获取页面文本内容
 * @returns {Array} - 包含文本内容、长度和元素的数组，按长度降序排序
 */
function 获取页面文本内容() {
    try {
        // 获取屏幕尺寸
        let 屏幕宽度 = device.width;
        let 屏幕高度 = device.height;
        
        // 获取页面所有文本元素
        let 所有文本元素 = className("android.widget.TextView")
            .boundsInside(0, 0, 屏幕宽度, 屏幕高度)
            .find();
        
        // 创建一个数组存储所有有效文本及其长度
        let 有效文本列表 = [];
        
        // 要过滤的关键词
        const 过滤关键词 = ["点赞", "收藏", "评论", "小红书", "分享", "复制链接", "举报"];
        
        for (let i = 0; i < 所有文本元素.length; i++) {
            let 元素 = 所有文本元素[i];
            let 文本 = 元素.text();
            
            // 跳过空文本或无效文本
            if (!文本 || 文本.length < 2) continue;
            
            // 跳过纯数字文本
            if (文本.match(/^\d+$/)) continue;
            
            // 跳过包含特定关键词的文本
            let 包含关键词 = false;
            for (let j = 0; j < 过滤关键词.length; j++) {
                if (文本.includes(过滤关键词[j])) {
                    包含关键词 = true;
                    break;
                }
            }
            if (包含关键词) continue;
            
            // 获取元素位置
            let 元素位置 = 元素.bounds();
            
            // 检查元素是否在屏幕可见区域内
            let 是否在屏幕内 = 元素位置.top >= 0 && 元素位置.bottom <= 屏幕高度;
            if (!是否在屏幕内) continue;
            
            // 将有效文本添加到列表
            有效文本列表.push({
                文本: 文本,
                长度: 文本.length,
                元素: 元素
            });
        }
        
        // 按文本长度降序排序
        有效文本列表.sort((a, b) => b.长度 - a.长度);
        
        return 有效文本列表;
    } catch (e) {
        return [];
    }
}


/**
 * 获取页面信息
 * @returns {Object} - 包含页面信息的对象
 */
function 获取页面信息() {
    try {
        // 初始化结果对象
        let 结果 = {
            标题: null,
            内容: null,
            用户名: null,
            点赞数: null,
            收藏数: null,
            评论数: null,
            是否视频: false
        };
        
        // 获取互动信息（点赞、收藏、评论）
        let 互动信息 = 获取互动元素();
        if (互动信息) {
            结果.点赞数 = 互动信息.点赞;
            结果.收藏数 = 互动信息.收藏;
            结果.评论数 = 互动信息.评论;
            结果.是否视频 = 互动信息.是否视频;
        } else {
            // 如果获取互动信息失败，尝试直接判断是否为视频页面
            结果.是否视频 = 判断是否为视频页面();
        }
        
        // 获取用户名/作者信息
        结果.用户名 = 获取作者信息();
        
        // 获取页面文本内容
        let 有效文本列表 = 获取页面文本内容();
        
        // 处理视频页面的特殊情况
        if (结果.是否视频) {
            // 对于视频页面，用户名通常是最短的有效文本之一
            if (有效文本列表.length > 0) {
                // 如果还没有获取到用户名，使用最短的有效文本
                if (!结果.用户名 && 有效文本列表.length > 0) {
                    let 最短文本 = 有效文本列表[有效文本列表.length - 1].文本;
                    结果.用户名 = 最短文本;
                }
                
                // 视频标题可能是最长的文本
                if (有效文本列表.length >= 1) {
                    let 可能的标题 = 有效文本列表[0].文本;
                    结果.标题 = 可能的标题;
                    
                    // 视频内容使用最长文本，但限制在50个字符以内
                    if (可能的标题.length > 50) {
                        结果.内容 = 可能的标题.substring(0, 50) + "...";
                    } else {
                        结果.内容 = 可能的标题;
                    }
                }
            }
        } else {
            // 非视频页面（图文）的处理逻辑
            
            // 标题通常是较长的文本，但不是最长的
            if (有效文本列表.length > 0) {
                // 使用最长的文本作为内容
                let 最长文本 = 有效文本列表[0].文本;
                结果.内容 = 最长文本.length > 50 ? 最长文本.substring(0, 50) + "..." : 最长文本;
                
                // 如果有第二长的文本，使用它作为标题
                if (有效文本列表.length > 1) {
                    结果.标题 = 有效文本列表[1].文本;
                }
            }
        }
        
        // 简化输出，只返回需要的信息
        return {
            标题: 结果.标题,
            内容: 结果.内容,
            用户名: 结果.用户名,
            点赞数: 结果.点赞数,
            收藏数: 结果.收藏数,
            是否视频: 结果.是否视频
        };
    } catch (e) {
        return {
            标题: null,
            内容: null,
            用户名: null,
            点赞数: null,
            收藏数: null,
            是否视频: false,
            error: e.message
        };
    }
}


/**
 * 判断是否为视频页面
 * @returns {boolean} - 是否为视频页面
 */
function 判断是否为视频页面() {
    try {
        // 获取屏幕尺寸
        let 屏幕宽度 = device.width;
        let 屏幕高度 = device.height;
        
        // 定义视频互动区域（通常在屏幕右侧）
        let 视频互动区域 = {
            left: Math.floor(屏幕宽度 * 0.7),  // 屏幕右侧30%区域
            top: Math.floor(屏幕高度 * 0.3),   // 从屏幕30%位置开始
            right: 屏幕宽度,
            bottom: 屏幕高度 * 0.8             // 到屏幕80%位置结束
        };
        
        // 视频页面通常右侧有特定的互动按钮
        let 视频区域按钮 = className("android.widget.Button")
            .boundsInside(视频互动区域.left, 视频互动区域.top, 视频互动区域.right, 视频互动区域.bottom)
            .findOne(300);
        
        // 如果在视频互动区域找到按钮，很可能是视频页面
        return 视频区域按钮 != null;
    } catch (e) {
        return false;
    }
}


/**
 * 比较两个页面信息，判断是否为同一篇文章
 * @param {Object} 信息1 - 第一个页面信息
 * @param {Object} 信息2 - 第二个页面信息
 * @returns {boolean} - 是否为同一篇文章
 */
function 比较页面信息(信息1, 信息2) {
    // 如果任一信息为空，返回false
    if (!信息1 || !信息2) return false;
    
    // 1. 检查作者是否相同
    let 作者相同 = 信息1.用户名 && 信息2.用户名 && 信息1.用户名 === 信息2.用户名;
    
    // 2. 检查内容是否相似（如果内容存在）
    let 内容相似 = false;
    if (信息1.内容 && 信息2.内容) {
        // 如果内容长度超过20个字符，比较前20个字符是否相同
        if (信息1.内容.length > 20 && 信息2.内容.length > 20) {
            内容相似 = 信息1.内容.substring(0, 20) === 信息2.内容.substring(0, 20);
        } else {
            // 否则比较整个内容
            内容相似 = 信息1.内容 === 信息2.内容;
        }
    }
    
    // 3. 检查点赞数和收藏数是否在合理范围内
    let 点赞数合理 = false;
    let 收藏数合理 = false;
    
    if (信息1.点赞数 !== null && 信息2.点赞数 !== null) {
        // 允许点赞数有小幅增长（其他用户可能点赞）或小幅减少（最多减少5个，可能是其他用户取消点赞）
        let 点赞差值 = 信息2.点赞数 - 信息1.点赞数;
        点赞数合理 = 点赞差值 >= -5 && 点赞差值 < 10; // 允许减少最多5个，增加不超过10个
    }
    
    if (信息1.收藏数 !== null && 信息2.收藏数 !== null) {
        // 允许收藏数有小幅增长或小幅减少
        let 收藏差值 = 信息2.收藏数 - 信息1.收藏数;
        收藏数合理 = 收藏差值 >= -5 && 收藏差值 < 10; // 允许减少最多5个，增加不超过10个
    }
    
    // 4. 综合判断是否是同一篇文章
    // 如果作者相同且内容相似，几乎可以确定是同一篇文章
    if (作者相同 && 内容相似) {
        return true;
    }
    
    // 如果作者相同且点赞数和收藏数变化合理，可能是同一篇文章
    if (作者相同 && 点赞数合理 && 收藏数合理) {
        return true;
    }
    
    // 如果内容相似且点赞数和收藏数变化合理，可能是同一篇文章
    if (内容相似 && 点赞数合理 && 收藏数合理) {
        return true;
    }
    
    return false;
}


/**
 * 返回主界面
 * @returns {boolean} - 是否成功返回主界面
 */
function 返回主界面() {
    console.log("返回主界面");
    
    // 首页可能出现的关键词
    const 首页关键词 = ["首页", "发现", "关注", "直播","视频","购物","消息", "短剧", "附近", "热门", "消息", "我", "推荐", "穿搭", "情感"];
    
    // 最多按5次返回键
    for (let i = 0; i < 5; i++) {
        // 检查是否已在主界面
        let 找到的关键词 = 0;
        
        try {
            // 查找所有文本元素，减少超时时间
            let 所有文本 = textMatches(".*").find();
            
            // 检查有多少关键词存在
            for (let j = 0; j < 所有文本.length; j++) {
                let 文本内容 = 所有文本[j].text();
                if (!文本内容) continue;
                
                // 使用some方法更高效地检查是否包含任何关键词
                if (首页关键词.some(关键词 => 文本内容.includes(关键词))) {
                    找到的关键词++;
                    
                    // 如果找到3个或以上关键词，认为已在首页
                    if (找到的关键词 >= 5) {
                        console.log(`已找到 ${找到的关键词} 个首页关键词，确认在首页`);
                        return true;
                    }
                }
            }
        } catch (e) {
            console.error("检查首页元素出错: " + e.message);
        }
        
        // 按返回键
        console.log("按返回键");
        back();
        sleep(1500);
    }
    
    // 最后检查一次
    let 找到的关键词 = 0;
    let 所有文本 = textMatches(".*").find();
    for (let j = 0; j < 所有文本.length; j++) {
        let 文本内容 = 所有文本[j].text();
        if (!文本内容) continue;
        
        if (首页关键词.some(关键词 => 文本内容.includes(关键词))) {
            找到的关键词++;
        }
    }
    
    if (找到的关键词 >= 3) {
        console.log(`已找到 ${找到的关键词} 个首页关键词，确认在首页`);
        return true;
    }
    
    console.log("未能确认返回首页");
    return false;
}


/**
 * 点击首篇文章
 * @returns {boolean} - 是否成功点击
 */
function 点击首篇文章() {
    console.log("点击首页文章");
    
    try {
        // 等待首页加载
        sleep(2000);
        
        // 随机化点击坐标
        let x = 250 + Math.floor(Math.random() * 40) - 20;  // 250 ± 20
        let y = 500 + Math.floor(Math.random() * 40) - 20;  // 500 ± 20
        
        console.log(`点击坐标: (${x}, ${y})`);
        click(x, y);
        
        // 等待文章加载
        sleep(3000);
        
        return true;
    } catch (e) {
        console.error("点击首篇文章出错: " + e.message);
        return false;
    }
}


/**
 * 执行点赞操作
 * @returns {boolean} - 是否成功点赞
 */
function 执行点赞() {
    console.log("执行点赞操作");
    
    try {
        // 获取互动元素
        let 互动元素 = 获取互动元素();
        if (!互动元素 || !互动元素.点赞元素) {
            console.log("未找到点赞元素");
            return false;
        }
        
        // 检查是否已点赞
        let desc = 互动元素.点赞元素.desc();
        if (desc && desc.includes("已点赞")) {
            console.log("已经点过赞了");
            return true;
        }
        
        // 点击点赞按钮
        console.log("点击点赞按钮");
        互动元素.点赞元素.click();
        sleep(1000);
        
        return true;
    } catch (e) {
        console.error("执行点赞出错: " + e.message);
        return false;
    }
}