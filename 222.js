// 1. 通过 uiautomator dump 生成 XML 文件
let cmd = `su -c "uiautomator dump --compressed /sdcard/window_dump.xml"`;
shell(cmd, true);  // 执行命令，true 表示使用 Root 权限

// 2. 读取 XML 文件内容
let xmlPath = "/sdcard/window_dump.xml";
let xmlContent = files.read(xmlPath);
console.log(xmlContent);  // 打印 XML 内容

// 3. （可选）删除临时文件
//files.remove(xmlPath);

// 全局变量，保存root会话状态
let root会话 = false;

/**
 * 初始化Root权限，获取一个root会话
 * @returns {boolean} 是否成功获取root权限
 */
function 初始化Root权限() {
    // 如果已经获取过root权限，直接返回true，不再重复申请
    if (root会话) {
        console.log("已有root会话，无需重新申请权限");
        return true;
    }

    console.log("首次尝试获取root权限...");

    try {
        // 使用普通shell命令测试root权限
        let result = shell("su -c 'echo root_test'", true);

        if (result.code === 0 && result.result.includes("root_test")) {
            console.log("成功获取root权限，标记为已授权");
            root会话 = true;
            return true;
        } else {
            console.log("获取root权限失败: " + result.error);
            root会话 = false;
            return false;
        }
    } catch (e) {
        console.error("初始化Root权限出错: " + e);
        root会话 = false;
        return false;
    }
}

//检查是否进入小红书()
function 检查是否进入小红书() {
    // 使用Root命令获取当前应用包名
    console.log("11111111111");
    //let result = 执行Root命令('dumpsys window | grep mCurrentFocus');
    let result = shell("su -c 'dumpsys window | grep mCurrentFocus'", true);
    console.log("22222222");
    console.log(result);
    if (result.code === 0) {
        let match = result.result.match(/mCurrentFocus.+?{.+?(\S+)\/(\S+)}/);
        if (match && match[1]) {
            let 当前包名 = match[1];
            console.log("当前应用包名: " + 当前包名);
            return 当前包名 === "com.xingin.xhs";
        }
    }

    console.log("无法确认是否已进入小红书");
    return false;
} 

