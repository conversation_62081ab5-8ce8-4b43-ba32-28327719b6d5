﻿/**
 * 灏忕孩涔﹀厓绱犺幏鍙栨祴璇曡剼鏈?- 鏀寔鏃犻殰纰嶅拰Root妯″紡
 * 
 * 姝よ剼鏈紨绀哄浣曚娇鐢?uiautomator dump 鍛戒护鑾峰彇鐣岄潰鍏冪礌
 * 骞惰В鏋愬嚭鍏冪礌鐨勬枃鏈唴瀹瑰拰鍧愭爣
 */

// 鎿嶄綔妯″紡鎺у埗鍙橀噺: 1=鏃犻殰纰嶆ā寮? 2=Root Shell妯″紡
const 鎿嶄綔妯″紡 = 2; // 榛樿浣跨敤Root Shell妯″紡

/**
 * 鍏抽棴瓒呯骇鐢ㄦ埛鏉冮檺鎻愮ず
 * 閫氳繃淇敼AutoJS鐨勯厤缃潵绂佺敤Root鏉冮檺鎻愮ず
 */
function 鍏抽棴瓒呯骇鐢ㄦ埛鏉冮檺鎻愮ず() {
    try {
        console.log("灏濊瘯鍏抽棴瓒呯骇鐢ㄦ埛鏉冮檺鎻愮ず...");
        // 浣跨敤鏇寸畝鍗曠殑鏂规硶鍏抽棴瓒呯骇鐢ㄦ埛鏉冮檺鎻愮ず
        importClass(com.stardust.autojs.core.accessibility.AccessibilityBridge.WindowFilter);
        let bridge = runtime.accessibilityBridge;
        let bridgeField = runtime.getClass().getDeclaredField("accessibilityBridge");
        let configField = bridgeField.getType().getDeclaredField("mConfig");
        configField.setAccessible(true);
        configField.set(bridge, configField.getType().newInstance());
        bridge.setWindowFilter(new JavaAdapter(WindowFilter, {
            filter: function(info) {
                return true;
            }
        }));
        console.log("鎴愬姛鍏抽棴瓒呯骇鐢ㄦ埛鏉冮檺鎻愮ず");
        return true;
    } catch (e) {
        console.error("鍏抽棴瓒呯骇鐢ㄦ埛鏉冮檺鎻愮ず澶辫触: " + e);
        return false;
    }
}

// 妫€鏌ユ搷浣滄ā寮忓苟杈撳嚭鎻愮ず
if (鎿嶄綔妯″紡 === 1) {
    console.log("褰撳墠浣跨敤鏃犻殰纰嶆ā寮忔搷浣滐紝璇风‘淇濆凡寮€鍚棤闅滅鏈嶅姟");
    // 妫€鏌ユ棤闅滅鏈嶅姟鏄惁宸插惎鐢?
    if (!auto.service) {
        console.log("鏃犻殰纰嶆湇鍔℃湭鍚敤锛屽皾璇曞惎鍔?..");
        auto.waitFor();
    }
} else if (鎿嶄綔妯″紡 === 2) {
    console.log("褰撳墠浣跨敤Root Shell妯″紡鎿嶄綔锛岃纭繚宸茶幏鍙朢oot鏉冮檺");
    // 妫€鏌oot鏉冮檺
    if (!shell("su -c 'echo root_test'", true).result.includes("root_test")) {
        console.log("璀﹀憡锛氭湭鑾峰彇鍒?root 鏉冮檺锛岃剼鏈彲鑳芥棤娉曟甯稿伐浣?);
    } else {
        console.log("Root鏉冮檺妫€鏌ラ€氳繃");
    }
} else {
    console.log("閿欒锛氭湭鐭ョ殑鎿嶄綔妯″紡锛岃璁剧疆涓?(鏃犻殰纰?鎴?(Root Shell)");
}

/**
 * 浣跨敤 uiautomator dump 鑾峰彇褰撳墠鐣岄潰鐨?XML 缁撴瀯
 * @returns {string|null} XML 鍐呭鎴?null锛堝鏋滃け璐ワ級
 */
function 鑾峰彇鐣岄潰XML() {
    console.log("寮€濮嬭幏鍙栫晫闈?XML...");
    
    // 浣跨敤 root 鏉冮檺鎵ц uiautomator dump 鍛戒护
    let result = shell("su -c 'uiautomator dump /sdcard/window_dump.xml'", true);
    
    if (result.code === 0) {
        console.log("鐣岄潰 XML 瀵煎嚭鎴愬姛");
        
        // 璇诲彇瀵煎嚭鐨?XML 鏂囦欢
        try {
            let xmlContent = files.read("/sdcard/window_dump.xml");
            console.log("鎴愬姛璇诲彇 XML 鏂囦欢锛屽ぇ灏? " + xmlContent.length + " 瀛楄妭");
            return xmlContent;
        } catch (e) {
            console.error("璇诲彇 XML 鏂囦欢澶辫触: " + e.message);
            return null;
        }
    } else {
        console.error("鐣岄潰 XML 瀵煎嚭澶辫触: " + result.error);
        return null;
    }
}

/**
 * 浠?XML 涓彁鍙栨墍鏈夋枃鏈厓绱犲強鍏跺潗鏍?
 * @param {string} xmlContent - XML 鍐呭
 * @returns {Array} 鍏冪礌鏁扮粍锛屾瘡涓厓绱犲寘鍚枃鏈拰鍧愭爣
 */
function 鎻愬彇鏂囨湰鍏冪礌(xmlContent) {
    console.log("寮€濮嬭В鏋?XML 涓殑鏂囨湰鍏冪礌...");
    let 鍏冪礌鍒楄〃 = [];
    let 鏂囨湰姝ｅ垯 = /(<node [^>]*text="([^"]*)"[^>]*bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"[^>]*>)/g;
    let 鍖归厤缁撴灉;
    let 绌烘枃鏈鏁?= 0;
    while ((鍖归厤缁撴灉 = 鏂囨湰姝ｅ垯.exec(xmlContent)) !== null) {
        let nodeStr = 鍖归厤缁撴灉[1];
        let 鏂囨湰 = 鍖归厤缁撴灉[2];
        if (!鏂囨湰 || 鏂囨湰.trim() === "") {
            绌烘枃鏈鏁?+;
            continue;
        }
        let 宸?= parseInt(鍖归厤缁撴灉[3]);
        let 涓?= parseInt(鍖归厤缁撴灉[4]);
        let 鍙?= parseInt(鍖归厤缁撴灉[5]);
        let 涓?= parseInt(鍖归厤缁撴灉[6]);
        // 瑙ｆ瀽鎵€鏈夊睘鎬?
        let 灞炴€?= {};
        let 灞炴€ф鍒?= /(\w+)="([^"]*)"/g;
        let 灞炴€у尮閰?
        while ((灞炴€у尮閰?= 灞炴€ф鍒?exec(nodeStr)) !== null) {
            灞炴€灞炴€у尮閰峓1]] = 灞炴€у尮閰峓2];
        }
        鍏冪礌鍒楄〃.push({
            鏂囨湰: 鏂囨湰,
            鍧愭爣: {
                宸? 宸?
                涓? 涓?
                鍙? 鍙?
                涓? 涓?
                涓績X: Math.floor((宸?+ 鍙? / 2),
                涓績Y: Math.floor((涓?+ 涓? / 2)
            },
            鍘熷灞炴€? 灞炴€?
        });
    }
    console.log("鍏辨壘鍒?" + 鍏冪礌鍒楄〃.length + " 涓湁鏁堟枃鏈厓绱?(宸茶繃婊?" + 绌烘枃鏈鏁?+ " 涓┖鏂囨湰鍏冪礌)");
    return 鍏冪礌鍒楄〃;
}

/**
 * 浠?XML 涓彁鍙栫壒瀹氭枃鏈殑鍏冪礌
 * @param {string} xmlContent - XML 鍐呭
 * @param {string} 鐩爣鏂囨湰 - 瑕佹煡鎵剧殑鏂囨湰锛堥儴鍒嗗尮閰嶏級
 * @returns {Object|null} 鎵惧埌鐨勫厓绱犳垨 null
 */
function 鏌ユ壘鐗瑰畾鏂囨湰鍏冪礌(xmlContent, 鐩爣鏂囨湰) {
    console.log("鏌ユ壘鍖呭惈鏂囨湰 '" + 鐩爣鏂囨湰 + "' 鐨勫厓绱?..");
    
    let 鎵€鏈夊厓绱?= 鎻愬彇鏂囨湰鍏冪礌(xmlContent);
    
    // 鏌ユ壘鍖呭惈鐩爣鏂囨湰鐨勫厓绱?
    for (let i = 0; i < 鎵€鏈夊厓绱?length; i++) {
        if (鎵€鏈夊厓绱燵i].鏂囨湰.includes(鐩爣鏂囨湰)) {
            console.log("鎵惧埌鍖归厤鍏冪礌: " + JSON.stringify(鎵€鏈夊厓绱燵i]));
            return 鎵€鏈夊厓绱燵i];
        }
    }
    
    console.log("鏈壘鍒板寘鍚枃鏈?'" + 鐩爣鏂囨湰 + "' 鐨勫厓绱?);
    return null;
}

/**
 * 浣跨敤 shell 鍛戒护妯℃嫙鐐瑰嚮
 * @param {number} x - X 鍧愭爣
 * @param {number} y - Y 鍧愭爣
 * @returns {boolean} 鏄惁鎴愬姛
 */
function shell鐐瑰嚮(x, y) {
    console.log("鎵ц shell 鐐瑰嚮: (" + x + ", " + y + ")");
    
    let result = shell("su -c 'input tap " + x + " " + y + "'", true);
    let 鎴愬姛 = result.code === 0;
    
    console.log(鎴愬姛 ? "鐐瑰嚮鎴愬姛" : "鐐瑰嚮澶辫触: " + result.error);
    return 鎴愬姛;
}

/**
 * 鏌ユ壘骞剁偣鍑荤壒瀹氭枃鏈殑鍏冪礌
 * @param {string} 鐩爣鏂囨湰 - 瑕佹煡鎵剧殑鏂囨湰
 * @returns {boolean} 鏄惁鎴愬姛
 */
function 鏌ユ壘骞剁偣鍑?鐩爣鏂囨湰) {
    let xmlContent = 鑾峰彇鐣岄潰XML();
    if (!xmlContent) return false;
    
    let 鍏冪礌 = 鏌ユ壘鐗瑰畾鏂囨湰鍏冪礌(xmlContent, 鐩爣鏂囨湰);
    if (!鍏冪礌) return false;
    
    return shell鐐瑰嚮(鍏冪礌.鍧愭爣.涓績X, 鍏冪礌.鍧愭爣.涓績Y);
}

/**
 * 鏌ユ壘鐐硅禐鎸夐挳骞剁偣鍑?
 * @returns {boolean} 鏄惁鎴愬姛
 */
function 鏌ユ壘鐐硅禐鎸夐挳() {
    let xmlContent = 鑾峰彇鐣岄潰XML();
    if (!xmlContent) return false;
    
    // 鍏堝皾璇曟煡鎵惧寘鍚?鐐硅禐"鐨勫厓绱?
    let 鐐硅禐鍏冪礌 = 鏌ユ壘鐗瑰畾鏂囨湰鍏冪礌(xmlContent, "鐐硅禐");
    
    if (鐐硅禐鍏冪礌) {
        return shell鐐瑰嚮(鐐硅禐鍏冪礌.鍧愭爣.涓績X, 鐐硅禐鍏冪礌.鍧愭爣.涓績Y);
    }
    
    // 濡傛灉娌℃壘鍒帮紝鍙兘闇€瑕佹煡鎵剧壒瀹氱殑鍥炬爣鎴栧叾浠栨爣璇?
    console.log("鏈壘鍒扮偣璧炴寜閽?);
    return false;
}

/**
 * 鎵撳嵃褰撳墠鐣岄潰鐨勬墍鏈夋枃鏈厓绱?
 */
function 鎵撳嵃鎵€鏈夋枃鏈厓绱?) {
    let xmlContent = 鑾峰彇鐣岄潰XML();
    if (!xmlContent) return;
    
    let 鍏冪礌鍒楄〃 = 鎻愬彇鏂囨湰鍏冪礌(xmlContent);
    
    console.log("==== 褰撳墠鐣岄潰鏂囨湰鍏冪礌 ====");
    for (let i = 0; i < 鍏冪礌鍒楄〃.length; i++) {
        let 鍏冪礌 = 鍏冪礌鍒楄〃[i];
        // 璺宠繃绌烘枃鏈唴瀹?
        if (!鍏冪礌.鏂囨湰 || 鍏冪礌.鏂囨湰.trim() === "") continue;
        
        console.log((i + 1) + ". 鏂囨湰: [" + 鍏冪礌.鏂囨湰 + "], 鍧愭爣: (" + 
                   鍏冪礌.鍧愭爣.涓績X + ", " + 鍏冪礌.鍧愭爣.涓績Y + ")");
    }
    console.log("==== 鍏?" + 鍏冪礌鍒楄〃.length + " 涓湁鏁堝厓绱?====");
    
    // 鏂板锛氭爣璁版牳蹇冨厓绱?
    鏍囪鏍稿績鍏冪礌(鍏冪礌鍒楄〃);
}

/**
 * 鏌ユ壘鍏冪礌
 * @param {string} 閫夋嫨鍣?- 鍏冪礌閫夋嫨鍣紝濡倀ext("鏂囨湰")鎴杋d("id")
 * @returns {UiObject|null} 鎵惧埌鐨勫厓绱犳垨null
 */
function 鏌ユ壘鍏冪礌(閫夋嫨鍣? {
    if (鎿嶄綔妯″紡 === 1) {
        // 鏃犻殰纰嶆ā寮?
        try {
            let 鍏冪礌 = 閫夋嫨鍣?findOne(1000);
            return 鍏冪礌 || null;
        } catch (e) {
            console.error("鏌ユ壘鍏冪礌鍑洪敊: " + e.message);
            return null;
        }
    } else {
        // Root Shell妯″紡涓嬫棤娉曠洿鎺ヤ娇鐢ㄦ棤闅滅閫夋嫨鍣?
        console.log("Root Shell妯″紡涓嬩笉鏀寔鐩存帴浣跨敤閫夋嫨鍣ㄦ煡鎵惧厓绱?);
        return null;
    }
}

/**
 * 鏌ユ壘鏂囨湰鍏冪礌骞剁偣鍑?
 * @param {string} 鏂囨湰 - 瑕佹煡鎵剧殑鏂囨湰
 * @returns {boolean} 鏄惁鎴愬姛鐐瑰嚮
 */
function 鏌ユ壘鏂囨湰骞剁偣鍑?鏂囨湰) {
    console.log("鏌ユ壘骞剁偣鍑绘枃鏈? " + 鏂囨湰);
    
    if (鎿嶄綔妯″紡 === 1) {
        // 鏃犻殰纰嶆ā寮?
        let 鍏冪礌 = text(鏂囨湰).findOne(3000);
        if (鍏冪礌) {
            鍏冪礌.click();
            return true;
        }
        return false;
    } else {
        // Root Shell妯″紡
        return 鏌ユ壘骞剁偣鍑?鏂囨湰);
    }
}

/**
 * 鎻愬彇ImageView鍏冪礌
 * @param {string} xmlContent - XML鍐呭
 * @returns {Array} 鍏冪礌鏁扮粍
 */
function 鎻愬彇ImageView鍏冪礌(xmlContent) {
    console.log("寮€濮嬫彁鍙朓mageView鍏冪礌...");
    let 鍏冪礌鍒楄〃 = [];
    // 淇敼姝ｅ垯琛ㄨ揪寮忥紝浣垮叾鏇村鏉惧湴鍖归厤鍙兘鐨勫浘鍍忓厓绱?
    let 姝ｅ垯 = /<node [^>]*class="android\.widget\.(ImageView|ImageButton|Button)"[^>]*bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"[^>]*\/>/g;
    let 鍖归厤;
    
    while ((鍖归厤 = 姝ｅ垯.exec(xmlContent)) !== null) {
        let 宸?= parseInt(鍖归厤[2]);
        let 涓?= parseInt(鍖归厤[3]);
        let 鍙?= parseInt(鍖归厤[4]);
        let 涓?= parseInt(鍖归厤[5]);
        
        // 瑙ｆ瀽鎵€鏈夊睘鎬?
        let nodeStr = 鍖归厤[0];
        let 灞炴€?= {};
        let 灞炴€ф鍒?= /(\w+)="([^"]*)"/g;
        let 灞炴€у尮閰?
        while ((灞炴€у尮閰?= 灞炴€ф鍒?exec(nodeStr)) !== null) {
            灞炴€灞炴€у尮閰峓1]] = 灞炴€у尮閰峓2];
        }
        
        鍏冪礌鍒楄〃.push({
            绫诲瀷: 鍖归厤[1],
            鍧愭爣: {
                宸? 宸?
                涓? 涓?
                鍙? 鍙?
                涓? 涓?
                涓績X: Math.floor((宸?+ 鍙? / 2),
                涓績Y: Math.floor((涓?+ 涓? / 2),
                瀹? 鍙?- 宸?
                楂? 涓?- 涓?
            },
            鍘熷灞炴€? 灞炴€?
        });
    }
    
    console.log(`鍏辨彁鍙栧埌 ${鍏冪礌鍒楄〃.length} 涓彲鑳界殑鍥惧儚鍏冪礌`);
    return 鍏冪礌鍒楄〃;
}

/**
 * 鑾峰彇浜や簰鎸夐挳浣嶇疆锛堝熀浜庢枃鏈亸绉伙級
 * @param {Array} 鏂囨湰鍏冪礌鍒楄〃 - 鏂囨湰鍏冪礌鏁扮粍
 * @returns {Object} 鍖呭惈鐐硅禐銆佹敹钘忓拰璇勮鎸夐挳浣嶇疆鐨勫璞?
 */
function 鑾峰彇浜や簰鎸夐挳浣嶇疆(鏂囨湰鍏冪礌鍒楄〃) {
    console.log("寮€濮嬭幏鍙栦氦浜掓寜閽綅缃紙鍩轰簬鏂囨湰鍋忕Щ锛?);
    
    // 浠庢枃鏈厓绱犱腑鎵惧嚭鐐硅禐銆佹敹钘忋€佽瘎璁烘暟瀛?
    let 鐐硅禐鏁版枃鏈?= null, 鏀惰棌鏁版枃鏈?= null, 璇勮鏁版枃鏈?= null, 鍒嗕韩鎸夐挳 = null;
    
    // 绛涢€夊簳閮ㄥ尯鍩熺殑鏁板瓧鏂囨湰鍜屾寜閽?
    let 搴曢儴鍏冪礌 = 鏂囨湰鍏冪礌鍒楄〃.filter(e => e.鍧愭爣.涓績Y > device.height * 0.7);
    
    // 鍏堟壘鍑哄垎浜寜閽?
    鍒嗕韩鎸夐挳 = 搴曢儴鍏冪礌.find(e => e.鏂囨湰 === "鍒嗕韩");
    
    // 绛涢€夋暟瀛楁枃鏈紝鍙兘鏄偣璧炴暟銆佹敹钘忔暟銆佽瘎璁烘暟
    let 浜掑姩鏁板瓧 = 搴曢儴鍏冪礌.filter(e =>
        /^[\d.]+涓?$/.test(e.鏂囨湰) || e.鏂囨湰 === "璇勮"
    );
    
    // 鎸塜鍧愭爣鎺掑簭锛岄€氬父浠庡乏鍒板彸鎺掑垪
    浜掑姩鏁板瓧.sort((a, b) => a.鍧愭爣.涓績X - b.鍧愭爣.涓績X);
    
    // 杈撳嚭鎵惧埌鐨勪簰鍔ㄥ厓绱?
    console.log("鎵惧埌鐨勪簰鍔ㄥ厓绱?");
    if (鍒嗕韩鎸夐挳) {
        console.log(`鍒嗕韩鎸夐挳: 鏂囨湰: [${鍒嗕韩鎸夐挳.鏂囨湰}], 鍧愭爣: (${鍒嗕韩鎸夐挳.鍧愭爣.涓績X}, ${鍒嗕韩鎸夐挳.鍧愭爣.涓績Y})`);
    }
    浜掑姩鏁板瓧.forEach((鍏冪礌, 绱㈠紩) => {
        console.log(`${绱㈠紩 + 1}. 鏂囨湰: [${鍏冪礌.鏂囨湰}], 鍧愭爣: (${鍏冪礌.鍧愭爣.涓績X}, ${鍏冪礌.鍧愭爣.涓績Y})`);
    });
    
    // 鏍规嵁浣嶇疆鍒嗛厤鐐硅禐銆佹敹钘忋€佽瘎璁?
    // 濡傛灉鏈夊垎浜寜閽紝鍒欎簰鍔ㄦ暟瀛楃殑椤哄簭閫氬父鏄細鐐硅禐鏁般€佹敹钘忔暟銆佽瘎璁烘暟
    if (鍒嗕韩鎸夐挳) {
        // 鏍规嵁鍒嗕韩鎸夐挳鐨勪綅缃垽鏂紝鍒嗕韩鎸夐挳鍙充晶鐨勬暟瀛楅€氬父鏄偣璧炴暟銆佹敹钘忔暟
        let 鍒嗕韩鍙充晶鏁板瓧 = 浜掑姩鏁板瓧.filter(e => e.鍧愭爣.涓績X > 鍒嗕韩鎸夐挳.鍧愭爣.涓績X);
        鍒嗕韩鍙充晶鏁板瓧.sort((a, b) => a.鍧愭爣.涓績X - b.鍧愭爣.涓績X);
        
        if (鍒嗕韩鍙充晶鏁板瓧.length >= 2) {
            鐐硅禐鏁版枃鏈?= 鍒嗕韩鍙充晶鏁板瓧[0];
            鏀惰棌鏁版枃鏈?= 鍒嗕韩鍙充晶鏁板瓧[1];
            if (鍒嗕韩鍙充晶鏁板瓧.length >= 3) {
                璇勮鏁版枃鏈?= 鍒嗕韩鍙充晶鏁板瓧[2];
            }
        } else if (鍒嗕韩鍙充晶鏁板瓧.length === 1) {
            鐐硅禐鏁版枃鏈?= 鍒嗕韩鍙充晶鏁板瓧[0];
        }
    } else {
        // 娌℃湁鍒嗕韩鎸夐挳锛屼娇鐢ㄥ師鏉ョ殑閫昏緫
        if (浜掑姩鏁板瓧.length >= 3) {
            鐐硅禐鏁版枃鏈?= 浜掑姩鏁板瓧[0];
            鏀惰棌鏁版枃鏈?= 浜掑姩鏁板瓧[1];
            璇勮鏁版枃鏈?= 浜掑姩鏁板瓧[2];
        } else if (浜掑姩鏁板瓧.length === 2) {
            鐐硅禐鏁版枃鏈?= 浜掑姩鏁板瓧[0];
            鏀惰棌鏁版枃鏈?= 浜掑姩鏁板瓧[1];
        } else if (浜掑姩鏁板瓧.length === 1) {
            鐐硅禐鏁版枃鏈?= 浜掑姩鏁板瓧[0];
        }
    }
    
    // 杈撳嚭璇嗗埆缁撴灉
    if (鐐硅禐鏁版枃鏈? console.log(`璇嗗埆鍒扮偣璧炴暟鏂囨湰: [${鐐硅禐鏁版枃鏈?鏂囨湰}], 鍧愭爣: (${鐐硅禐鏁版枃鏈?鍧愭爣.涓績X}, ${鐐硅禐鏁版枃鏈?鍧愭爣.涓績Y})`);
    if (鏀惰棌鏁版枃鏈? console.log(`璇嗗埆鍒版敹钘忔暟鏂囨湰: [${鏀惰棌鏁版枃鏈?鏂囨湰}], 鍧愭爣: (${鏀惰棌鏁版枃鏈?鍧愭爣.涓績X}, ${鏀惰棌鏁版枃鏈?鍧愭爣.涓績Y})`);
    if (璇勮鏁版枃鏈? console.log(`璇嗗埆鍒拌瘎璁烘暟鏂囨湰: [${璇勮鏁版枃鏈?鏂囨湰}], 鍧愭爣: (${璇勮鏁版枃鏈?鍧愭爣.涓績X}, ${璇勮鏁版枃鏈?鍧愭爣.涓績Y})`);
    
    return {
        鐐硅禐鏁版枃鏈?
        鏀惰棌鏁版枃鏈?
        璇勮鏁版枃鏈?
        鍒嗕韩鎸夐挳
    };
}

/**
 * 鍒ゆ柇鎸夐挳鐘舵€侊紙閫氳繃鍗曠偣鍙栬壊鑾峰彇鎸夐挳棰滆壊锛?
 * @param {Object} 鎸夐挳淇℃伅 - 鍖呭惈鎸夐挳浣嶇疆鐨勫璞?
 * @returns {Object} 鍖呭惈鍚勬寜閽姸鎬佺殑瀵硅薄
 */
function 鍒ゆ柇鎸夐挳鐘舵€?鎸夐挳淇℃伅) {
    if (!鎸夐挳淇℃伅) {
        console.log("鏈彁渚涙寜閽俊鎭紝鏃犳硶鍒ゆ柇鐘舵€?);
        return null;
    }
    
    if (!requestScreenCapture()) {
        console.log("璇锋眰鎴浘鏉冮檺澶辫触");
        return null;
    }
    
    let img = captureScreen();
    let 灞忓箷瀹藉害 = img.getWidth();
    let 灞忓箷楂樺害 = img.getHeight();
    console.log(`褰撳墠灞忓箷鍒嗚鲸鐜? ${灞忓箷瀹藉害}x${灞忓箷楂樺害}`);
    
    // 浣跨敤鎻愪緵鐨勭簿纭鑹?
    let 鐐硅禐棰滆壊 = "#FF2442"; // 鐐硅禐鍚庣殑绾㈣壊
    let 鏀惰棌棰滆壊 = "#FCBD54"; // 鏀惰棌鍚庣殑榛勮壊
    
    console.log(`鐩爣鐐硅禐棰滆壊: ${鐐硅禐棰滆壊}`);
    console.log(`鐩爣鏀惰棌棰滆壊: ${鏀惰棌棰滆壊}`);
    
    let 鐘舵€?= {
        宸茬偣璧? false,
        宸叉敹钘? false
    };
    
    // 妫€鏌ユ槸鍚︽槸甯﹀垎浜寜閽殑鐣岄潰
    let 甯﹀垎浜寜閽?= !!鎸夐挳淇℃伅.鍒嗕韩鎸夐挳;
    
    // X鍋忕Щ鑼冨洿锛堝鏋滄槸甯﹀垎浜寜閽殑鐣岄潰锛屽垯X涓嶅亸绉伙級
    let X鍋忕Щ鑼冨洿 = 甯﹀垎浜寜閽?? [0] : [-60, -65, -70, -75, -80, -85, -90];
    
    // Y鍋忕Щ鑼冨洿浠?60鍒?90
    let Y鍋忕Щ鑼冨洿 = [-60, -65, -70, -75, -80, -85, -90];
    
    console.log(`鐣岄潰绫诲瀷: ${甯﹀垎浜寜閽?? "甯﹀垎浜寜閽? : "鏍囧噯鐣岄潰"}`);
    console.log(`X鍋忕Щ鑼冨洿: ${X鍋忕Щ鑼冨洿.join(", ")}`);
    console.log(`Y鍋忕Щ鑼冨洿: ${Y鍋忕Щ鑼冨洿.join(", ")}`);
    
    // 鍒ゆ柇鐐硅禐鎸夐挳鐘舵€?
    if (鎸夐挳淇℃伅.鐐硅禐鏁版枃鏈? {
        console.log("===== 鐐硅禐鎸夐挳妫€娴?=====");
        console.log(`鐐硅禐鏁版枃鏈綅缃? 宸?${鎸夐挳淇℃伅.鐐硅禐鏁版枃鏈?鍧愭爣.宸, 涓績X=${鎸夐挳淇℃伅.鐐硅禐鏁版枃鏈?鍧愭爣.涓績X}, 涓績Y=${鎸夐挳淇℃伅.鐐硅禐鏁版枃鏈?鍧愭爣.涓績Y}`);
        console.log(`浣跨敤Y鍋忕Щ鑼冨洿: -60鍒?90`);
        
        // 鍙岄噸寰幆閬嶅巻X鍜孻鐨勫亸绉昏寖鍥?
        let 宸叉壘鍒扮偣璧為鑹?= false;
        for (let i = 0; i < X鍋忕Щ鑼冨洿.length && !宸叉壘鍒扮偣璧為鑹? i++) {
            let X鍋忕Щ = X鍋忕Щ鑼冨洿[i];
            
            for (let j = 0; j < Y鍋忕Щ鑼冨洿.length && !宸叉壘鍒扮偣璧為鑹? j++) {
                let Y鍋忕Щ = Y鍋忕Щ鑼冨洿[j];
                
                // 濡傛灉鏄甫鍒嗕韩鎸夐挳鐨勭晫闈紝浣跨敤涓績X鍧愭爣鑰屼笉鏄乏杈圭晫
                let x = 甯﹀垎浜寜閽?? 
                    鎸夐挳淇℃伅.鐐硅禐鏁版枃鏈?鍧愭爣.涓績X + X鍋忕Щ : 
                    鎸夐挳淇℃伅.鐐硅禐鏁版枃鏈?鍧愭爣.宸?+ X鍋忕Щ;
                    
                let y = 鎸夐挳淇℃伅.鐐硅禐鏁版枃鏈?鍧愭爣.涓績Y + Y鍋忕Щ;
                
                // 纭繚鍧愭爣鍦ㄥ睆骞曡寖鍥村唴
                if (x < 0 || x >= 灞忓箷瀹藉害 || y < 0 || y >= 灞忓箷楂樺害) {
                    console.log(`鍧愭爣(${x}, ${y})瓒呭嚭灞忓箷鑼冨洿锛岃烦杩嘸);
                    continue;
                }
                
                // 鑾峰彇棰滆壊
                let color = images.pixel(img, x, y);
                let colorHex = colors.toString(color).toUpperCase();
                let r = colors.red(color), g = colors.green(color), b = colors.blue(color);
                
                // 鍒ゆ柇棰滆壊鐩镐技搴?
                let 涓庣偣璧為鑹茬浉浼?= colors.isSimilar(color, 鐐硅禐棰滆壊, 30);
                console.log(`X鍋忕Щ${X鍋忕Щ}, Y鍋忕Щ${Y鍋忕Щ}妫€娴? 浣嶇疆(${x}, ${y}), 棰滆壊: ${colorHex}, RGB(${r},${g},${b}), ${涓庣偣璧為鑹茬浉浼?? "鍖归厤" : "涓嶅尮閰?}`);
                
                if (涓庣偣璧為鑹茬浉浼? {
                    鐘舵€?宸茬偣璧?= true;
                    宸叉壘鍒扮偣璧為鑹?= true;
                    console.log(`鍦╔鍋忕Щ${X鍋忕Щ}, Y鍋忕Щ${Y鍋忕Щ}澶勬壘鍒扮偣璧為鑹插尮閰嶏紝纭宸茬偣璧瀈);
                    break; // 鎵惧埌鍖归厤棰滆壊锛岃烦鍑哄唴灞傚惊鐜?
                }
            }
        }
        
        console.log(`鐐硅禐鐘舵€? ${鐘舵€?宸茬偣璧?? "宸茬偣璧? : "鏈偣璧?}`);
    }
    
    // 鍒ゆ柇鏀惰棌鎸夐挳鐘舵€?
    if (鎸夐挳淇℃伅.鏀惰棌鏁版枃鏈? {
        console.log("\n===== 鏀惰棌鎸夐挳妫€娴?=====");
        console.log(`鏀惰棌鏁版枃鏈綅缃? 宸?${鎸夐挳淇℃伅.鏀惰棌鏁版枃鏈?鍧愭爣.宸, 涓績X=${鎸夐挳淇℃伅.鏀惰棌鏁版枃鏈?鍧愭爣.涓績X}, 涓績Y=${鎸夐挳淇℃伅.鏀惰棌鏁版枃鏈?鍧愭爣.涓績Y}`);
        console.log(`浣跨敤Y鍋忕Щ鑼冨洿: -60鍒?90`);
        
        // 鍙岄噸寰幆閬嶅巻X鍜孻鐨勫亸绉昏寖鍥?
        let 宸叉壘鍒版敹钘忛鑹?= false;
        for (let i = 0; i < X鍋忕Щ鑼冨洿.length && !宸叉壘鍒版敹钘忛鑹? i++) {
            let X鍋忕Щ = X鍋忕Щ鑼冨洿[i];
            
            for (let j = 0; j < Y鍋忕Щ鑼冨洿.length && !宸叉壘鍒版敹钘忛鑹? j++) {
                let Y鍋忕Щ = Y鍋忕Щ鑼冨洿[j];
                
                // 濡傛灉鏄甫鍒嗕韩鎸夐挳鐨勭晫闈紝浣跨敤涓績X鍧愭爣鑰屼笉鏄乏杈圭晫
                let x = 甯﹀垎浜寜閽?? 
                    鎸夐挳淇℃伅.鏀惰棌鏁版枃鏈?鍧愭爣.涓績X + X鍋忕Щ : 
                    鎸夐挳淇℃伅.鏀惰棌鏁版枃鏈?鍧愭爣.宸?+ X鍋忕Щ;
                    
                let y = 鎸夐挳淇℃伅.鏀惰棌鏁版枃鏈?鍧愭爣.涓績Y + Y鍋忕Щ;
                
                // 纭繚鍧愭爣鍦ㄥ睆骞曡寖鍥村唴
                if (x < 0 || x >= 灞忓箷瀹藉害 || y < 0 || y >= 灞忓箷楂樺害) {
                    console.log(`鍧愭爣(${x}, ${y})瓒呭嚭灞忓箷鑼冨洿锛岃烦杩嘸);
                    continue;
                }
                
                // 鑾峰彇棰滆壊
                let color = images.pixel(img, x, y);
                let colorHex = colors.toString(color).toUpperCase();
                let r = colors.red(color), g = colors.green(color), b = colors.blue(color);
                
                // 鍒ゆ柇棰滆壊鐩镐技搴?
                let 涓庢敹钘忛鑹茬浉浼?= colors.isSimilar(color, 鏀惰棌棰滆壊, 30);
                console.log(`X鍋忕Щ${X鍋忕Щ}, Y鍋忕Щ${Y鍋忕Щ}妫€娴? 浣嶇疆(${x}, ${y}), 棰滆壊: ${colorHex}, RGB(${r},${g},${b}), ${涓庢敹钘忛鑹茬浉浼?? "鍖归厤" : "涓嶅尮閰?}`);
                
                if (涓庢敹钘忛鑹茬浉浼? {
                    鐘舵€?宸叉敹钘?= true;
                    宸叉壘鍒版敹钘忛鑹?= true;
                    console.log(`鍦╔鍋忕Щ${X鍋忕Щ}, Y鍋忕Щ${Y鍋忕Щ}澶勬壘鍒版敹钘忛鑹插尮閰嶏紝纭宸叉敹钘廯);
                    break; // 鎵惧埌鍖归厤棰滆壊锛岃烦鍑哄唴灞傚惊鐜?
                }
            }
        }
        
        console.log(`鏀惰棌鐘舵€? ${鐘舵€?宸叉敹钘?? "宸叉敹钘? : "鏈敹钘?}`);
    }
    
    return 鐘舵€?
}

/**
 * 璋冭瘯浜や簰鎸夐挳
 * 妫€娴嬫寜閽姸鎬侊紝浣跨敤鏂扮殑X鍜孻鍋忕Щ鑼冨洿
 */
function 璋冭瘯浜や簰鎸夐挳() {
    console.log("寮€濮嬫娴嬩氦浜掓寜閽姸鎬?);
    
    // 鑾峰彇鐣岄潰鍏冪礌
    let xmlContent = 鑾峰彇鐣岄潰XML();
    if (!xmlContent) {
        console.log("鑾峰彇鐣岄潰XML澶辫触");
        return;
    }
    
    let 鍏冪礌鍒楄〃 = 鎻愬彇鏂囨湰鍏冪礌(xmlContent);
    let 鎸夐挳淇℃伅 = 鑾峰彇浜や簰鎸夐挳浣嶇疆(鍏冪礌鍒楄〃);
    
    if (!鎸夐挳淇℃伅) {
        console.log("鏈壘鍒版寜閽綅缃?);
        return;
    }
    
    // 杈撳嚭鐣岄潰绫诲瀷淇℃伅
    if (鎸夐挳淇℃伅.鍒嗕韩鎸夐挳) {
        console.log("妫€娴嬪埌甯︽湁鍒嗕韩鎸夐挳鐨勮棰戠晫闈?);
    } else {
        console.log("妫€娴嬪埌鏍囧噯鐣岄潰锛堟棤鍒嗕韩鎸夐挳锛?);
    }
    
    // 妫€鏌ユ槸鍚︽壘鍒扮偣璧炲拰鏀惰棌鏁版枃鏈?
    if (!鎸夐挳淇℃伅.鐐硅禐鏁版枃鏈? {
        console.log("鏈壘鍒扮偣璧炴暟鏂囨湰锛屾棤娉曟娴嬬偣璧炵姸鎬?);
        return;
    }
    
    if (!鎸夐挳淇℃伅.鏀惰棌鏁版枃鏈? {
        console.log("鏈壘鍒版敹钘忔暟鏂囨湰锛屾棤娉曟娴嬫敹钘忕姸鎬?);
    }
    
    // 妫€鏌ュ綋鍓嶇姸鎬?
    let 鐘舵€?= 鍒ゆ柇鎸夐挳鐘舵€?鎸夐挳淇℃伅);
    if (!鐘舵€? {
        console.log("鍒ゆ柇鎸夐挳鐘舵€佸け璐?);
        return;
    }
    
    console.log("\n===== 褰撳墠鎸夐挳鐘舵€?=====");
    console.log(`鐐硅禐鐘舵€? ${鐘舵€?宸茬偣璧?? "宸茬偣璧? : "鏈偣璧?}`);
    console.log(`鏀惰棌鐘舵€? ${鐘舵€?宸叉敹钘?? "宸叉敹钘? : "鏈敹钘?}`);
}

/**
 * 鐐瑰嚮鐐硅禐鎸夐挳
 * @param {Object} 鎸夐挳淇℃伅 - 鍖呭惈鎸夐挳浣嶇疆鐨勫璞?
 * @returns {boolean} 鏄惁鎴愬姛鐐瑰嚮
 */
function 鐐瑰嚮鐐硅禐鎸夐挳(鎸夐挳淇℃伅) {
    if (!鎸夐挳淇℃伅 || !鎸夐挳淇℃伅.鐐硅禐鎸夐挳) {
        console.log("鏈彁渚涚偣璧炴寜閽俊鎭紝鏃犳硶鐐瑰嚮");
        return false;
    }
    
    let x = 鎸夐挳淇℃伅.鐐硅禐鎸夐挳.鍧愭爣.涓績X;
    let y = 鎸夐挳淇℃伅.鐐硅禐鎸夐挳.鍧愭爣.涓績Y;
    
    console.log(`鐐瑰嚮鐐硅禐鎸夐挳: (${x}, ${y})`);
    return 鐐瑰嚮(x, y);
}

/**
 * 鐐瑰嚮鏀惰棌鎸夐挳
 * @param {Object} 鎸夐挳淇℃伅 - 鍖呭惈鎸夐挳浣嶇疆鐨勫璞?
 * @returns {boolean} 鏄惁鎴愬姛鐐瑰嚮
 */
function 鐐瑰嚮鏀惰棌鎸夐挳(鎸夐挳淇℃伅) {
    if (!鎸夐挳淇℃伅 || !鎸夐挳淇℃伅.鏀惰棌鎸夐挳) {
        console.log("鏈彁渚涙敹钘忔寜閽俊鎭紝鏃犳硶鐐瑰嚮");
        return false;
    }
    
    let x = 鎸夐挳淇℃伅.鏀惰棌鎸夐挳.鍧愭爣.涓績X;
    let y = 鎸夐挳淇℃伅.鏀惰棌鎸夐挳.鍧愭爣.涓績Y;
    
    console.log(`鐐瑰嚮鏀惰棌鎸夐挳: (${x}, ${y})`);
    return 鐐瑰嚮(x, y);
}

// 涓诲嚱鏁?
function main() {
    console.log("===== 寮€濮嬫祴璇?" + (鎿嶄綔妯″紡 === 1 ? "鏃犻殰纰? : "Root Shell") + " 妯″紡 =====");
    
    // 灏濊瘯鍏抽棴瓒呯骇鐢ㄦ埛鏉冮檺鎻愮ず
    鍏抽棴瓒呯骇鐢ㄦ埛鏉冮檺鎻愮ず();
    
    // 鎵撳嵃褰撳墠鐣岄潰鐨勬墍鏈夋枃鏈厓绱?
    if (鎿嶄綔妯″紡 === 2) {
        鎵撳嵃鎵€鏈夋枃鏈厓绱?);
    } else {
        console.log("鏃犻殰纰嶆ā寮忎笅锛屼娇鐢ㄥ唴缃嚱鏁拌幏鍙栫晫闈㈠厓绱?);
    }
    
    console.log("\n===== 娴嬭瘯瀹屾垚 =====");
}

/**
 * 甯哥敤鎿嶆帶鍑芥暟灏佽 - 浣跨敤 shell 鍛戒护鏇夸唬鏃犻殰纰嶆湇鍔?
 * 浠ヤ笅鍑芥暟閮戒娇鐢?root 鏉冮檺鎵ц shell 鍛戒护
 */

/**
 * 杩斿洖閿搷浣?
 * @returns {boolean} 鏄惁鎵ц鎴愬姛
 */
function 杩斿洖() {
    console.log("鎵ц杩斿洖鎿嶄綔");
    
    if (鎿嶄綔妯″紡 === 1) {
        // 鏃犻殰纰嶆ā寮?
        back();
        return true;
    } else {
        // Root Shell妯″紡
        let result = shell("su -c 'input keyevent 4'", true); // KEYCODE_BACK = 4
        let 鎴愬姛 = result.code === 0;
        console.log(鎴愬姛 ? "杩斿洖鎿嶄綔鎴愬姛" : "杩斿洖鎿嶄綔澶辫触: " + result.error);
        return 鎴愬姛;
    }
}

/**
 * 鍥炲埌涓婚〉
 * @returns {boolean} 鏄惁鎵ц鎴愬姛
 */
function 涓婚〉() {
    console.log("鎵ц鍥炲埌涓婚〉鎿嶄綔");
    
    if (鎿嶄綔妯″紡 === 1) {
        // 鏃犻殰纰嶆ā寮?
        home();
        return true;
    } else {
        // Root Shell妯″紡
        let result = shell("su -c 'input keyevent 3'", true); // KEYCODE_HOME = 3
        let 鎴愬姛 = result.code === 0;
        console.log(鎴愬姛 ? "涓婚〉鎿嶄綔鎴愬姛" : "涓婚〉鎿嶄綔澶辫触: " + result.error);
        return 鎴愬姛;
    }
}

/**
 * 鎵撳紑鏈€杩戜换鍔?
 * @returns {boolean} 鏄惁鎵ц鎴愬姛
 */
function 鏈€杩戜换鍔?) {
    console.log("鎵ц鎵撳紑鏈€杩戜换鍔℃搷浣?);
    
    if (鎿嶄綔妯″紡 === 1) {
        // 鏃犻殰纰嶆ā寮?
        recents();
        return true;
    } else {
        // Root Shell妯″紡
        let result = shell("su -c 'input keyevent 187'", true); // KEYCODE_APP_SWITCH = 187
        let 鎴愬姛 = result.code === 0;
        console.log(鎴愬姛 ? "鏈€杩戜换鍔℃搷浣滄垚鍔? : "鏈€杩戜换鍔℃搷浣滃け璐? " + result.error);
        return 鎴愬姛;
    }
}

/**
 * 鐐瑰嚮灞忓箷
 * @param {number} x - X 鍧愭爣
 * @param {number} y - Y 鍧愭爣
 * @returns {boolean} 鏄惁鎵ц鎴愬姛
 */
function 鐐瑰嚮(x, y) {
    console.log("鎵ц鐐瑰嚮鎿嶄綔: (" + x + ", " + y + ")");
    
    if (鎿嶄綔妯″紡 === 1) {
        // 鏃犻殰纰嶆ā寮?
        click(x, y);
        return true;
    } else {
        // Root Shell妯″紡
        let result = shell("su -c 'input tap " + x + " " + y + "'", true);
        let 鎴愬姛 = result.code === 0;
        console.log(鎴愬姛 ? "鐐瑰嚮鎿嶄綔鎴愬姛" : "鐐瑰嚮鎿嶄綔澶辫触: " + result.error);
        return 鎴愬姛;
    }
}

/**
 * 闀挎寜灞忓箷
 * @param {number} x - X 鍧愭爣
 * @param {number} y - Y 鍧愭爣
 * @param {number} 鏃堕暱 - 闀挎寜鏃堕暱(姣)锛岄粯璁?000ms
 * @returns {boolean} 鏄惁鎵ц鎴愬姛
 */
function 闀挎寜(x, y, 鏃堕暱 = 1000) {
    console.log("鎵ц闀挎寜鎿嶄綔: (" + x + ", " + y + "), 鏃堕暱: " + 鏃堕暱 + "ms");
    
    if (鎿嶄綔妯″紡 === 1) {
        // 鏃犻殰纰嶆ā寮?
        press(x, y, 鏃堕暱);
        return true;
    } else {
        // Root Shell妯″紡 - 浣跨敤 swipe 鍛戒护鍦ㄥ悓涓€浣嶇疆鍋滅暀鏉ユā鎷熼暱鎸?
        let result = shell("su -c 'input swipe " + x + " " + y + " " + x + " " + y + " " + 鏃堕暱 + "'", true);
        let 鎴愬姛 = result.code === 0;
        console.log(鎴愬姛 ? "闀挎寜鎿嶄綔鎴愬姛" : "闀挎寜鎿嶄綔澶辫触: " + result.error);
        return 鎴愬姛;
    }
}

/**
 * 婊戝姩灞忓箷
 * @param {number} 璧风偣x - 璧风偣X鍧愭爣
 * @param {number} 璧风偣y - 璧风偣Y鍧愭爣
 * @param {number} 缁堢偣x - 缁堢偣X鍧愭爣
 * @param {number} 缁堢偣y - 缁堢偣Y鍧愭爣
 * @param {number} 鏃堕暱 - 婊戝姩鏃堕暱(姣)锛岄粯璁?00ms
 * @returns {boolean} 鏄惁鎵ц鎴愬姛
 */
function 婊戝姩(璧风偣x, 璧风偣y, 缁堢偣x, 缁堢偣y, 鏃堕暱 = 500) {
    console.log("鎵ц婊戝姩鎿嶄綔: (" + 璧风偣x + ", " + 璧风偣y + ") -> (" + 缁堢偣x + ", " + 缁堢偣y + "), 鏃堕暱: " + 鏃堕暱 + "ms");
    
    if (鎿嶄綔妯″紡 === 1) {
        // 鏃犻殰纰嶆ā寮?
        swipe(璧风偣x, 璧风偣y, 缁堢偣x, 缁堢偣y, 鏃堕暱);
        return true;
    } else {
        // Root Shell妯″紡
        let result = shell("su -c 'input swipe " + 璧风偣x + " " + 璧风偣y + " " + 缁堢偣x + " " + 缁堢偣y + " " + 鏃堕暱 + "'", true);
        let 鎴愬姛 = result.code === 0;
        console.log(鎴愬姛 ? "婊戝姩鎿嶄綔鎴愬姛" : "婊戝姩鎿嶄綔澶辫触: " + result.error);
        return 鎴愬姛;
    }
}

/**
 * 涓婃粦灞忓箷
 * @param {number} 璺濈 - 婊戝姩璺濈锛岄粯璁ゅ睆骞曢珮搴︾殑1/3
 * @param {number} 鏃堕暱 - 婊戝姩鏃堕暱(姣)锛岄粯璁?00ms
 * @returns {boolean} 鏄惁鎵ц鎴愬姛
 */
function 涓婃粦(璺濈 = null, 鏃堕暱 = 500) {
    let 灞忓箷瀹藉害 = device.width;
    let 灞忓箷楂樺害 = device.height;
    
    // 濡傛灉鏈寚瀹氳窛绂伙紝榛樿涓哄睆骞曢珮搴︾殑1/3
    璺濈 = 璺濈 || Math.floor(灞忓箷楂樺害 / 3);
    
    let 璧风偣x = Math.floor(灞忓箷瀹藉害 / 2);
    let 璧风偣y = Math.floor(灞忓箷楂樺害 * 0.7);
    let 缁堢偣y = 璧风偣y - 璺濈;
    
    return 婊戝姩(璧风偣x, 璧风偣y, 璧风偣x, 缁堢偣y, 鏃堕暱);
}

/**
 * 涓嬫粦灞忓箷
 * @param {number} 璺濈 - 婊戝姩璺濈锛岄粯璁ゅ睆骞曢珮搴︾殑1/3
 * @param {number} 鏃堕暱 - 婊戝姩鏃堕暱(姣)锛岄粯璁?00ms
 * @returns {boolean} 鏄惁鎵ц鎴愬姛
 */
function 涓嬫粦(璺濈 = null, 鏃堕暱 = 500) {
    let 灞忓箷瀹藉害 = device.width;
    let 灞忓箷楂樺害 = device.height;
    
    // 濡傛灉鏈寚瀹氳窛绂伙紝榛樿涓哄睆骞曢珮搴︾殑1/3
    璺濈 = 璺濈 || Math.floor(灞忓箷楂樺害 / 3);
    
    let 璧风偣x = Math.floor(灞忓箷瀹藉害 / 2);
    let 璧风偣y = Math.floor(灞忓箷楂樺害 * 0.3);
    let 缁堢偣y = 璧风偣y + 璺濈;
    
    return 婊戝姩(璧风偣x, 璧风偣y, 璧风偣x, 缁堢偣y, 鏃堕暱);
}

/**
 * 宸︽粦灞忓箷
 * @param {number} 璺濈 - 婊戝姩璺濈锛岄粯璁ゅ睆骞曞搴︾殑1/3
 * @param {number} 鏃堕暱 - 婊戝姩鏃堕暱(姣)锛岄粯璁?00ms
 * @returns {boolean} 鏄惁鎵ц鎴愬姛
 */
function 宸︽粦(璺濈 = null, 鏃堕暱 = 500) {
    let 灞忓箷瀹藉害 = device.width;
    let 灞忓箷楂樺害 = device.height;
    
    // 濡傛灉鏈寚瀹氳窛绂伙紝榛樿涓哄睆骞曞搴︾殑1/3
    璺濈 = 璺濈 || Math.floor(灞忓箷瀹藉害 / 3);
    
    let 璧风偣y = Math.floor(灞忓箷楂樺害 / 2);
    let 璧风偣x = Math.floor(灞忓箷瀹藉害 * 0.7);
    let 缁堢偣x = 璧风偣x - 璺濈;
    
    return 婊戝姩(璧风偣x, 璧风偣y, 缁堢偣x, 璧风偣y, 鏃堕暱);
}

/**
 * 鍙虫粦灞忓箷
 * @param {number} 璺濈 - 婊戝姩璺濈锛岄粯璁ゅ睆骞曞搴︾殑1/3
 * @param {number} 鏃堕暱 - 婊戝姩鏃堕暱(姣)锛岄粯璁?00ms
 * @returns {boolean} 鏄惁鎵ц鎴愬姛
 */
function 鍙虫粦(璺濈 = null, 鏃堕暱 = 500) {
    let 灞忓箷瀹藉害 = device.width;
    let 灞忓箷楂樺害 = device.height;
    
    // 濡傛灉鏈寚瀹氳窛绂伙紝榛樿涓哄睆骞曞搴︾殑1/3
    璺濈 = 璺濈 || Math.floor(灞忓箷瀹藉害 / 3);
    
    let 璧风偣y = Math.floor(灞忓箷楂樺害 / 2);
    let 璧风偣x = Math.floor(灞忓箷瀹藉害 * 0.3);
    let 缁堢偣x = 璧风偣x + 璺濈;
    
    return 婊戝姩(璧风偣x, 璧风偣y, 缁堢偣x, 璧风偣y, 鏃堕暱);
}

/**
 * 杈撳叆鏂囨湰
 * @param {string} 鏂囨湰 - 瑕佽緭鍏ョ殑鏂囨湰
 * @returns {boolean} 鏄惁鎵ц鎴愬姛
 */
function 杈撳叆鏂囨湰(鏂囨湰) {
    console.log("鎵ц杈撳叆鏂囨湰鎿嶄綔: " + 鏂囨湰);
    
    if (鎿嶄綔妯″紡 === 1) {
        // 鏃犻殰纰嶆ā寮?
        input(鏂囨湰);
        return true;
    } else {
        // Root Shell妯″紡
        let result = shell("su -c 'input text \"" + 鏂囨湰.replace(/"/g, '\\"') + "\"'", true);
        let 鎴愬姛 = result.code === 0;
        console.log(鎴愬姛 ? "杈撳叆鏂囨湰鎴愬姛" : "杈撳叆鏂囨湰澶辫触: " + result.error);
        return 鎴愬姛;
    }
}

/**
 * 鎸変笅鎸夐敭
 * @param {number} 鎸夐敭鐮?- 鎸夐敭鐨刱eycode
 * @returns {boolean} 鏄惁鎵ц鎴愬姛
 */
function 鎸夐敭(鎸夐敭鐮? {
    console.log("鎵ц鎸夐敭鎿嶄綔: " + 鎸夐敭鐮?;
    
    if (鎿嶄綔妯″紡 === 1) {
        // 鏃犻殰纰嶆ā寮?
        keycode(鎸夐敭鐮?;
        return true;
    } else {
        // Root Shell妯″紡
        let result = shell("su -c 'input keyevent " + 鎸夐敭鐮?+ "'", true);
        let 鎴愬姛 = result.code === 0;
        console.log(鎴愬姛 ? "鎸夐敭鎿嶄綔鎴愬姛" : "鎸夐敭鎿嶄綔澶辫触: " + result.error);
        return 鎴愬姛;
    }
}

/**
 * 甯哥敤鎸夐敭鐮?
 */
const 鎸夐敭鐮?= {
    杩斿洖: 4,      // KEYCODE_BACK
    涓婚〉: 3,      // KEYCODE_HOME
    鑿滃崟: 82,     // KEYCODE_MENU
    鎼滅储: 84,     // KEYCODE_SEARCH
    鐢垫簮: 26,     // KEYCODE_POWER
    鐩告満: 27,     // KEYCODE_CAMERA
    鏈€杩戜换鍔? 187, // KEYCODE_APP_SWITCH
    闊抽噺鍔? 24,   // KEYCODE_VOLUME_UP
    闊抽噺鍑? 25,   // KEYCODE_VOLUME_DOWN
    闈欓煶: 164,    // KEYCODE_VOLUME_MUTE
    浜害鍔? 221,  // KEYCODE_BRIGHTNESS_UP
    浜害鍑? 220,  // KEYCODE_BRIGHTNESS_DOWN
    涓? 19,       // KEYCODE_DPAD_UP
    涓? 20,       // KEYCODE_DPAD_DOWN
    宸? 21,       // KEYCODE_DPAD_LEFT
    鍙? 22,       // KEYCODE_DPAD_RIGHT
    纭畾: 23,     // KEYCODE_DPAD_CENTER
    閫氳瘽: 5,      // KEYCODE_CALL
    鎸傛柇: 6,      // KEYCODE_ENDCALL
    閿佸睆: 223     // KEYCODE_SLEEP
};

/**
 * 鍚姩搴旂敤
 * @param {string} 鍖呭悕 - 搴旂敤鍖呭悕
 * @returns {boolean} 鏄惁鎵ц鎴愬姛
 */
function 鍚姩搴旂敤(鍖呭悕) {
    console.log("鍚姩搴旂敤: " + 鍖呭悕);
    
    if (鎿嶄綔妯″紡 === 1) {
        // 鏃犻殰纰嶆ā寮?
        app.launch(鍖呭悕);
        return true;
    } else {
        // Root Shell妯″紡
        let result = shell("su -c 'am start -n " + 鍖呭悕 + "/.MainActivity'", true);
        // 濡傛灉涓婇潰鐨勫懡浠ゅけ璐ワ紝灏濊瘯浣跨敤鏇撮€氱敤鐨勬柟寮?
        if (result.code !== 0) {
            result = shell("su -c 'monkey -p " + 鍖呭悕 + " -c android.intent.category.LAUNCHER 1'", true);
        }
        let 鎴愬姛 = result.code === 0;
        console.log(鎴愬姛 ? "鍚姩搴旂敤鎴愬姛" : "鍚姩搴旂敤澶辫触: " + result.error);
        return 鎴愬姛;
    }
}

/**
 * 鍏抽棴搴旂敤
 * @param {string} 鍖呭悕 - 搴旂敤鍖呭悕
 * @returns {boolean} 鏄惁鎵ц鎴愬姛
 */
function 鍏抽棴搴旂敤(鍖呭悕) {
    console.log("鍏抽棴搴旂敤: " + 鍖呭悕);
    
    if (鎿嶄綔妯″紡 === 1) {
        // 鏃犻殰纰嶆ā寮?
        app.openAppSetting(鍖呭悕);
        sleep(1000);
        let 寮鸿鍋滄 = text("寮鸿鍋滄").findOne(2000);
        if (寮鸿鍋滄) {
            寮鸿鍋滄.click();
            sleep(1000);
            let 纭 = text("纭畾").findOne(2000) || 
                      text("纭").findOne() || 
                      text("寮鸿鍋滄").findOne();
            if (纭) {
                纭.click();
                sleep(1000);
                back();
                return true;
            }
        }
        back();
        return false;
    } else {
        // Root Shell妯″紡
        let result = shell("su -c 'am force-stop " + 鍖呭悕 + "'", true);
        let 鎴愬姛 = result.code === 0;
        console.log(鎴愬姛 ? "鍏抽棴搴旂敤鎴愬姛" : "鍏抽棴搴旂敤澶辫触: " + result.error);
        return 鎴愬姛;
    }
}

/**
 * 鑾峰彇褰撳墠搴旂敤鍖呭悕
 * @returns {string} 褰撳墠搴旂敤鍖呭悕
 */
function 鑾峰彇褰撳墠搴旂敤() {
    console.log("鑾峰彇褰撳墠搴旂敤鍖呭悕");
    
    if (鎿嶄綔妯″紡 === 1) {
        // 鏃犻殰纰嶆ā寮?
        let 鍖呭悕 = currentPackage();
        console.log("褰撳墠搴旂敤鍖呭悕: " + 鍖呭悕);
        return 鍖呭悕;
    } else {
        // Root Shell妯″紡
        let result = shell("su -c 'dumpsys window | grep mCurrentFocus'", true);
        if (result.code === 0) {
            let match = result.result.match(/mCurrentFocus.+?{.+?(\S+)\/(\S+)}/);
            if (match) {
                console.log("褰撳墠搴旂敤鍖呭悕: " + match[1]);
                return match[1];
            }
        }
        console.log("鑾峰彇褰撳墠搴旂敤鍖呭悕澶辫触");
        return null;
    }
}

/**
 * 绛夊緟鎸囧畾鏃堕棿
 * @param {number} 姣 - 绛夊緟鏃堕棿(姣)
 */
function 绛夊緟(姣) {
    console.log("绛夊緟 " + 姣 + " 姣");
    sleep(姣);
}

/**
 * 妫€鏌ュ簲鐢ㄦ槸鍚﹀畨瑁?
 * @param {string} 鍖呭悕 - 搴旂敤鍖呭悕
 * @returns {boolean} 鏄惁宸插畨瑁?
 */
function 搴旂敤宸插畨瑁?鍖呭悕) {
    console.log("妫€鏌ュ簲鐢ㄦ槸鍚﹀畨瑁? " + 鍖呭悕);
    let result = shell("su -c 'pm list packages | grep " + 鍖呭悕 + "'", true);
    let 宸插畨瑁?= result.code === 0 && result.result.includes(鍖呭悕);
    console.log(宸插畨瑁?? "搴旂敤宸插畨瑁? : "搴旂敤鏈畨瑁?);
    return 宸插畨瑁?
}

/**
 * 鏍囪鏍稿績鍏冪礌
 * @param {Array} 鍏冪礌鍒楄〃 - 鍏冪礌鏁扮粍
 */
function 鏍囪鏍稿績鍏冪礌(鍏冪礌鍒楄〃) {
    let 灞忓箷楂樺害 = device.height;
    let 灞忓箷瀹藉害 = device.width;

    // 1. 鎵鹃《閮ㄧ敤鎴峰悕锛堝浘鏂囷級
    let 椤堕儴鐢ㄦ埛鍚?= null;
    for (let 鍏冪礌 of 鍏冪礌鍒楄〃) {
        if (
            鍏冪礌.鍧愭爣.涓績Y < 灞忓箷楂樺害 * 0.3 &&
            鍏冪礌.鏂囨湰.length >= 2 && 鍏冪礌.鏂囨湰.length <= 12 &&
            !/^\d+$/.test(鍏冪礌.鏂囨湰) &&
            !["鍏虫敞", "璇寸偣浠€涔?..", "璇勮", "鍙戝脊骞?, "鍒嗕韩"].some(k => 鍏冪礌.鏂囨湰.includes(k))
        ) {
            椤堕儴鐢ㄦ埛鍚?= 鍏冪礌;
            break;
        }
    }

    // 鍙湪瑙嗛鏃舵壘搴曢儴鏄电О鍜屼簰鍔ㄦ暟
    let 搴曢儴鐢ㄦ埛鍚?= null;
    let 鐐硅禐 = null, 鏀惰棌 = null, 璇勮 = null;

    if (!椤堕儴鐢ㄦ埛鍚? {
        // 1. 鎵?鍏虫敞"鎴?宸插叧娉?鎸夐挳
        let 鍏虫敞鎸夐挳 = 鍏冪礌鍒楄〃.find(e =>
            e.鍧愭爣.涓績Y > 灞忓箷楂樺害 * 0.5 &&
            (e.鏂囨湰 === "鍏虫敞" || e.鏂囨湰 === "宸插叧娉?)
        );

        // 2. 鏄电О鍊欓€?
        let 鏄电О鍊欓€?= 鍏冪礌鍒楄〃.filter(e =>
            e.鍧愭爣.涓績Y > 灞忓箷楂樺害 * 0.5 &&
            e.鏂囨湰.length >= 2 && e.鏂囨湰.length <= 12 &&
            !/^[\d.]+涓?$/.test(e.鏂囨湰) &&
            !["鍏虫敞", "璇寸偣浠€涔?..", "璇勮", "鍙戝脊骞?, "鍒嗕韩", "鐩稿叧鎼滅储", "璇剧▼鍜ㄨ"].some(k => e.鏂囨湰.includes(k)) &&
            !e.鏂囨湰.includes(":")
        );

        if (鏄电О鍊欓€?length > 0) {
            if (鍏虫敞鎸夐挳) {
                // 鍙€塜鍦ㄥ叧娉ㄦ寜閽乏渚х殑锛屼笖Y鏈€鎺ヨ繎
                let 宸︿晶鍊欓€?= 鏄电О鍊欓€?
                    .filter(e => e.鍧愭爣.涓績X < 鍏虫敞鎸夐挳.鍧愭爣.涓績X)
                    .sort((a, b) => {
                        // Y瓒婃帴杩戝叧娉ㄦ寜閽秺浼樺厛
                        let dyA = Math.abs(a.鍧愭爣.涓績Y - 鍏虫敞鎸夐挳.鍧愭爣.涓績Y);
                        let dyB = Math.abs(b.鍧愭爣.涓績Y - 鍏虫敞鎸夐挳.鍧愭爣.涓績Y);
                        if (dyA !== dyB) return dyA - dyB;
                        // X瓒婇潬杩戝叧娉ㄦ寜閽秺浼樺厛
                        return 鍏虫敞鎸夐挳.鍧愭爣.涓績X - a.鍧愭爣.涓績X - (鍏虫敞鎸夐挳.鍧愭爣.涓績X - b.鍧愭爣.涓績X);
                    });
                if (宸︿晶鍊欓€?length > 0) {
                    搴曢儴鐢ㄦ埛鍚?= 宸︿晶鍊欓€塠0];
                } else {
                    // 娌℃湁宸︿晶鐨勶紝閫€鍥炲師鏈塝鏈€澶鏈€灏?
                    鏄电О鍊欓€?sort((a, b) => {
                        if (b.鍧愭爣.涓績Y !== a.鍧愭爣.涓績Y) {
                            return b.鍧愭爣.涓績Y - a.鍧愭爣.涓績Y;
                        }
                        return a.鍧愭爣.涓績X - b.鍧愭爣.涓績X;
                    });
                    搴曢儴鐢ㄦ埛鍚?= 鏄电О鍊欓€塠0];
                }
            } else {
                // 娌℃湁鍏虫敞鎸夐挳锛岄€€鍥炲師鏈塝鏈€澶鏈€灏?
                鏄电О鍊欓€?sort((a, b) => {
                    if (b.鍧愭爣.涓績Y !== a.鍧愭爣.涓績Y) {
                        return b.鍧愭爣.涓績Y - a.鍧愭爣.涓績Y;
                    }
                    return a.鍧愭爣.涓績X - b.鍧愭爣.涓績X;
                });
                搴曢儴鐢ㄦ埛鍚?= 鏄电О鍊欓€塠0];
            }
        }

        // 3. 鐐硅禐/鏀惰棌/璇勮鏁帮細搴曢儴鍓╀笅鐨勬暟瀛楋紙鍚?涓?锛?
        let 搴曢儴鍏冪礌 = 鍏冪礌鍒楄〃.filter(e => e.鍧愭爣.涓績Y > 灞忓箷楂樺害 * 0.7);
        let 浜掑姩鏁板瓧 = 搴曢儴鍏冪礌.filter(e =>
            /^[\d.]+涓?$/.test(e.鏂囨湰)
        );
        // 鎺掗櫎鏄电О
        if (搴曢儴鐢ㄦ埛鍚? {
            浜掑姩鏁板瓧 = 浜掑姩鏁板瓧.filter(e => e.鏂囨湰 !== 搴曢儴鐢ㄦ埛鍚?鏂囨湰);
        }
        // 鎸塜鍧愭爣鎺掑簭
        浜掑姩鏁板瓧.sort((a, b) => a.鍧愭爣.涓績X - b.鍧愭爣.涓績X);
        鐐硅禐 = 浜掑姩鏁板瓧[0] || null;
        鏀惰棌 = 浜掑姩鏁板瓧[1] || null;
        璇勮 = 浜掑姩鏁板瓧[2] || null;
    } else {
        // 鍥炬枃椤靛簳閮ㄦ暟瀛楋紙绮惧噯锛氳鐐逛粈涔?..鍙充晶渚濇涓虹偣璧?鏀惰棌/璇勮锛?
        let 璇寸偣浠€涔?= 鍏冪礌鍒楄〃.find(e =>
            e.鏂囨湰 === "璇寸偣浠€涔?.." && e.鍧愭爣.涓績Y > 灞忓箷楂樺害 * 0.8
        );
        let 鍒嗙晫X = 璇寸偣浠€涔?? 璇寸偣浠€涔?鍧愭爣.涓績X : 0;
        let 搴曢儴鏁板瓧 = 鍏冪礌鍒楄〃.filter(e =>
            e.鍧愭爣.涓績Y > 灞忓箷楂樺害 * 0.8 &&
            (/^[\d.]+涓?/.test(e.鏂囨湰) || /^\d+$/.test(e.鏂囨湰) || ["鐐硅禐", "鏀惰棌", "璇勮", "璧?].includes(e.鏂囨湰)) &&
            e.鍧愭爣.涓績X > 鍒嗙晫X
        );
        搴曢儴鏁板瓧.sort((a, b) => a.鍧愭爣.涓績X - b.鍧愭爣.涓績X);
        鐐硅禐 = 搴曢儴鏁板瓧[0] || null;
        鏀惰棌 = 搴曢儴鏁板瓧[1] || null;
        璇勮 = 搴曢儴鏁板瓧[2] || null;
        // 濡傛灉涓烘枃鏈紝瑙嗕负0
        if (鐐硅禐 && (鐐硅禐.鏂囨湰 === "鐐硅禐" || 鐐硅禐.鏂囨湰 === "璧?)) 鐐硅禐.鏂囨湰 = "0";
        if (鏀惰棌 && 鏀惰棌.鏂囨湰 === "鏀惰棌") 鏀惰棌.鏂囨湰 = "0";
        if (璇勮 && 璇勮.鏂囨湰 === "璇勮") 璇勮.鏂囨湰 = "0";
    }

    // 鎵撳嵃鏍囪缁撴灉
    if (椤堕儴鐢ㄦ埛鍚? {
        鎵撳嵃鍏冪礌璇︾粏淇℃伅(椤堕儴鐢ㄦ埛鍚? "鍥炬枃鐢ㄦ埛鍚?);
    }
    if (搴曢儴鐢ㄦ埛鍚? {
        鎵撳嵃鍏冪礌璇︾粏淇℃伅(搴曢儴鐢ㄦ埛鍚? "鐢ㄦ埛鏄电О");
    }
    if (鐐硅禐) {
        鎵撳嵃鍏冪礌璇︾粏淇℃伅(鐐硅禐, "鐐硅禐鏁?);
    }
    if (鏀惰棌) {
        鎵撳嵃鍏冪礌璇︾粏淇℃伅(鏀惰棌, "鏀惰棌鏁?);
    }
    if (璇勮) {
        鎵撳嵃鍏冪礌璇︾粏淇℃伅(璇勮, "璇勮鏁?);
    }
    console.log(`鍐呭绫诲瀷: ${椤堕儴鐢ㄦ埛鍚?? "鍥炬枃" : "瑙嗛"}`);
    
    // 妫€鏌ユ槸鍚︽湁鍒嗕韩鎸夐挳
    let 鍒嗕韩鎸夐挳 = 鍏冪礌鍒楄〃.find(e => e.鏂囨湰 === "鍒嗕韩");
    
    // 鍙湁鍦ㄦ娴嬪埌鍒嗕韩鎸夐挳鏃舵墠杩涜鎸夐挳鐘舵€佹娴?
    if (鍒嗕韩鎸夐挳) {
        console.log("妫€娴嬪埌鍒嗕韩鎸夐挳锛岃繘琛屾寜閽姸鎬佹娴?);
        let 鎸夐挳淇℃伅 = 鑾峰彇浜や簰鎸夐挳浣嶇疆(鍏冪礌鍒楄〃);
        if (鎸夐挳淇℃伅) {
            let 鎸夐挳鐘舵€?= 鍒ゆ柇鎸夐挳鐘舵€?鎸夐挳淇℃伅);
            console.log("鎸夐挳鐘舵€?", JSON.stringify(鎸夐挳鐘舵€?);
        }
    } else {
        console.log("鏈娴嬪埌鍒嗕韩鎸夐挳锛岃烦杩囨寜閽姸鎬佹娴?);
    }
}

function 鍒ゆ柇鐐硅禐鏀惰棌棰滆壊(鐐硅禐, 鏀惰棌, 璇勮) {
    if (!鐐硅禐 && !鏀惰棌 && !璇勮) {
        console.log("鏈壘鍒扮偣璧?鏀惰棌/璇勮鍏冪礌锛屾棤娉曞垽鏂鑹?);
        return;
    }
    if (!requestScreenCapture()) {
        console.log("璇锋眰鎴浘鏉冮檺澶辫触");
        return;
    }
    let img = captureScreen();
    // 鐐硅禐icon
    if (鐐硅禐) {
        let iconX = 鐐硅禐.鍧愭爣.涓績X - 50;
        let iconY = 鐐硅禐.鍧愭爣.涓績Y + 2;
        let color = images.pixel(img, iconX, iconY);
        let r = colors.red(color), g = colors.green(color), b = colors.blue(color);
        console.log("鐐硅禐icon棰滆壊RGB:", r, g, b);
        if (colors.isSimilar(color, "#FF2D55", 40)) {
            console.log("褰撳墠涓恒€愬凡鐐硅禐銆戠姸鎬侊紙绾㈣壊锛?);
        } else {
            console.log("褰撳墠涓恒€愭湭鐐硅禐銆戠姸鎬侊紙鐏拌壊锛?);
        }
    }
    // 鏀惰棌icon
    if (鏀惰棌) {
        let iconX = 鏀惰棌.鍧愭爣.涓績X - 50;
        let iconY = 鏀惰棌.鍧愭爣.涓績Y + 2;
        let color = images.pixel(img, iconX, iconY);
        let r = colors.red(color), g = colors.green(color), b = colors.blue(color);
        console.log("鏀惰棌icon棰滆壊RGB:", r, g, b);
        if (colors.isSimilar(color, "#FFC93C", 40)) {
            console.log("褰撳墠涓恒€愬凡鏀惰棌銆戠姸鎬侊紙榛勮壊锛?);
        } else {
            console.log("褰撳墠涓恒€愭湭鏀惰棌銆戠姸鎬侊紙鐏拌壊锛?);
        }
    }
    // 璇勮icon锛堝闇€璇嗗埆锛屽彲浠跨収涓婇潰鍐欐硶锛?
    if (璇勮) {
        let iconX = 璇勮.鍧愭爣.涓績X - 50;
        let iconY = 璇勮.鍧愭爣.涓績Y + 2;
        let color = images.pixel(img, iconX, iconY);
        let r = colors.red(color), g = colors.green(color), b = colors.blue(color);
        console.log("璇勮icon棰滆壊RGB:", r, g, b);
        // 璇勮icon棰滆壊鍒ゆ柇鍙嚜瀹氫箟
    }
}

function 鎵撳嵃鍏冪礌璇︾粏淇℃伅(鍏冪礌, 鏍囩) {
    if (!鍏冪礌) return;
    let 灞炴€ф枃鏈?= '';
    if (鍏冪礌.鍘熷灞炴€? {
        for (let k in 鍏冪礌.鍘熷灞炴€? {
            灞炴€ф枃鏈?+= `${k}: ${鍏冪礌.鍘熷灞炴€k]}, `;
        }
        灞炴€ф枃鏈?= 灞炴€ф枃鏈?replace(/, $/, '');
    }
    console.log(`${鏍囩}: [${鍏冪礌.鏂囨湰}], 鍧愭爣: (${鍏冪礌.鍧愭爣.涓績X}, ${鍏冪礌.鍧愭爣.涓績Y})${灞炴€ф枃鏈?? ' | ' + 灞炴€ф枃鏈?: ''}`);
}

// 鎵ц涓诲嚱鏁?
main();

