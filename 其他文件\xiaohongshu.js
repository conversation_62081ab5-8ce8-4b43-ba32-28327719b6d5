// 声明使用UI模式
"ui";

// 在文件开头添加一个标志，表示是否是首次运行
let 是否首次运行 = true;

// 检查AutoJS环境
if (typeof auto === 'undefined') {
    throw new Error("请在AutoJS环境中运行此脚本");
}




/**
 * 从文本中提取数字
 * 优化版本：提高提取效率，减少日志输出
 * 
 * @param {string} text - 包含数字的文本
 * @returns {number|null} - 提取的数字，失败返回null
 */
function 提取数字(text) {
    if (!text) return null;
    
    try {
        // 先移除所有空格，确保能处理"点赞 1445"这种格式
        let cleanText = text.replace(/\s+/g, "");
        
        // 匹配数字部分（包括小数和千分位）
        let 数字匹配 = cleanText.match(/\d+(\.\d+)?/);
        if (数字匹配) {
            // 转换为数字
            let 提取结果 = parseFloat(数字匹配[0]);
            return 提取结果;
        }
        
        // 如果是纯数字文本，直接解析
        if (/^\d+$/.test(text)) {
            let 提取结果 = parseInt(text);
            return 提取结果;
        }
        
        return null;
    } catch (e) {
        return null;
    }
}

/**
 * 获取互动元素（点赞、收藏、评论）
 * 通过查找Button元素的desc属性获取互动信息
 * 优化版本：提高查询效率，减少日志输出
 * 
 * @returns {Object|null} - 包含点赞、收藏、评论信息的对象，获取失败返回null
 */
function 获取互动元素() {
    try {
        // 初始化返回结果
        let 结果 = {
            点赞: null,
            收藏: null,
            评论: null,
            点赞元素: null,
            收藏元素: null,
            评论元素: null,
            是否视频: false
        };
        
        // 获取屏幕尺寸
        let 屏幕宽度 = device.width;
        let 屏幕高度 = device.height;
        
        // 定义视频互动区域（通常在屏幕右侧）
        let 视频互动区域 = {
            left: Math.floor(屏幕宽度 * 0.7),  // 屏幕右侧30%区域
            top: Math.floor(屏幕高度 * 0.3),   // 从屏幕30%位置开始
            right: 屏幕宽度,
            bottom: 屏幕高度 * 0.8             // 到屏幕80%位置结束
        };
        
        // 定义普通互动区域（通常在屏幕下半部分）
        let 普通互动区域 = {
            left: 0,
            top: Math.floor(屏幕高度 * 0.5),  // 从屏幕中间开始
            right: 屏幕宽度,
            bottom: 屏幕高度
        };
        
        // 首先检查是否为视频页面 - 使用更高效的方法
        let 视频区域按钮 = className("android.widget.Button")
            .boundsInside(视频互动区域.left, 视频互动区域.top, 视频互动区域.right, 视频互动区域.bottom)
            .find();
        
        let 是否视频页面 = 视频区域按钮.length > 0;
        结果.是否视频 = 是否视频页面;
        
        // 根据页面类型选择互动区域
        let 互动区域 = 是否视频页面 ? 视频互动区域 : 普通互动区域;
        
        // 使用更精确的选择器直接查找互动元素
        // 1. 查找点赞元素
        let 点赞元素 = className("android.widget.Button")
            .boundsInside(互动区域.left, 互动区域.top, 互动区域.right, 互动区域.bottom)
            .descMatches(".*点赞.*")
            .findOne(500);
        
        if (点赞元素) {
            结果.点赞元素 = 点赞元素;
            let desc = 点赞元素.desc();
            结果.点赞 = 提取数字(desc);
        }
        
        // 2. 查找收藏元素
        let 收藏元素 = className("android.widget.Button")
            .boundsInside(互动区域.left, 互动区域.top, 互动区域.right, 互动区域.bottom)
            .descMatches(".*收藏.*")
            .findOne(500);
        
        if (收藏元素) {
            结果.收藏元素 = 收藏元素;
            let desc = 收藏元素.desc();
            结果.收藏 = 提取数字(desc);
        }
        
        // 3. 查找评论元素
        let 评论元素 = className("android.widget.Button")
            .boundsInside(互动区域.left, 互动区域.top, 互动区域.right, 互动区域.bottom)
            .descMatches(".*评论.*")
            .findOne(500);
        
        if (评论元素) {
            结果.评论元素 = 评论元素;
            let desc = 评论元素.desc();
            结果.评论 = 提取数字(desc);
        }
        
        // 如果在指定区域没有找到互动元素，尝试扩大搜索范围
        if (!结果.点赞元素 && !结果.收藏元素 && !结果.评论元素) {
            // 尝试在整个屏幕查找
            点赞元素 = className("android.widget.Button").descMatches(".*点赞.*").findOne(500);
            收藏元素 = className("android.widget.Button").descMatches(".*收藏.*").findOne(500);
            评论元素 = className("android.widget.Button").descMatches(".*评论.*").findOne(500);
            
            if (点赞元素) {
                结果.点赞元素 = 点赞元素;
                结果.点赞 = 提取数字(点赞元素.desc());
            }
            
            if (收藏元素) {
                结果.收藏元素 = 收藏元素;
                结果.收藏 = 提取数字(收藏元素.desc());
            }
            
            if (评论元素) {
                结果.评论元素 = 评论元素;
                结果.评论 = 提取数字(评论元素.desc());
            }
        }
        
        // 如果仍然没有找到互动元素，尝试使用text属性查找
        if (!结果.点赞元素 && !结果.收藏元素 && !结果.评论元素) {
            // 查找可能包含点赞、收藏、评论的文本
            let 点赞文本 = textMatches(".*点赞.*").findOne(300);
            let 收藏文本 = textMatches(".*收藏.*").findOne(300);
            let 评论文本 = textMatches(".*评论.*").findOne(300);
            
            if (点赞文本) {
                结果.点赞 = 提取数字(点赞文本.text());
            }
            
            if (收藏文本) {
                结果.收藏 = 提取数字(收藏文本.text());
            }
            
            if (评论文本) {
                结果.评论 = 提取数字(评论文本.text());
            }
        }
        
        // 视频页面特殊处理：如果没有找到点赞数，尝试查找纯数字文本
        if (是否视频页面 && 结果.点赞 === null) {
            let 可能的点赞数字 = textMatches("^\\d+$")
                .boundsInside(视频互动区域.left, 视频互动区域.top, 视频互动区域.right, 视频互动区域.bottom)
                .find();
            
            // 通常第一个纯数字是点赞数
            if (可能的点赞数字.length > 0) {
                for (let i = 0; i < 可能的点赞数字.length; i++) {
                    let 元素 = 可能的点赞数字[i];
                    let 文本 = 元素.text();
                    
                    // 尝试解析为数字
                    let 点赞数 = parseInt(文本);
                    if (!isNaN(点赞数)) {
                        结果.点赞 = 点赞数;
                        break;
                    }
                }
            }
        }
        
        // 检查是否找到任何互动元素
        if (结果.点赞 !== null || 结果.收藏 !== null || 结果.评论 !== null) {
            return 结果;
        }
        
        return null;
    } catch (e) {
        return null;
    }
}

/**
 * 全局变量，存储互动元素信息
 */
let 互动元素信息 = {
    点赞: null,
    收藏: null,
    评论: null,
    点赞元素: null,
    收藏元素: null,
    评论元素: null
};

/**
 * 等待指定时间
 * 
 * @param {number} 毫秒 - 等待的毫秒数
 */
function 等待(毫秒) {
    sleep(毫秒);
}

/**
 * 随机等待一段时间
 * 
 * @param {number} 最小毫秒 - 最小等待时间
 * @param {number} 最大毫秒 - 最大等待时间
 */
function 随机等待(最小毫秒, 最大毫秒) {
    let 等待时间 = 最小毫秒 + Math.floor(Math.random() * (最大毫秒 - 最小毫秒));
    console.log("随机等待 " + 等待时间 + " 毫秒");
    等待(等待时间);
}

/**
 * 处理权限弹窗
 * 检查并点击各种常见的权限按钮
 * 优化版：一次性获取所有按钮，避免多次等待
 * 
 * @returns {boolean} - 是否找到并点击了权限按钮
 */
function 处理权限弹窗() {
    console.log("检查权限弹窗...");
    
    // 常见的权限按钮文本
    const 权限按钮文本列表 = ["允许", "始终允许", "确定", "继续", "同意", "确认", "好的"];
    
    try {
        // 一次性获取所有文本元素，而不是一个一个查找
        let 所有文本元素 = textMatches(".*").find();
        console.log(`找到 ${所有文本元素.length} 个文本元素`);
        
        // 遍历所有文本元素，寻找匹配的按钮
        for (let i = 0; i < 所有文本元素.length; i++) {
            let 元素 = 所有文本元素[i];
            let 文本 = 元素.text();
            
            if (!文本) continue;
            
            // 检查是否匹配任何权限按钮文本
            if (权限按钮文本列表.includes(文本)) {
                console.log(`找到按钮 "${文本}"，尝试点击`);
                
                if (元素.clickable()) {
                    元素.click();
                    sleep(500);
                    return true;
                } else {
                    // 尝试点击元素中心位置
                    let 位置 = 元素.bounds();
                    if (位置) {
                        click(位置.centerX(), 位置.centerY());
                        console.log(`通过坐标点击: (${位置.centerX()}, ${位置.centerY()})`);
                        sleep(500);
                        return true;
                    }
                }
            }
        }
        
        console.log("未找到权限弹窗");
        return false;
    } catch (e) {
        console.error("处理权限弹窗出错: " + e.message);
        return false;
    }
}

/**
 * 检查账号异常提示
 * 检测是否出现需要更换账号的提示，如账号下线、登录过期等
 * 优化版本：减少查询次数，提高执行效率
 * 
 * @returns {boolean} - 是否检测到账号异常提示
 */
function 检查账号异常提示() {
    console.log("检查账号异常提示...");
    
    try {
        // 一次性获取页面上所有文本元素，避免多次查询
        let 所有文本元素 = textMatches(".*").find();
        console.log(`找到 ${所有文本元素.length} 个文本元素`);
        
        // 常见的账号异常关键词
        const 异常关键词 = [
            "账号下线", "下线提示", "登录过期", "请重新登录", 
            "其他设备登录", "账号已被冻结", "账号异常", "违反社区规定"
        ];
        
        // 登录页面关键词
        const 登录页面关键词 = [
            "手机号登录", "用户协议", "隐私政策", "一键登录", "注册"
        ];
        
        // 检查所有文本元素是否包含异常关键词
        for (let i = 0; i < 所有文本元素.length; i++) {
            let 文本内容 = 所有文本元素[i].text();
            if (!文本内容) continue;
            
            // 检查账号异常关键词
            for (let j = 0; j < 异常关键词.length; j++) {
                if (文本内容.includes(异常关键词[j])) {
                    console.log(`检测到账号异常提示: "${文本内容}" 包含关键词 "${异常关键词[j]}"`);
                    return true;
                }
            }
            
            // 检查登录页面关键词
            for (let j = 0; j < 登录页面关键词.length; j++) {
                if (文本内容.includes(登录页面关键词[j])) {
                    console.log(`检测到登录页面: "${文本内容}" 包含关键词 "${登录页面关键词[j]}"`);
                    return true;
                }
            }
        }
        
        // 检查是否同时存在"小红书"和"生活指南"文本，这是登录页面的特征
        let 有小红书文本 = false;
        let 有生活指南文本 = false;
        
        for (let i = 0; i < 所有文本元素.length; i++) {
            let 文本内容 = 所有文本元素[i].text();
            if (!文本内容) continue;
            
            if (文本内容.includes("小红书")) {
                有小红书文本 = true;
            }
            if (文本内容.includes("生活指南")) {
                有生活指南文本 = true;
            }
            
            // 如果两者都找到了，可以提前返回
            if (有小红书文本 && 有生活指南文本) {
                console.log("检测到登录页面: 同时存在'小红书'和'生活指南'文本");
                return true;
            }
        }
        
        // 检查"知道了"按钮特殊情况
        let 知道了按钮 = false;
        for (let i = 0; i < 所有文本元素.length; i++) {
            if (所有文本元素[i].text() === "知道了") {
                知道了按钮 = true;
                break;
            }
        }
        
        if (知道了按钮) {
            console.log("检测到'知道了'按钮，检查是否有账号异常提示");
            
            // 再次遍历文本元素，查找可能的异常提示
            for (let i = 0; i < 所有文本元素.length; i++) {
                let 文本内容 = 所有文本元素[i].text();
                if (!文本内容 || 文本内容 === "知道了") continue;
                
                // 检查是否包含下线、登录、账号等关键词
                if (文本内容.includes("下线") || 
                    文本内容.includes("登录") || 
                    文本内容.includes("账号") ||
                    文本内容.includes("异常")) {
                    console.log(`检测到可能的账号异常提示: "${文本内容}"`);
                    return true;
                }
            }
        }
        
        // 如果上述检查都未发现异常，则认为没有账号异常
        console.log("未检测到账号异常提示");
        return false;
    } catch (e) {
        console.error("检查账号异常提示出错: " + e.message);
        // 出错时返回false，避免误判
        return false;
    }
}

// 在适当的位置调用检查账号异常提示函数
// 例如，可以在打开小红书、打开链接后调用

/**
 * 打开小红书应用
 */
function 打开小红书() {
    console.log("打开小红书应用");
    let 应用包名 = "com.xingin.xhs";
    
    try {
        app.launch(应用包名);
        等待(3000); // 减少等待时间，从5000ms降到3000ms
        
        // 处理可能出现的权限弹窗
        处理权限弹窗();
        
        console.log("小红书应用已启动");
        return true;
    } catch (e) {
        console.error("打开小红书失败: " + e.message);
        return false;
    }
}

/**
 * 返回主界面
 * 多次按返回键，直到回到主界面
 * 优化版：减少查询次数和等待时间
 * 
 * @returns {boolean} - 是否成功返回主界面
 */
function 返回主界面() {
    console.log("返回主界面");
    
    // 首页可能出现的关键词
    const 首页关键词 = ["首页", "发现", "关注", "直播", "短剧", "附近", "热门", "消息", "我", "推荐", "穿搭", "情感"];
    
    // 最多按5次返回键
    for (let i = 0; i < 5; i++) {
        // 检查是否已在主界面
        let 找到的关键词 = 0;
        
        try {
            // 查找所有文本元素，减少超时时间
            let 所有文本 = textMatches(".*").find();
            
            // 只记录找到的元素数量，不输出日志
            
            // 检查有多少关键词存在
            for (let j = 0; j < 所有文本.length; j++) {
                let 文本内容 = 所有文本[j].text();
                if (!文本内容) continue;
                
                // 使用some方法更高效地检查是否包含任何关键词
                if (首页关键词.some(关键词 => 文本内容.includes(关键词))) {
                    找到的关键词++;
                    
                    // 如果找到3个或以上关键词，认为已在首页（降低标准以加快判断）
                    if (找到的关键词 >= 4) { // 降低标准，从3个降到2个
                        console.log(`已找到 ${找到的关键词} 个首页关键词，确认在首页`);
                        return true;
                    }
                }
            }
        } catch (e) {
            console.error("检查首页元素出错: " + e.message);
        }
        
        // 按返回键
        console.log("按返回键");
        back();
        等待(1000); // 减少等待时间，从1500ms降到1000ms
    }
    
    // 最后检查一次
    let 找到的关键词 = 0;
    let 所有文本 = textMatches(".*").find();
    for (let j = 0; j < 所有文本.length; j++) {
        let 文本内容 = 所有文本[j].text();
        if (!文本内容) continue;
        
        if (首页关键词.some(关键词 => 文本内容.includes(关键词))) {
            找到的关键词++;
        }
    }
    
    if (找到的关键词 >= 3) {
        console.log(`已找到 ${找到的关键词} 个首页关键词，确认在首页`);
        return true;
    }
    
    console.log("未能确认返回首页");
    return false;
}

/**
 * 点击首篇文章
 * 
 * @returns {boolean} - 是否成功点击
 */
function 点击首篇文章() {
    console.log("点击首页文章");
    
    try {
        // 等待首页加载
        等待(3000);
        
        // 随机化点击坐标
        let x = 250 + Math.floor(Math.random() * 40) - 20;  // 250 ± 20
        let y = 500 + Math.floor(Math.random() * 40) - 20;  // 500 ± 20
        
        console.log(`点击坐标: (${x}, ${y})`);
        click(x, y);
        
        // 等待文章加载
        等待(5000);
        
        return true;
    } catch (e) {
        console.error("点击首篇文章出错: " + e.message);
        return false;
    }
}

/**
 * 打开链接
 * 
 * @param {string} 链接 - 要打开的链接
 * @returns {boolean} - 是否成功打开
 */
function 打开链接(链接) {
    console.log("打开链接: " + 链接);
    
    try {
        // 直接使用在浏览器检测App内打开方法
        return 在浏览器检测App内打开(链接);
    } catch (e) {
        console.error("打开链接出错: " + e.message);
        return false;
    }
}


function 在浏览器检测App内打开(链接) {
    console.log("准备打开链接: " + 链接);
    
    // 先关闭可能运行中的小红书应用
    关闭小红书();
    
    // 使用浏览器打开链接
    console.log("使用浏览器打开链接");
    app.openUrl(链接);
    toast("已用浏览器打开链接");
    
    // 等待1秒，处理可能出现的打开方式弹窗
    sleep(1000);
    处理打开方式弹窗();
    
    // 最大尝试次数和超时时间
    const 最大尝试次数 = 5;
    const 每次尝试间隔 = 1000; // 1秒
    const 总超时时间 = 20000; // 20秒
    
    // 记录开始时间
    const 开始时间 = new Date().getTime();
    let 尝试次数 = 0;
    
    while (尝试次数 < 最大尝试次数 && (new Date().getTime() - 开始时间) < 总超时时间) {
        尝试次数++;
        console.log(`第 ${尝试次数} 次尝试打开小红书...`);
        
        // 等待页面加载
        sleep(每次尝试间隔);
        
        // 处理可能出现的权限弹窗
        处理权限弹窗();
        
        // 检查是否已经在小红书App中
        let pkg = typeof currentPackage === "function" ? currentPackage() : "";
        if (pkg === "com.xingin.xhs") {
            console.log("已成功打开小红书App");
            return true;
        }
        
        // 检查是否有"App内打开"按钮
        let appButton = text('App内打开').findOne(1000);
        
        if (appButton) {
            console.log("找到'App内打开'按钮，点击");
            
            // 获取按钮的坐标位置
            let bounds = appButton.bounds();
            if (bounds) {
                // 使用坐标点击中心位置
                let centerX = bounds.centerX();
                let centerY = bounds.centerY();
                console.log(`点击坐标: (${centerX}, ${centerY})`);
                click(centerX, centerY);
            } else {
                // 如果获取坐标失败，尝试直接点击控件
                appButton.click();
            }
            
            toast("已点击'App内打开'按钮");
            
            // 等待小红书App启动
            sleep(2000);
            
            // 再次检查是否已经在小红书App中
            pkg = typeof currentPackage === "function" ? currentPackage() : "";
            if (pkg === "com.xingin.xhs") {
                console.log("已成功打开小红书App");
                return true;
            }
        } else {
            console.log("未找到'App内打开'按钮，检查其他可能的按钮...");
            
            const 可能的按钮文本 = ["浏览器","Chrome", "打开APP", "在不登录账号的情况下使用"];
            for (let i = 0; i < 可能的按钮文本.length; i++) {
                let 按钮文本 = 可能的按钮文本[i];
                let 其他按钮 = text(按钮文本).findOne(500);
                
                if (其他按钮) {
                    console.log(`找到'${按钮文本}'按钮，尝试通过坐标点击`);
                    
                    // 获取按钮的坐标位置
                    let bounds = 其他按钮.bounds();
                    if (bounds) {
                        // 使用坐标点击中心位置
                        let centerX = bounds.centerX();
                        let centerY = bounds.centerY();
                        console.log(`点击坐标: (${centerX}, ${centerY})`);
                        click(centerX, centerY);
                    } else {
                        // 如果获取坐标失败，尝试直接点击控件
                        其他按钮.click();
                        console.log("通过控件直接点击");
                    }
                    
                    sleep(1500);
                    break;
                }
            }
            
            // 尝试查找"始终"按钮并点击
            let 始终按钮 = text("始终").findOne(1000);
            if (始终按钮) {
                console.log("找到'始终'按钮，尝试点击");
                
                let bounds = 始终按钮.bounds();
                if (bounds) {
                    // 使用坐标点击中心位置
                    let centerX = bounds.centerX();
                    let centerY = bounds.centerY();
                    click(centerX, centerY);
                    console.log(`点击'始终'按钮坐标: (${centerX}, ${centerY})`);
                } else {
                    始终按钮.click();
                }
                
                sleep(1000);
            }
            
            // 如果常规方法都失败了，尝试自动扫描屏幕点击
            if (尝试次数 >= 3) {
                console.log("常规方法未能打开小红书，尝试自动扫描屏幕点击...");
                let 扫描结果 = 自动扫描屏幕点击浏览器按钮();
                if (扫描结果) {
                    console.log("自动扫描屏幕点击成功！");
                    return true;
                }
            }
        }
    }
    
    // 最后检查一次
    let pkg = typeof currentPackage === "function" ? currentPackage() : "";
    if (pkg === "com.xingin.xhs") {
        console.log("最终确认：已成功打开小红书App");
        return true;
    } else {
        console.log("多次尝试后未能自动进入小红书App");
        
        // 最后的尝试：全屏扫描点击
        console.log("最后尝试：全屏扫描点击");
        let 扫描结果 = 自动扫描屏幕点击浏览器按钮();
        if (扫描结果) {
            console.log("最后尝试成功打开小红书！");
            return true;
        }
        
        toast("请手动点击'App内打开'按钮");
        
        // 给用户一些时间手动操作
        sleep(5000);
        
        // 再次检查
        pkg = typeof currentPackage === "function" ? currentPackage() : "";
        return pkg === "com.xingin.xhs";
    }
}

/**
 * 处理打开方式弹窗
 * 自动点击浏览器选项，并选择"始终"
 * 优化版：使用坐标点击
 * 
 * @returns {boolean} - 是否成功处理
 */
function 处理打开方式弹窗() {
    console.log("检查打开方式弹窗...");
    
    try {
        // 查找"打开方式"文本
        let 打开方式文本 = text("打开方式").findOne(2000);
        if (!打开方式文本) {
            console.log("没有检测到打开方式弹窗");
            return false;
        }
        
        console.log("检测到打开方式弹窗");
        
        // 查找浏览器选项 
        let 浏览器列表 = textMatches(/.*(浏览器|Browser|Chrome|展开|Firefox|Edge).*/).find();
        if (浏览器列表.length > 0) {
            console.log(`找到${浏览器列表.length}个浏览器选项`);
            
            // 优先选择默认浏览器
            let 浏览器 = 浏览器列表[0];
            
            // 使用坐标点击
            let bounds = 浏览器.bounds();
            if (bounds) {
                let centerX = bounds.centerX();
                let centerY = bounds.centerY();
                console.log(`点击浏览器坐标: (${centerX}, ${centerY})`);
                click(centerX, centerY);
            } else {
                // 如果获取坐标失败，尝试通过父元素点击
                浏览器.parent().click();
            }
            
            console.log(`已点击: ${浏览器.text()}`);
            
            // 等待并点击"始终"按钮
            sleep(500);
            let 始终按钮 = text("始终").findOne(1000);
            if (始终按钮) {
                let 始终bounds = 始终按钮.bounds();
                if (始终bounds) {
                    let centerX = 始终bounds.centerX();
                    let centerY = 始终bounds.centerY();
                    console.log(`点击"始终"按钮坐标: (${centerX}, ${centerY})`);
                    click(centerX, centerY);
                } else {
                    始终按钮.click();
                }
                console.log("已点击'始终'按钮");
                return true;
            } else {
                let 仅一次按钮 = text("仅一次").findOne(1000);
                if (仅一次按钮) {
                    let 仅一次bounds = 仅一次按钮.bounds();
                    if (仅一次bounds) {
                        let centerX = 仅一次bounds.centerX();
                        let centerY = 仅一次bounds.centerY();
                        console.log(`点击"仅一次"按钮坐标: (${centerX}, ${centerY})`);
                        click(centerX, centerY);
                    } else {
                        仅一次按钮.click();
                    }
                    console.log("未找到'始终'按钮，点击'仅一次'");
                    return true;
                }
            }
        } else {
            console.log("未找到明确的浏览器选项，尝试通用方法");
            
            // 如果没找到浏览器，尝试点击位于屏幕上部的选项
            // 一般打开方式弹窗，第一个选项通常在屏幕中上部位置
            let 屏幕宽度 = device.width;
            let 屏幕高度 = device.height;
            
            // 点击屏幕左上部位置（通常是第一个选项）
            let x = 屏幕宽度 * 0.25;
            let y = 屏幕高度 * 0.3;
            console.log(`尝试点击屏幕左上部位置: (${x}, ${y})`);
            click(x, y);
            
            // 等待并点击"始终"按钮
            sleep(500);
            let 始终按钮 = text("始终").findOne(1000);
            if (始终按钮) {
                let 始终bounds = 始终按钮.bounds();
                if (始终bounds) {
                    let centerX = 始终bounds.centerX();
                    let centerY = 始终bounds.centerY();
                    console.log(`点击"始终"按钮坐标: (${centerX}, ${centerY})`);
                    click(centerX, centerY);
                } else {
                    始终按钮.click();
                }
                return true;
            }
            
            // 如果上述尝试失败，尝试点击屏幕下部位置（通常是确定按钮）
            y = 屏幕高度 * 0.9;
            console.log(`尝试点击确认按钮位置: (${屏幕宽度 * 0.75}, ${y})`);
            click(屏幕宽度 * 0.75, y);
        }
        
        return false;
    } catch (e) {
        console.error("处理打开方式弹窗出错: " + e.message);
        return false;
    }
}

/**
 * 从URL中提取笔记ID
 * 支持短链接、长链接和直接的笔记ID
 * 
 * @param {string} url - 小红书URL或笔记ID
 * @returns {string|null} - 笔记ID，失败返回null
 */
function 提取笔记ID(url) {
    try {
        console.log("开始提取笔记ID...");
        
        // 确保url是字符串类型
        url = String(url);
        
        // 如果链接为空
        if (!url) {
            console.error("无效的链接");
            return null;
        }
        
        // 情况1: 直接是笔记ID
        if (/^[a-zA-Z0-9_]+$/.test(url) && !url.includes(".")) {
            console.log("检测到笔记ID，直接返回");
            return url;
        }
        
        // 情况2: 小红书长链接
        if (url.indexOf("xiaohongshu.com") !== -1) {
            console.log("检测到小红书长链接，提取ID");
            
            // 直接尝试匹配ID模式（通常是24位字符）
            let idMatch = url.match(/([a-zA-Z0-9]{24})/);
            if (idMatch && idMatch[1]) {
                console.log("通过ID模式匹配成功: " + idMatch[1]);
                return idMatch[1];
            }
            
            // 尝试提取 /explore/[noteId]
            let match = url.match(/\/explore\/([a-zA-Z0-9_]+)/);
            if (match && match[1]) {
                console.log("通过explore路径匹配成功: " + match[1]);
                return match[1];
            }
            
            // 尝试提取 /discovery/item/[noteId]
            match = url.match(/\/discovery\/item\/([a-zA-Z0-9_]+)/);
            if (match && match[1]) {
                // 如果ID包含参数，只取问号前面的部分
                let id = match[1].split('?')[0];
                console.log("通过discovery路径匹配成功: " + id);
                return id;
            }
            
            console.error("未能从长链接提取笔记ID");
            return null;
        }
        
        // 情况3: 小红书短链接，需要先解析
        if (url.indexOf("xhslink.com") !== -1) {
            console.log("检测到小红书短链接，开始解析...");
            
            try {
                // 发送HTTP请求获取重定向URL
                let response = http.get(url, {
                    followRedirect: true,  // 自动跟随重定向
                    headers: {
                        "User-Agent": "Mozilla/5.0 (Linux; Android 10; Redmi Note 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.104 Mobile Safari/537.36"
                    }
                });
                
                // 从最终URL中提取ID
                let finalUrl = response.url;
                console.log("短链接解析结果: " + finalUrl);
                
                // 再次调用提取笔记ID函数处理解析后的URL
                return 提取笔记ID(finalUrl);
            } catch (e) {
                console.error("解析短链接失败: " + e.message);
                return null;
            }
        }
        
        // 尝试直接从URL中提取24位ID格式
        let idMatch = url.match(/([a-zA-Z0-9]{24})/);
        if (idMatch && idMatch[1]) {
            console.log("从URL中直接提取到ID: " + idMatch[1]);
            return idMatch[1];
        }
        
        // 情况4: 其他未识别的链接类型
        console.error("未识别的链接类型，无法提取ID: " + url);
        return null;
    } catch (e) {
        console.error("提取笔记ID出错: " + e.message);
        return null;
    }
}

/**
 * 执行点赞操作
 * 
 * @returns {boolean} - 是否成功点赞
 */
function 执行点赞() {
    console.log("执行点赞操作");
    
    try {
        if (!互动元素信息.点赞元素) {
            console.log("未找到点赞元素");
            return false;
        }
        
        // 检查是否已点赞
        let desc = 互动元素信息.点赞元素.desc();
        if (desc && desc.includes("已点赞")) {
            console.log("已经点过赞了");
            return true;
        }
        
        // 点击点赞按钮
        console.log("点击点赞按钮");
        互动元素信息.点赞元素.click();
        等待(1000);
        
        return true;
    } catch (e) {
        console.error("执行点赞出错: " + e.message);
        return false;
    }
}

/**
 * 执行收藏操作
 * 
 * @returns {boolean} - 是否成功收藏
 */
function 执行收藏() {
    console.log("执行收藏操作");
    
    try {
        if (!互动元素信息.收藏元素) {
            console.log("未找到收藏元素");
            return false;
        }
        
        // 检查是否已收藏
        let desc = 互动元素信息.收藏元素.desc();
        if (desc && desc.includes("已收藏")) {
            console.log("已经收藏过了");
            return true;
        }
        
        // 点击收藏按钮
        console.log("点击收藏按钮");
        互动元素信息.收藏元素.click();
        等待(1000);
        
        return true;
    } catch (e) {
        console.error("执行收藏出错: " + e.message);
        return false;
    }
}

/**
 * 清除存储的设备令牌
 * 强制重新生成设备令牌
 */
function 清除设备令牌() {
    console.log("清除存储的设备令牌");
    storages.create("小红书操作").remove("设备令牌");
    API配置.设备令牌 = "";
    是否首次运行 = true;
}

/**
 * 获取特定配置项的值
 * 
 * @param {string} 配置名称 - 配置项名称
 * @param {any} 默认值 - 如果配置不存在，返回的默认值
 * @returns {any} - 配置项的值
 */
function 获取配置项(配置名称, 默认值) {
    // 先尝试直接从存储中获取
    let storage = storages.create("小红书操作");
    let 配置值 = storage.get(配置名称);
    
    // 如果直接存储中有值，直接返回
    if (配置值 !== undefined && 配置值 !== null) {
        console.log(`获取配置项 ${配置名称}: ${配置值}`);
        return 配置值;
    }
    
    // 尝试从所有配置中获取
    let 所有配置 = storage.get("所有配置");
    if (所有配置) {
        // 遍历所有配置项
        for (let 配置组名 in 所有配置) {
            let 配置组 = 所有配置[配置组名];
            // 如果配置组中包含指定的配置项
            if (配置组 && 配置组[配置名称] !== undefined) {
                console.log(`从配置组 ${配置组名} 中获取配置项 ${配置名称}: ${配置组[配置名称]}`);
                return 配置组[配置名称];
            }
        }
    }
    
    // 尝试从当前配置中获取
    let 当前配置 = storage.get("当前配置");
    if (当前配置 && 当前配置.config && 当前配置.config[配置名称] !== undefined) {
        console.log(`从当前配置中获取配置项 ${配置名称}: ${当前配置.config[配置名称]}`);
        return 当前配置.config[配置名称];
    }
    
    // 如果都没有找到，返回默认值
    console.log(`未找到配置项 ${配置名称}，使用默认值: ${默认值}`);
    return 默认值;
}



/**
 * 获取作者信息
 * 通过查找作者元素获取作者名称
 * 优化版本：提高查询效率，减少日志输出
 * 
 * @returns {string|null} - 作者名称，获取失败返回null
 */
function 获取作者信息() {
    try {
        // 查找包含"作者"的Button元素
        let 作者元素 = className("android.widget.Button").descMatches(".*作者.*").findOne(300);
        
        if (作者元素) {
            let desc = 作者元素.desc();
            
            // 从描述中提取作者名称
            let 作者名称匹配 = desc.match(/作者,(.+)/);
            if (作者名称匹配 && 作者名称匹配[1]) {
                let 作者名称 = 作者名称匹配[1].trim();
                return 作者名称;
            }
        }
        
        // 尝试其他方式查找作者信息
        // 1. 查找包含@的文本
        let 包含艾特的文本 = textMatches("@.*").findOne(300);
        if (包含艾特的文本) {
            return 包含艾特的文本.text();
        }
        
        // 2. 查找可能的作者名称（短文本，通常在页面上方）
        let 屏幕高度 = device.height;
        let 可能的作者元素列表 = className("android.widget.TextView")
            .boundsInside(0, 0, device.width, Math.floor(屏幕高度 * 0.3))
            .find();
        
        // 筛选可能的作者名称
        for (let i = 0; i < 可能的作者元素列表.length; i++) {
            let 元素 = 可能的作者元素列表[i];
            let 文本 = 元素.text();
            
            // 作者名称通常较短，且不包含特殊字符
            if (文本 && 文本.length > 0 && 文本.length < 20 && 
                !文本.includes("点赞") && !文本.includes("收藏") && 
                !文本.includes("评论") && !文本.match(/^\d+$/)) {
                return 文本;
            }
        }
        
        return null;
    } catch (e) {
        return null;
    }
}

/**
 * 启动UI界面
 * 显示设置窗口,允许用户配置链接文件名、最大操作数量和操作间隔时间
 */
function 启动UI界面() {
    // 创建UI布局
    ui.layout(
        <vertical padding="16">
            <text textSize="24sp" textColor="#FF5722" gravity="center" margin="0 0 0 16">小红书自动点赞工具</text>
            
            <text textSize="16sp" textColor="#666666" margin="0 16 0 0">链接文件名 (存放于/mnt/shared/Pictures/)</text>
            <input id="链接文件名" text="links.txt" hint="输入链接文件名" />
            
            <text textSize="16sp" textColor="#666666" margin="0 16 0 0">最大操作数量</text>
            <input id="最大操作数量" text="10" inputType="number" hint="输入最大操作数量" />
            
            <text textSize="16sp" textColor="#666666" margin="0 16 0 0">操作间隔时间 (秒)</text>
            <input id="操作间隔时间" text="5" inputType="number" hint="输入操作间隔时间(秒)" />
            
            <button id="开始按钮" text="开始操作" textColor="#ffffff" bg="#FF5722" margin="0 32 0 0" />
            
            <text id="状态文本" textSize="14sp" textColor="#333333" margin="0 16 0 0" />
        </vertical>
    );
    
    // 设置按钮点击事件
    ui.开始按钮.on("click", function() {
        let 链接文件名 = ui.链接文件名.getText().toString();
        let 最大操作数量 = parseInt(ui.最大操作数量.getText().toString());
        let 操作间隔时间 = parseInt(ui.操作间隔时间.getText().toString()) * 1000; // 转换为毫秒
        
        if(!链接文件名) {
            toast("请输入链接文件名");
            return;
        }
        
        if(isNaN(最大操作数量) || 最大操作数量 <= 0) {
            toast("请输入有效的最大操作数量");
            return;
        }
        
        if(isNaN(操作间隔时间) || 操作间隔时间 <= 0) {
            toast("请输入有效的操作间隔时间");
            return;
        }
        
        // 开始执行自动点赞操作
        threads.start(function() {
            执行自动点赞(链接文件名, 最大操作数量, 操作间隔时间);
        });
    });
}

/**
 * 文章信息全局变量
 * 用于存储当前文章的标题和用户名
 */
let 当前文章信息 = {
    标题: "",
    用户名: "",
    已获取: false
};

/**
 * 获取文章信息
 * 提取当前页面的文章标题和用户名
 * 
 * @returns {Object|null} - 包含标题和用户名的对象，失败返回null
 */
function 获取文章信息() {
    console.log("获取文章信息...");
    
    try {
        // 确保页面已完全加载
        等待(1500);
        
        let 文章信息 = {
            标题: "",
            用户名: ""
        };
        
        // 1. 尝试查找文章标题 - 使用多种方法
        
        // 方法1：尝试查找大字体标题
        let 大标题元素列表 = className("android.widget.TextView")
            .find()
            .filter(element => {
                let bounds = element.bounds();
                let text = element.text();
                // 标题通常位于屏幕上部且宽度较大，内容长度也较长
                return bounds && 
                       bounds.width() > device.width * 0.6 && 
                       bounds.top < device.height * 0.4 &&
                       text && text.length > 5;
            });
        
        // 按照文本长度排序，通常最长的是标题
        大标题元素列表.sort((a, b) => b.text().length - a.text().length);
        
        if (大标题元素列表.length > 0) {
            文章信息.标题 = 大标题元素列表[0].text();
            console.log(`通过大字体找到文章标题: "${文章信息.标题}"`);
        }
        
        // 方法2：如果方法1没找到，尝试查找任何较长的文本作为标题
        if (!文章信息.标题) {
            let 所有文本元素 = className("android.widget.TextView").find();
            let 长文本元素 = 所有文本元素.filter(元素 => {
                let 文本 = 元素.text();
                return 文本 && 文本.length > 10 && 
                       !文本.includes("点赞") && !文本.includes("收藏");
            });
            
            // 按长度排序
            长文本元素.sort((a, b) => b.text().length - a.text().length);
            
            if (长文本元素.length > 0) {
                文章信息.标题 = 长文本元素[0].text();
                console.log(`通过长文本找到可能的标题: "${文章信息.标题}"`);
            }
        }
        
        // 2. 尝试查找用户名 - 使用多种方法
        
        // 方法1：尝试查找作者按钮
        let 作者按钮 = className("android.widget.Button")
            .descMatches(".*作者.*")
            .findOne(300);
        
        if (作者按钮) {
            let desc = 作者按钮.desc();
            let 作者匹配 = desc.match(/作者[,，]\s*(.+)/);
            if (作者匹配 && 作者匹配[1]) {
                文章信息.用户名 = 作者匹配[1].trim();
                console.log(`通过作者按钮找到用户名: "${文章信息.用户名}"`);
            }
        }
        
        // 方法2: 如果方法1没找到，尝试查找包含@的文本
        if (!文章信息.用户名) {
            let 包含艾特的文本元素列表 = textMatches("@.*").find();
            
            if (包含艾特的文本元素列表.length > 0) {
                文章信息.用户名 = 包含艾特的文本元素列表[0].text();
                console.log(`通过@找到作者: "${文章信息.用户名}"`);
            }
        }
        
        // 方法3: 尝试查找屏幕上部短文本作为可能的用户名
        if (!文章信息.用户名) {
            let 可能的作者元素列表 = className("android.widget.TextView")
                .boundsInside(0, 0, device.width, Math.floor(device.height * 0.3))
                .find()
                .filter(元素 => {
                    let 文本 = 元素.text();
                    return 文本 && 
                           文本.length > 0 && 
                           文本.length < 20 && 
                           !文本.includes("点赞") && 
                           !文本.includes("收藏") && 
                           !文本.includes("评论") && 
                           !文本.includes("...") &&
                           !文本.match(/^\d+$/);
                });
            
            // 通常用户名较短
            可能的作者元素列表.sort((a, b) => a.text().length - b.text().length);
            
            if (可能的作者元素列表.length > 0) {
                文章信息.用户名 = 可能的作者元素列表[0].text();
                console.log(`通过短文本找到可能的作者: "${文章信息.用户名}"`);
            }
        }
        
        // 检查是否获取到了有用信息
        if (文章信息.标题 || 文章信息.用户名) {
            return 文章信息;
        }
        
        return null;
    } catch (e) {
        console.error("获取文章信息出错: " + e.message);
        return null;
    }
}

/**
 * 获取首页第一篇文章信息
 * 基于FrameLayout元素和desc属性获取标题和用户名
 * 
 * @returns {Object|null} - 包含标题和用户名的对象，失败返回null
 */
function 获取首页文章信息() {
    console.log("获取首页文章信息，基于FrameLayout元素...");
    
    try {
        let 文章信息 = {
            标题: "",
            用户名: ""
        };
        
        // 首先等待一下确保页面完全加载
        等待(2000);
        
        // 1. 查找所有FrameLayout元素，这些是首页的卡片框架
        console.log("查找首页FrameLayout元素...");
        let 框架元素列表 = className("android.widget.FrameLayout").find();
        console.log(`找到 ${框架元素列表.length} 个FrameLayout元素`);
        
        // 2. 筛选可能的文章卡片框架
        // 文章卡片通常有一定的高度和宽度，并且位于屏幕的可见区域内
        let 可能的卡片框架 = 框架元素列表.filter(元素 => {
            let 边界 = 元素.bounds();
            // 筛选适当大小的框架，一般卡片框架会占据较大屏幕空间
            return 边界 && 
                  边界.width() > device.width * 0.8 && // 宽度超过屏幕80%
                  边界.height() > device.height * 0.15 && // 高度适中
                  边界.height() < device.height * 0.6 && // 不会太高
                  边界.top >= 0 && // 在屏幕可见区域内
                  边界.top < device.height * 0.7 && // 不会太靠下
                  边界.bottom > device.height * 0.1; // 不会太靠上
        });
        
        console.log(`筛选出 ${可能的卡片框架.length} 个可能的文章卡片框架`);
        
        // 3. 按照位置排序，找出最靠上的几个框架（第一页的内容）
        可能的卡片框架.sort((a, b) => a.bounds().top - b.bounds().top);
        
        // 优先处理屏幕上方的框架（通常是第一篇文章）
        let 已处理的框架数 = 0;
        let 成功获取 = false;
        
        for (let i = 0; i < Math.min(4, 可能的卡片框架.length); i++) {
            let 框架 = 可能的卡片框架[i];
            console.log(`处理第 ${i+1} 个框架，位置: ${JSON.stringify(框架.bounds())}`);
            
            // 获取框架的desc属性，这里可能包含标题和用户名
            let desc描述 = 框架.desc();
            if (desc描述) {
                console.log(`框架desc: "${desc描述}"`);
                
                // 解析desc中的信息，格式可能是"标题-用户名"或其他格式
                let 解析结果 = 解析框架描述(desc描述);
                if (解析结果) {
                    文章信息.标题 = 解析结果.标题;
                    文章信息.用户名 = 解析结果.用户名;
                    console.log(`从框架desc成功解析: 标题="${文章信息.标题}", 用户名="${文章信息.用户名}"`);
                    成功获取 = true;
                    break;
                }
            }
            
            // 如果框架本身没有desc，尝试查找框架内的子元素
            console.log("尝试查找框架内的子元素...");
            let 框架内元素 = 框架.find(className("android.view.View").descMatches(".+"));
            
            if (框架内元素.length > 0) {
                console.log(`找到 ${框架内元素.length} 个带描述的子元素`);
                
                // 查看所有子元素的desc
                for (let j = 0; j < 框架内元素.length; j++) {
                    let 子元素描述 = 框架内元素[j].desc();
                    if (子元素描述) {
                        console.log(`子元素desc: "${子元素描述}"`);
                        let 解析结果 = 解析框架描述(子元素描述);
                        if (解析结果) {
                            if (!文章信息.标题 && 解析结果.标题) 文章信息.标题 = 解析结果.标题;
                            if (!文章信息.用户名 && 解析结果.用户名) 文章信息.用户名 = 解析结果.用户名;
                        }
                    }
                }
                
                if (文章信息.标题 || 文章信息.用户名) {
                    console.log(`从框架子元素成功解析: 标题="${文章信息.标题}", 用户名="${文章信息.用户名}"`);
                    成功获取 = true;
                    break;
                }
            }
            
            已处理的框架数++;
        }
        
        // 如果通过FrameLayout获取信息失败，尝试直接查找带desc属性的元素
        if (!成功获取) {
            console.log("通过框架获取信息失败，尝试直接查找带desc属性的元素...");
            
            // 查找屏幕上方区域的所有带描述的元素
            let 带描述元素 = className("android.view.View")
                .descMatches(".+")
                .boundsInside(0, 0, device.width, device.height * 0.7)
                .find();
            
            console.log(`找到 ${带描述元素.length} 个带描述的元素`);
            
            // 遍历所有带描述的元素
            for (let i = 0; i < 带描述元素.length; i++) {
                let 描述 = 带描述元素[i].desc();
                if (描述) {
                    console.log(`元素描述: "${描述}"`);
                    let 解析结果 = 解析框架描述(描述);
                    if (解析结果) {
                        if (!文章信息.标题 && 解析结果.标题) 文章信息.标题 = 解析结果.标题;
                        if (!文章信息.用户名 && 解析结果.用户名) 文章信息.用户名 = 解析结果.用户名;
                    }
                }
            }
        }
        
        // 如果标题和用户名至少有一个找到，就返回结果
        if (文章信息.标题 || 文章信息.用户名) {
            console.log(`最终获取到的信息: 标题="${文章信息.标题}", 用户名="${文章信息.用户名}"`);
            return 文章信息;
        }
        
        console.log("未能获取有效的首页文章信息");
        return null;
    } catch (e) {
        console.error("获取首页文章信息出错: " + e.message);
        return null;
    }
}

/**
 * 解析框架描述信息，提取标题和用户名
 * 
 * @param {string} 描述 - 框架的desc属性文本
 * @returns {Object|null} - 包含标题和用户名的对象，解析失败返回null
 */
function 解析框架描述(描述) {
    if (!描述) return null;
    
    try {
        let 结果 = {
            标题: "",
            用户名: ""
        };
        
        // 常见格式1: "标题 by 用户名"
        let 匹配1 = 描述.match(/(.+)\s+by\s+(.+)/i);
        if (匹配1) {
            结果.标题 = 匹配1[1].trim();
            结果.用户名 = 匹配1[2].trim();
            return 结果;
        }
        
        // 常见格式2: "标题 - 用户名"
        let 匹配2 = 描述.match(/(.+)\s+-\s+(.+)/);
        if (匹配2) {
            结果.标题 = 匹配2[1].trim();
            结果.用户名 = 匹配2[2].trim();
            return 结果;
        }
        
        // 常见格式3: "用户名: 标题"
        let 匹配3 = 描述.match(/(.+)[:：]\s*(.+)/);
        if (匹配3) {
            结果.用户名 = 匹配3[1].trim();
            结果.标题 = 匹配3[2].trim();
            return 结果;
        }
        
        // 如果描述文本包含@符号，可能是用户名
        if (描述.includes("@")) {
            let 分割 = 描述.split("@");
            if (分割.length >= 2) {
                // @前面的部分可能是标题，@后面的部分可能是用户名
                结果.标题 = 分割[0].trim();
                结果.用户名 = "@" + 分割[1].trim();
                return 结果;
            }
        }
        
        // 如果描述很短，可能是用户名
        if (描述.length < 15 && !描述.includes("点赞") && !描述.includes("收藏")) {
            结果.用户名 = 描述.trim();
            return 结果;
        }
        
        // 如果描述较长，可能是标题
        if (描述.length > 10) {
            结果.标题 = 描述.trim();
            return 结果;
        }
        
        return null;
    } catch (e) {
        console.error("解析框架描述出错: " + e.message);
        return null;
    }
}

/**
 * 调试首页文章框架
 * 详细输出首页所有FrameLayout元素信息
 */
function 调试首页文章框架() {
    console.log("=== 开始调试首页文章框架 ===");
    
    try {
        // 1. 输出当前屏幕尺寸
        let 屏幕宽度 = device.width;
        let 屏幕高度 = device.height;
        console.log(`屏幕尺寸: ${屏幕宽度}x${屏幕高度}`);
        
        // 2. 查找所有FrameLayout元素
        let 框架元素列表 = className("android.widget.FrameLayout").find();
        console.log(`总共找到 ${框架元素列表.length} 个FrameLayout元素`);
        
        // 3. 输出每个框架的信息
        for (let i = 0; i < 框架元素列表.length; i++) {
            let 框架 = 框架元素列表[i];
            let 边界 = 框架.bounds();
            
            // 只关注可能是文章卡片的框架
            if (边界.width() > 屏幕宽度 * 0.5 && 边界.height() > 屏幕高度 * 0.1) {
                console.log(`\n框架 #${i}:`);
                console.log(`  位置: 左${边界.left}, 上${边界.top}, 右${边界.right}, 下${边界.bottom}`);
                console.log(`  尺寸: 宽${边界.width()}, 高${边界.height()}`);
                
                let desc描述 = 框架.desc();
                if (desc描述) {
                    console.log(`  描述(desc): "${desc描述}"`);
                } else {
                    console.log("  无描述(desc)");
                }
                
                let 文本 = 框架.text();
                if (文本) {
                    console.log(`  文本(text): "${文本}"`);
                } else {
                    console.log("  无文本(text)");
                }
                
                let id = 框架.id();
                if (id) {
                    console.log(`  ID: "${id}"`);
                } else {
                    console.log("  无ID");
                }
                
                // 查找框架内的子元素
                console.log("  查找子元素...");
                let 带描述子元素 = 框架.find(descMatches(".+"));
                console.log(`  找到 ${带描述子元素.length} 个带描述的子元素`);
                
                for (let j = 0; j < Math.min(带描述子元素.length, 5); j++) {
                    let 子元素 = 带描述子元素[j];
                    let 子元素描述 = 子元素.desc();
                    console.log(`    子元素 #${j} 描述: "${子元素描述}"`);
                }
                
                let 带文本子元素 = 框架.find(textMatches(".+"));
                console.log(`  找到 ${带文本子元素.length} 个带文本的子元素`);
                
                for (let j = 0; j < Math.min(带文本子元素.length, 5); j++) {
                    let 子元素 = 带文本子元素[j];
                    let 子元素文本 = 子元素.text();
                    console.log(`    子元素 #${j} 文本: "${子元素文本}"`);
                }
            }
        }
        
        // 4. 查找所有带描述的View元素
        console.log("\n=== 查找所有带描述的View元素 ===");
        let 带描述元素 = className("android.view.View").descMatches(".+").find();
        console.log(`找到 ${带描述元素.length} 个带描述的View元素`);
        
        for (let i = 0; i < Math.min(带描述元素.length, 10); i++) {
            let 元素 = 带描述元素[i];
            let 描述 = 元素.desc();
            let 边界 = 元素.bounds();
            console.log(`\nView元素 #${i}:`);
            console.log(`  描述: "${描述}"`);
            console.log(`  位置: 左${边界.left}, 上${边界.top}, 右${边界.right}, 下${边界.bottom}`);
        }
        
        // 5. 截图辅助分析
        console.log("\n尝试截图保存...");
        try {
            let img = captureScreen();
            if (img) {
                let 时间戳 = new Date().getTime();
                let 文件路径 = "/sdcard/Pictures/框架调试截图_" + 时间戳 + ".jpg";
                images.save(img, 文件路径);
                console.log("截图已保存: " + 文件路径);
            }
        } catch (e) {
            console.log("截图失败: " + e.message);
        }
        
        console.log("=== 调试完成 ===");
    } catch (e) {
        console.error("调试过程出错: " + e.message);
    }
}

/**
 * 比较两篇文章是否相同
 * 优化版：更精确地比较标题和作者信息
 * 
 * @param {Object} 文章1 - 第一篇文章信息
 * @param {Object} 文章2 - 第二篇文章信息
 * @returns {boolean} - 是否是同一篇文章
 */
function 比较文章(文章1, 文章2) {
    if (!文章1 || !文章2) return false;
    
    let 标题匹配 = false;
    let 用户名匹配 = false;
    
    // 比较标题
    if (文章1.标题 && 文章2.标题) {
        // 标题可能被截断，使用包含关系判断
        // 将两个标题都转为小写进行比较，提高匹配准确性
        let 标题1 = 文章1.标题.toLowerCase().trim();
        let 标题2 = 文章2.标题.toLowerCase().trim();
        
        // 如果有一个标题特别短，只比较较短的部分
        const 最小匹配长度 = 5; // 至少需要5个字符
        
        if (标题1.length < 最小匹配长度 || 标题2.length < 最小匹配长度) {
            // 标题过短，不可靠，需要依赖用户名匹配
            console.log("标题过短，跳过标题比较");
        } 
        // 如果长标题包含短标题
        else if (标题1.includes(标题2) || 标题2.includes(标题1)) {
            标题匹配 = true;
            console.log("文章标题匹配");
        } 
        // 计算相似度：检查重叠部分
        else {
            // 提取标题中的关键词（去除常见的修饰词）
            const 忽略词 = ["的", "了", "和", "与", "在", "是", "我", "你", "他", "她", "它", "这", "那", "有", "不", "就"];
            
            // 计算两个标题的词语重叠率
            let 关键词1 = 标题1.split(/\s+/).filter(词 => 词.length > 1 && !忽略词.includes(词));
            let 关键词2 = 标题2.split(/\s+/).filter(词 => 词.length > 1 && !忽略词.includes(词));
            
            // 计算重叠的关键词数量
            let 重叠词 = 关键词1.filter(词 => 关键词2.some(词2 => 词2.includes(词) || 词.includes(词2)));
            
            // 如果有足够多的关键词重叠，认为标题匹配
            if (重叠词.length > 0 && (重叠词.length >= Math.min(关键词1.length, 关键词2.length) * 0.5)) {
                标题匹配 = true;
                console.log("文章标题关键词匹配");
            } else {
                console.log("文章标题不匹配");
                console.log(`标题1: "${文章1.标题}"`);
                console.log(`标题2: "${文章2.标题}"`);
            }
        }
    }
    
    // 比较用户名
    if (文章1.用户名 && 文章2.用户名) {
        // 用户名应该完全匹配或包含关系
        let 用户名1 = 文章1.用户名.toLowerCase().trim();
        let 用户名2 = 文章2.用户名.toLowerCase().trim();
        
        // 移除@符号等前缀
        用户名1 = 用户名1.replace(/^@+/, '');
        用户名2 = 用户名2.replace(/^@+/, '');
        
        if (用户名1 === 用户名2 || 用户名1.includes(用户名2) || 用户名2.includes(用户名1)) {
            用户名匹配 = true;
            console.log("作者匹配");
        } else {
            console.log("作者不匹配");
            console.log(`作者1: "${文章1.用户名}"`);
            console.log(`作者2: "${文章2.用户名}"`);
        }
    }
    
    // 判断最终结果
    // 1. 如果标题和用户名都匹配，则认为是同一篇文章
    if (标题匹配 && 用户名匹配) {
        console.log("标题和作者都匹配，确认为同一篇文章");
        return true;
    }
    // 2. 如果只有标题匹配，但标题足够长且特征明显，也可能是同一篇文章
    else if (标题匹配 && (文章1.标题?.length >= 10 || 文章2.标题?.length >= 10)) {
        console.log("标题足够特征明显，可能是同一篇文章");
        return true;
    }
    // 3. 如果只有用户名匹配，且标题缺失或特别短，可能需要进一步确认
    else if (用户名匹配 && (!文章1.标题 || !文章2.标题 || 文章1.标题.length < 5 || 文章2.标题.length < 5)) {
        console.log("只有作者匹配，标题信息不足，暂定为同一作者的文章");
        return true;
    }
    
    // 其他情况，认为不是同一篇文章
    console.log("文章不匹配");
    return false;
}

/**
 * 点击首页第一篇文章的点赞按钮
 * 优化版：多种方法尝试找到并点击点赞按钮
 * 
 * @returns {boolean} - 是否成功点赞
 */
function 点赞首页文章() {
    console.log("尝试点赞首页文章...");
    
    try {
        // 方法1：尝试查找点赞按钮元素
        let 点赞按钮 = className("android.widget.Button")
            .descMatches(".*点赞.*")
            .findOne(1000);
        
        if (点赞按钮) {
            console.log("找到点赞按钮元素，直接点击");
            // 检查是否已点赞
            let desc = 点赞按钮.desc();
            if (desc && desc.includes("已点赞")) {
                console.log("文章已经点过赞了");
                return true;
            }
            
            点赞按钮.click();
            等待(1000);
            return true;
        }
        
        // 方法2：查找点赞图标或点赞区域
        // 首页文章点赞按钮通常在文章右下角
        console.log("尝试通过区域定位点赞按钮");
        
        // 定义首页文章区域
        let 屏幕宽度 = device.width;
        let 屏幕高度 = device.height;
        
        // 点赞按钮通常在首页文章的右下角
        let 点赞区域 = {
            left: Math.floor(屏幕宽度 * 0.7),  // 右侧30%区域
            top: Math.floor(屏幕高度 * 0.4),   // 大约在第一篇文章的位置
            right: 屏幕宽度,
            bottom: Math.floor(屏幕高度 * 0.6)  // 到屏幕60%位置结束
        };
        
        // 尝试查找该区域内的可能的点赞按钮
        let 区域内按钮 = className("android.widget.Button")
            .boundsInside(点赞区域.left, 点赞区域.top, 点赞区域.right, 点赞区域.bottom)
            .find();
        
        if (区域内按钮.length > 0) {
            console.log(`找到区域内${区域内按钮.length}个按钮，点击第一个`);
            区域内按钮[0].click();
            等待(1000);
            return true;
        }
        
        // 方法3：使用坐标点击（最后的备选方案）
        console.log("尝试使用坐标点击点赞按钮");
        
        // 计算首页第一篇文章的点赞按钮大致位置
        let x = 屏幕宽度 * 0.85;  // 屏幕右侧
        let y = 屏幕高度 * 0.45;  // 大约在第一篇文章的位置
        
        console.log(`点击坐标: (${x}, ${y})`);
        click(x, y);
        
        // 等待点赞动画
        等待(1000);
        
        // 尝试其他位置
        if (Math.random() > 0.5) {
            console.log("尝试第二个位置");
            click(屏幕宽度 * 0.83, 屏幕高度 * 0.48);
            等待(800);
        }
        
        // 检查是否点赞成功（如果能找到"已点赞"文本）
        let 已点赞 = textMatches(".*已点赞.*").findOne(500) || 
                   descMatches(".*已点赞.*").findOne(500);
        
        if (已点赞) {
            console.log("确认点赞成功");
            return true;
        }
        
        return true; // 即使无法确认是否成功，也返回true
    } catch (e) {
        console.error("点赞首页文章出错: " + e.message);
        return false;
    }
}

/**
 * 基于链接内容和首页比较的点赞流程
 * 优化版本：确保正确返回首页并处理特殊情况
 * 
 * @param {string} 链接 - 要点赞的文章链接
 * @returns {boolean} - 是否成功点赞
 */
function 基于比较点赞(链接) {
    console.log("开始基于比较的点赞流程...");
    
    try {
        // 0. 确保小红书已关闭
        //关闭小红书();
        
        // 1. 打开链接并获取文章信息
        console.log("使用浏览器打开链接...");
        if (!在浏览器检测App内打开(链接)) {
            console.log("打开链接失败");
            return false;
        }
        
        // 等待页面完全加载
        console.log("等待文章页面加载...");
        等待(3000);
        
        // 获取文章详情页的信息
        let 链接文章信息 = 获取文章信息();
        if (!链接文章信息 || (!链接文章信息.标题 && !链接文章信息.用户名)) {
            console.log("未能获取有效的链接文章信息");
            return false;
        }
        
        console.log("获取到链接文章信息:");
        console.log(`标题: "${链接文章信息.标题}"`);
        console.log(`作者: "${链接文章信息.用户名}"`);
        
        // 2. 返回主界面
        console.log("准备返回首页...");
        if (!返回主界面()) {
            console.log("返回首页失败");
            return false;
        }
        
        // 等待首页内容加载
        console.log("已返回首页，等待首页内容加载...");
        等待(2000);
        
        // 3. 点击首页第一篇文章
        console.log("点击首页第一篇文章");
        if (!点击首篇文章()) {
            console.log("点击首页文章失败");
            return false;
        }
        
        // 等待文章加载
        等待(3000);
        
        // 4. 获取首页点击的文章信息
        let 首页文章信息 = 获取文章信息();
        if (!首页文章信息 || (!首页文章信息.标题 && !首页文章信息.用户名)) {
            console.log("未能获取有效的首页文章信息");
            return false;
        }
        
        console.log("获取到首页文章信息:");
        console.log(`标题: "${首页文章信息.标题}"`);
        console.log(`作者: "${首页文章信息.用户名}"`);
        
        // 5. 比较两篇文章是否相同
        let 是否相同文章 = 比较文章(链接文章信息, 首页文章信息);
        
        // 6. 如果是相同文章，点赞
        if (是否相同文章) {
            console.log("文章匹配，执行点赞");
            互动元素信息 = 获取互动元素();
            if (互动元素信息) {
                let 点赞结果 = 执行点赞();
                console.log("点赞完成，返回主界面");
                返回主界面();
                return 点赞结果;
            } else {
                console.log("无法获取互动元素");
                返回主界面();
                return false;
            }
        } else {
            console.log("文章不匹配，不执行点赞");
            返回主界面();
            return false;
        }
    } catch (e) {
        console.error("基于比较点赞出错: " + e.message);
        try {
            返回主界面();
        } catch (err) {
            console.error("返回主界面出错: " + err.message);
        }
        return false;
    } finally {
        // 无论成功失败，结束时都尝试关闭小红书
        try {
            //关闭小红书();
        } catch (err) {
            console.error("关闭小红书出错: " + err.message);
        }
    }
}

/**
 * 检查是否在首页
 * 
 * @returns {boolean} - 是否在首页
 */
function 检查是否在首页() {
    // 首页可能出现的关键词
    const 首页关键词 = ["首页", "发现", "关注", "直播", "短剧", "附近", "热门", "消息", "我", "推荐", "穿搭", "情感"];
    
    try {
        // 查找所有文本元素
        let 所有文本 = textMatches(".*").find();
        let 找到的关键词 = 0;
        
        // 检查有多少关键词存在
        for (let i = 0; i < 所有文本.length; i++) {
            let 文本内容 = 所有文本[i].text();
            if (!文本内容) continue;
            
            // 使用some方法更高效地检查是否包含任何关键词
            if (首页关键词.some(关键词 => 文本内容.includes(关键词))) {
                找到的关键词++;
                
                // 如果找到2个或以上关键词，认为已在首页
                if (找到的关键词 >= 2) {
                    return true;
                }
            }
        }
        
        return false;
    } catch (e) {
        console.error("检查是否在首页出错: " + e.message);
        return false;
    }
}

/**
 * 执行自动点赞操作
 * 
 * @param {string} 链接文件名 - 链接文件名
 * @param {number} 最大操作数量 - 最大操作数量
 * @param {number} 操作间隔时间 - 操作间隔时间(毫秒)
 */
function 执行自动点赞(链接文件名, 最大操作数量, 操作间隔时间) {
    // 立即更新状态，提示用户已经开始处理
    ui.run(() => {
        ui.状态文本.setText("正在准备操作...");
    });
    home();
    // 使用新线程读取文件，避免阻塞UI
    threads.start(function() {
        // 读取链接文件
        let 链接列表 = 读取链接文件(链接文件名);
        if(!链接列表 || 链接列表.length === 0) {
            ui.run(() => {
                ui.状态文本.setText("链接文件为空或不存在");
            });
            return;
        }
        
        ui.run(() => {
            ui.状态文本.setText(`读取到${链接列表.length}个链接,准备开始操作...`);
        });
        
        // 首先确保小红书已关闭
        //关闭小红书();
        
        // 记录已操作数量
        let 已操作数量 = 0;
        
        // 遍历链接列表
        for(let i = 0; i < 链接列表.length && 已操作数量 < 最大操作数量; i++) {
            let 链接 = 链接列表[i];
            if(!链接) continue;
            
            ui.run(() => {
                ui.状态文本.setText(`正在处理第${i+1}个链接(${已操作数量+1}/${最大操作数量})`);
            });
            
            // 确保每次处理链接前小红书都是关闭状态
            //关闭小红书();
            
            // 使用基于比较的点赞方法
            let 点赞结果 = 基于比较点赞(链接);
            
            if(点赞结果) {
                已操作数量++;
                ui.run(() => {
                    ui.状态文本.setText(`成功点赞第${i+1}个链接(${已操作数量}/${最大操作数量})`);
                });
            } else {
                ui.run(() => {
                    ui.状态文本.setText(`点赞第${i+1}个链接失败`);
                });
            }
            
            // 操作间隔
            随机等待(操作间隔时间, 操作间隔时间 + 2000);
            
            // 操作完成后再次确保小红书已关闭
            //关闭小红书();
        }
        
        ui.run(() => {
            ui.状态文本.setText(`操作完成,共成功点赞${已操作数量}篇文章`);
        });
    });
}

/**
 * 读取链接文件
 * 
 * @param {string} 文件名 - 链接文件名
 * @returns {Array} - 链接列表
 */
function 读取链接文件(文件名) {
    try {
        // 文件路径
        let 文件路径 = "/mnt/shared/Pictures/" + 文件名;
        
        // 检查文件是否存在
        if(!files.exists(文件路径)) {
            console.error("链接文件不存在: " + 文件路径);
            return [];
        }
        
        // 读取文件内容
        let 文件内容 = files.read(文件路径);
        
        // 按行分割并过滤空行
        let 链接列表 = 文件内容.split("\n")
            .map(行 => 行.trim())
            .filter(行 => 行.length > 0);
        
        console.log(`成功读取${链接列表.length}个链接`);
        return 链接列表;
    } catch(e) {
        console.error("读取链接文件出错: " + e.message);
        return [];
    }
}

// 启动UI界面
console.log("正在启动小红书自动点赞工具...");

// 确保无障碍服务已启用
if(!auto.service) {
    console.log("正在申请无障碍服务权限...");
    auto.waitFor();
}

// 启动UI界面
启动UI界面();

/**
 * 调试首页文章信息获取
 * 详细输出首页文章元素和识别过程
 */
function 调试首页文章信息() {
    console.log("=== 开始调试首页文章信息获取 ===");
    
    try {
        // 1. 输出当前屏幕尺寸
        let 屏幕宽度 = device.width;
        let 屏幕高度 = device.height;
        console.log(`屏幕尺寸: ${屏幕宽度}x${屏幕高度}`);
        
        // 2. 查找所有文本元素
        let 所有文本元素 = className("android.widget.TextView").find();
        console.log(`总共找到 ${所有文本元素.length} 个文本元素`);
        
        // 3. 输出可能的标题元素
        console.log("\n--- 屏幕中部文本元素 (可能的标题) ---");
        let 中部文本 = 所有文本元素.filter(元素 => {
            let 边界 = 元素.bounds();
            return 边界 && 
                   边界.top > 屏幕高度 * 0.2 && 
                   边界.top < 屏幕高度 * 0.6 &&
                   元素.text().length > 3;
        });
        
        for (let i = 0; i < Math.min(中部文本.length, 10); i++) {
            let 元素 = 中部文本[i];
            let 文本 = 元素.text();
            let 边界 = 元素.bounds();
            console.log(`[${i}] 文本: "${文本}"`);
            console.log(`    位置: 左${边界.left}, 上${边界.top}, 右${边界.right}, 下${边界.bottom}`);
            console.log(`    宽度: ${边界.width()}, 高度: ${边界.height()}`);
        }
        
        // 4. 输出可能的用户名元素
        console.log("\n--- 可能的用户名元素 ---");
        let 短文本元素 = 所有文本元素.filter(元素 => {
            let 文本 = 元素.text();
            let 边界 = 元素.bounds();
            return 文本 && 
                   文本.length > 0 && 
                   文本.length < 20 &&
                   边界 &&
                   边界.top < 屏幕高度 * 0.6 &&
                   !文本.match(/^\d+$/);
        });
        
        for (let i = 0; i < Math.min(短文本元素.length, 10); i++) {
            let 元素 = 短文本元素[i];
            let 文本 = 元素.text();
            let 边界 = 元素.bounds();
            console.log(`[${i}] 文本: "${文本}"`);
            console.log(`    位置: 左${边界.left}, 上${边界.top}, 右${边界.right}, 下${边界.bottom}`);
        }
        
        // 5. 查找互动按钮
        console.log("\n--- 互动按钮 ---");
        let 点赞按钮 = className("android.widget.Button").descMatches(".*点赞.*").findOne(500);
        if (点赞按钮) {
            console.log(`点赞按钮: "${点赞按钮.desc()}"`);
            console.log(`位置: ${JSON.stringify(点赞按钮.bounds())}`);
        } else {
            console.log("未找到点赞按钮");
        }
        
        // 6. 调用现有函数获取信息并输出结果
        console.log("\n--- 现有函数识别结果 ---");
        let 文章信息 = 获取首页文章信息();
        if (文章信息) {
            console.log(`识别到的标题: "${文章信息.标题}"`);
            console.log(`识别到的用户名: "${文章信息.用户名}"`);
        } else {
            console.log("未识别到文章信息");
        }
        
        // 7. 截图辅助分析
        console.log("\n尝试截图保存...");
        try {
            let img = captureScreen();
            if (img) {
                let 时间戳 = new Date().getTime();
                let 文件路径 = "/sdcard/Pictures/调试截图_" + 时间戳 + ".jpg";
                images.save(img, 文件路径);
                console.log("截图已保存: " + 文件路径);
            }
        } catch (e) {
            console.log("截图失败: " + e.message);
        }
        
        console.log("=== 调试完成 ===");
        
    } catch (e) {
        console.error("调试过程出错: " + e.message);
    }
}

/**
 * 关闭小红书应用
 * 
 * @returns {boolean} - 是否成功关闭
 */
function 关闭小红书() {
    console.log("尝试关闭小红书应用");
    
    try {
        // 检查小红书是否在运行
        let 小红书包名 = "com.xingin.xhs";
        let 当前应用 = currentPackage();
        
        //if (当前应用 === 小红书包名 ) {
           // console.log("检测到小红书正在运行，准备关闭");
            
            // 先尝试使用系统接口关闭应用
            app.openAppSetting(小红书包名);
            sleep(1000);
            
            // 寻找并点击"强行停止"按钮
            let 强行停止按钮 = text("强行停止").findOne(2000) || 
                             text("强制停止").findOne(1000) || 
                             textContains("停止").findOne(1000);
            
            if (强行停止按钮) {
                console.log("找到强行停止按钮，点击");
                强行停止按钮.click();
                sleep(1000);
                
                // 点击确认按钮
                let 确认按钮 = text("确定").findOne(1000) || 
                            text("确认").findOne(1000) || 
                            text("强行停止").findOne(1000);
                
                if (确认按钮) {
                    确认按钮.click();
                    sleep(1000);
                    console.log("已确认关闭小红书");
                    
                    // 返回主屏幕
                    home();
                    sleep(1000);
                    return true;
                }
            } else {
                console.log("未找到强行停止按钮，尝试直接返回主屏幕");
                home();
                sleep(1000);
                return true;
            }
        // } else {
        //     console.log("小红书未在运行，无需关闭");
        //     return true;
        // }
        
        // 检查是否成功关闭
        当前应用 = currentPackage();
        if (当前应用 !== 小红书包名) {
            console.log("成功关闭小红书应用");
            return true;
        } else {
            console.log("关闭小红书失败，尝试使用后退键退出");
            for (let i = 0; i < 5; i++) {
                back();
                sleep(300);
            }
            
            home();
            sleep(1000);
            return true;
        }
    } catch (e) {
        console.error("关闭小红书出错: " + e.message);
        // 即使出错，也尝试返回主屏幕
        home();
        return false;
    }
}

/**
 * 自动扫描屏幕并点击可能的浏览器按钮
 * 通过坐标网格扫描查找可能的浏览器或App打开按钮
 * 
 * @returns {boolean} - 是否找到并点击了按钮
 */
function 自动扫描屏幕点击浏览器按钮() {
    console.log("开始自动扫描屏幕查找浏览器或App打开按钮...");
    
    try {
        // 截取当前屏幕
        let 屏幕截图 = captureScreen();
        if (!屏幕截图) {
            console.log("无法获取屏幕截图");
            return false;
        }
        
        // 获取屏幕尺寸
        let 屏幕宽度 = device.width;
        let 屏幕高度 = device.height;
        
        // 优先检查已知位置
        // 1. 通常Chrome浏览器在打开方式对话框中位于上方区域
        let 点击位置列表 = [
            {x: 屏幕宽度 * 0.25, y: 屏幕高度 * 0.25, 描述: "左上区域"},
            {x: 屏幕宽度 * 0.75, y: 屏幕高度 * 0.25, 描述: "右上区域"},
            {x: 屏幕宽度 * 0.25, y: 屏幕高度 * 0.35, 描述: "左中上区域"},
            {x: 屏幕宽度 * 0.75, y: 屏幕高度 * 0.35, 描述: "右中上区域"},
            {x: 屏幕宽度 * 0.5, y: 屏幕高度 * 0.3, 描述: "中上区域"},
            {x: 屏幕宽度 * 0.5, y: 屏幕高度 * 0.6, 描述: "中间区域"},
            {x: 屏幕宽度 * 0.25, y: 屏幕高度 * 0.85, 描述: "左下区域(取消)"},
            {x: 屏幕宽度 * 0.75, y: 屏幕高度 * 0.85, 描述: "右下区域(确定)"}
        ];
        
        // 2. App内打开按钮通常在页面中部或底部
        let App内打开位置 = [
            {x: 屏幕宽度 * 0.5, y: 屏幕高度 * 0.7, 描述: "底部中间(App内打开)"},
            {x: 屏幕宽度 * 0.5, y: 屏幕高度 * 0.5, 描述: "中间(App内打开)"},
            {x: 屏幕宽度 * 0.5, y: 屏幕高度 * 0.8, 描述: "底部(App内打开)"}
        ];
        
        // 合并所有可能的点击位置
        点击位置列表 = 点击位置列表.concat(App内打开位置);
        
        // 首先尝试点击可能的位置
        for (let i = 0; i < 点击位置列表.length; i++) {
            let 位置 = 点击位置列表[i];
            console.log(`尝试点击${位置.描述}位置: (${位置.x}, ${位置.y})`);
            click(位置.x, 位置.y);
            sleep(1000);
            
            // 检查是否已经进入小红书
            let 当前应用 = typeof currentPackage === "function" ? currentPackage() : "";
            if (当前应用 === "com.xingin.xhs") {
                console.log(`点击${位置.描述}位置后成功进入小红书！`);
                return true;
            }
            
            // 检查是否出现了"始终"按钮
            let 始终按钮 = text("始终").findOne(300);
            if (始终按钮) {
                console.log("检测到'始终'按钮，点击");
                let bounds = 始终按钮.bounds();
                if (bounds) {
                    click(bounds.centerX(), bounds.centerY());
                } else {
                    始终按钮.click();
                }
                sleep(1000);
                
                // 再次检查是否进入小红书
                当前应用 = typeof currentPackage === "function" ? currentPackage() : "";
                if (当前应用 === "com.xingin.xhs") {
                    console.log("点击'始终'按钮后成功进入小红书！");
                    return true;
                }
            }
        }
        
        console.log("自动扫描屏幕点击未能进入小红书");
        return false;
    } catch (e) {
        console.error("自动扫描屏幕出错: " + e.message);
        return false;
    }
}

/**
 * 扫描页面并点击匹配关键词的元素
 * 
 * @param {Array} 关键词列表 - 要匹配的关键词列表
 * @param {number} 等待时间 - 点击后等待的时间(毫秒)
 * @returns {boolean} - 是否找到并点击了匹配的元素
 */
function 扫描页面点击匹配元素(关键词列表, 等待时间) {
    console.log(`扫描页面寻找关键词: ${关键词列表.join(', ')}`);
    
    try {
        // 查找所有文本元素
        let 所有元素 = textMatches(".*").find();
        console.log(`找到 ${所有元素.length} 个文本元素`);
        
        // 遍历所有文本元素
        for (let i = 0; i < 所有元素.length; i++) {
            let 元素 = 所有元素[i];
            let 文本 = 元素.text();
            
            if (!文本) continue;
            
            // 检查是否匹配任何关键词
            for (let j = 0; j < 关键词列表.length; j++) {
                let 关键词 = 关键词列表[j];
                
                if (文本.includes(关键词)) {
                    console.log(`找到匹配关键词 "${关键词}" 的元素: "${文本}"`);
                    
                    // 获取元素的边界
                    let bounds = 元素.bounds();
                    if (bounds) {
                        let centerX = bounds.centerX();
                        let centerY = bounds.centerY();
                        console.log(`点击坐标: (${centerX}, ${centerY})`);
                        
                        // 执行点击
                        click(centerX, centerY);
                        sleep(等待时间 || 1000);
                        return true;
                    } else {
                        // 如果无法获取边界，尝试直接点击元素
                        console.log("尝试直接点击元素");
                        元素.click();
                        sleep(等待时间 || 1000);
                        return true;
                    }
                }
            }
        }
        
        // 如果没找到匹配的文本元素，尝试查找desc属性
        所有元素 = descMatches(".*").find();
        console.log(`找到 ${所有元素.length} 个desc元素`);
        
        // 遍历所有desc元素
        for (let i = 0; i < 所有元素.length; i++) {
            let 元素 = 所有元素[i];
            let 描述 = 元素.desc();
            
            if (!描述) continue;
            
            // 检查是否匹配任何关键词
            for (let j = 0; j < 关键词列表.length; j++) {
                let 关键词 = 关键词列表[j];
                
                if (描述.includes(关键词)) {
                    console.log(`找到匹配关键词 "${关键词}" 的desc元素: "${描述}"`);
                    
                    // 获取元素的边界
                    let bounds = 元素.bounds();
                    if (bounds) {
                        let centerX = bounds.centerX();
                        let centerY = bounds.centerY();
                        console.log(`点击坐标: (${centerX}, ${centerY})`);
                        
                        // 执行点击
                        click(centerX, centerY);
                        sleep(等待时间 || 1000);
                        return true;
                    } else {
                        // 如果无法获取边界，尝试直接点击元素
                        console.log("尝试直接点击元素");
                        元素.click();
                        sleep(等待时间 || 1000);
                        return true;
                    }
                }
            }
        }
        
        console.log("未找到匹配的元素");
        return false;
    } catch (e) {
        console.error("扫描页面出错: " + e.message);
        return false;
    }
}

/**
 * 在浏览器检测App内打开
 * 优化版：使用关键词扫描页面
 * 
 * @param {string} 链接 - 要打开的链接
 * @returns {boolean} - 是否成功打开
 */
function 在浏览器检测App内打开(链接) {
    console.log("准备打开链接: " + 链接);
    
    // 先关闭可能运行中的小红书应用
    //关闭小红书();
    
    // 使用浏览器打开链接
    console.log("使用浏览器打开链接");
    app.openUrl(链接);
    toast("已用浏览器打开链接");
    
    // 等待1秒，处理可能出现的打开方式弹窗
    sleep(1000);
    
    // 处理打开方式弹窗 - 查找并点击浏览器选项
    if (text("打开方式").exists()) {
        console.log("检测到打开方式弹窗");
        
        // 浏览器相关关键词
        let 浏览器关键词 = ["浏览器", "Chrome", "Firefox", "Edge", "展开", "Browser"];
        if (扫描页面点击匹配元素(浏览器关键词, 1000)) {
            console.log("已点击浏览器选项");
            
            // 查找并点击"始终"按钮
            sleep(500);
            let 确认关键词 = ["始终", "仅一次"];
            if (扫描页面点击匹配元素(确认关键词, 1000)) {
                console.log("已点击确认按钮");
            }
        }
    }
    
    // 最大尝试次数和超时时间
    const 最大尝试次数 = 5;
    const 每次尝试间隔 = 1000; // 1秒
    const 总超时时间 = 20000; // 20秒
    
    // 记录开始时间
    const 开始时间 = new Date().getTime();
    let 尝试次数 = 0;
    
    // 待查找的关键词列表
    const App内打开关键词 = ["App内打开", "打开APP", "打开小红书", "打开应用", "小红书"];
    const 浏览器关键词 = ["浏览器", "Chrome", "在不登录账号的情况下使用"];
    const 始终按钮关键词 = ["始终", "仅一次", "总是"];
    
    while (尝试次数 < 最大尝试次数 && (new Date().getTime() - 开始时间) < 总超时时间) {
        尝试次数++;
        console.log(`第 ${尝试次数} 次尝试打开小红书...`);
        
        // 等待页面加载
        sleep(每次尝试间隔);
        
        // 处理可能出现的权限弹窗
        处理权限弹窗();
        
        // 检查是否已经在小红书App中
        let pkg = typeof currentPackage === "function" ? currentPackage() : "";
        if (pkg === "com.xingin.xhs") {
            console.log("已成功打开小红书App");
            return true;
        }
        
        // 尝试查找并点击"App内打开"按钮
        console.log("尝试查找'App内打开'等按钮...");
        if (扫描页面点击匹配元素(App内打开关键词, 2000)) {
            console.log("已点击App相关按钮，等待进入小红书");
            sleep(2000);
            
            // 再次检查是否已经在小红书App中
            pkg = typeof currentPackage === "function" ? currentPackage() : "";
            if (pkg === "com.xingin.xhs") {
                console.log("已成功进入小红书App");
                return true;
            }
        }
        
        // 如果未能找到App内打开按钮，尝试点击浏览器选项
        if (尝试次数 > 2) {
            console.log("尝试查找浏览器相关选项...");
            if (扫描页面点击匹配元素(浏览器关键词, 1000)) {
                console.log("已点击浏览器相关选项");
                
                // 查找并点击"始终"按钮
                sleep(500);
                if (扫描页面点击匹配元素(始终按钮关键词, 1000)) {
                    console.log("已点击确认按钮");
                }
            }
        }
        
        // 如果还是失败，尝试点击屏幕上常见位置
        if (尝试次数 > 3) {
            console.log("尝试点击屏幕常见位置...");
            
            let 屏幕宽度 = device.width;
            let 屏幕高度 = device.height;
            
            // 中下部位置（通常是App内打开按钮的位置）
            click(屏幕宽度 * 0.5, 屏幕高度 * 0.7);
            sleep(1500);
            
            // 检查是否成功
            pkg = typeof currentPackage === "function" ? currentPackage() : "";
            if (pkg === "com.xingin.xhs") {
                console.log("点击中下部位置后成功进入小红书");
                return true;
            }
        }
    }
    
    // 最后检查一次
    let pkg = typeof currentPackage === "function" ? currentPackage() : "";
    if (pkg === "com.xingin.xhs") {
        console.log("最终确认：已成功打开小红书App");
        return true;
    } else {
        console.log("多次尝试后未能自动进入小红书App");
        toast("请手动点击'App内打开'按钮");
        
        // 给用户一些时间手动操作
        sleep(5000);
        
        // 再次检查
        pkg = typeof currentPackage === "function" ? currentPackage() : "";
        return pkg === "com.xingin.xhs";
    }
}

/**
 * 处理打开方式弹窗
 * 简化版：使用关键词扫描页面
 * 
 * @returns {boolean} - 是否成功处理
 */
function 处理打开方式弹窗() {
    console.log("检查打开方式弹窗...");
    
    try {
        // 查找"打开方式"文本
        if (!text("打开方式").exists()) {
            console.log("没有检测到打开方式弹窗");
            return false;
        }
        
        console.log("检测到打开方式弹窗");
        
        // 浏览器相关关键词
        let 浏览器关键词 = ["浏览器", "Chrome", "Firefox", "Edge", "展开", "Browser"];
        if (扫描页面点击匹配元素(浏览器关键词, 1000)) {
            console.log("已点击浏览器选项");
            
            // 查找并点击"始终"按钮
            sleep(500);
            let 确认关键词 = ["始终", "仅一次"];
            if (扫描页面点击匹配元素(确认关键词, 1000)) {
                console.log("已点击确认按钮");
                return true;
            }
        } else {
            // 如果没找到浏览器选项，尝试点击屏幕上部位置
            console.log("未找到浏览器选项，尝试点击屏幕上部位置");
            
            let 屏幕宽度 = device.width;
            let 屏幕高度 = device.height;
            
            // 点击左上方位置
            click(屏幕宽度 * 0.25, 屏幕高度 * 0.3);
            console.log(`点击屏幕左上方坐标: (${屏幕宽度 * 0.25}, ${屏幕高度 * 0.3})`);
            sleep(1000);
            
            // 查找并点击"始终"按钮
            let 确认关键词 = ["始终", "仅一次"];
            if (扫描页面点击匹配元素(确认关键词, 1000)) {
                console.log("已点击确认按钮");
                return true;
            }
        }
        
        return false;
    } catch (e) {
        console.error("处理打开方式弹窗出错: " + e.message);
        return false;
    }
}

