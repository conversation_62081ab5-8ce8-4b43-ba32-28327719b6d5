﻿"ui";

// 全局变量，用于标记是否已经在运行  335
let 正在运行 = false;

// 全局变量，操作模式: 1=无障碍模式, 2=Root Shell模式
let 操作模式 = 2; // 默认使用Root Shell模式

// 全局变量，保存root会话状态
let root会话 = false;

// 全局变量，按钮颜色配置
let 颜色配置 = {
    点赞颜色: "FF2442",  // 红色系
    收藏颜色: "FCBD54",  // 黄色系
    颜色容差: 40         // 颜色匹配的容差值
};

/**
 * 判断颜色是否匹配
 * @param {number} r - 当前像素的红色分量
 * @param {number} g - 当前像素的绿色分量
 * @param {number} b - 当前像素的蓝色分量
 * @param {string} colorCode - 目标颜色代码，格式为RRGGBB
 * @param {number} tolerance - 容差值，默认为30
 * @returns {boolean} 是否匹配
 */
function 颜色匹配(r, g, b, colorCode, tolerance = 30) {
    // 解析颜色代码
    let targetR = parseInt(colorCode.substring(0, 2), 16);
    let targetG = parseInt(colorCode.substring(2, 4), 16);
    let targetB = parseInt(colorCode.substring(4, 6), 16);

    // 计算颜色差异
    let diffR = Math.abs(r - targetR);
    let diffG = Math.abs(g - targetG);
    let diffB = Math.abs(b - targetB);

    // 判断是否在容差范围内
    return diffR <= tolerance && diffG <= tolerance && diffB <= tolerance;
}

/**
 * 颜色调试函数，将RGB转为十六进制颜色代码
 * @param {number} r - 红色分量
 * @param {number} g - 绿色分量
 * @param {number} b - 蓝色分量
 * @returns {string} 十六进制颜色代码
 */
function RGB转十六进制(r, g, b) {
    function 转两位十六进制(n) {
        let hex = n.toString(16).toUpperCase();
        return hex.length === 1 ? '0' + hex : hex;
    }
    return 转两位十六进制(r) + 转两位十六进制(g) + 转两位十六进制(b);
}

// 创建UI界面
ui.layout(
    <vertical padding="16">
        <text textSize="24sp" textColor="#FF5722" gravity="center" margin="0 0 0 16">小红书自动互动工具</text>

        <text textSize="16sp" textColor="#666666" margin="0 16 0 0">操作模式</text>
        <horizontal>
            <radiogroup id="操作模式选择" orientation="horizontal">
                <radio id="无障碍模式" text="无障碍模式" textColor="#666666" />
                <radio id="Root模式" text="Root模式" textColor="#666666" checked="true" />
            </radiogroup>
        </horizontal>

        <text textSize="16sp" textColor="#666666" margin="0 16 0 0">互动选项</text>
        <horizontal>
            <checkbox id="需要点赞" text="点赞" checked="true" textColor="#666666" />
            <checkbox id="需要收藏" text="收藏" textColor="#666666" marginLeft="16" />
        </horizontal>

        <text textSize="16sp" textColor="#666666" margin="0 16 0 0">链接文件设置</text>
        <horizontal>
            <text text="文件路径:" textColor="#666666" />
            <input id="文件路径" text="/mnt/shared/Pictures/" layout_weight="1" />
        </horizontal>
        <horizontal>
            <text text="文件名:" textColor="#666666" />
            <input id="文件名" text="links.txt" layout_weight="1" />
        </horizontal>

        <text textSize="16sp" textColor="#666666" margin="0 16 0 0">操作设置</text>
        <horizontal>
            <text text="最大操作数:" textColor="#666666" />
            <input id="最大操作数" text="10" inputType="number" layout_weight="1" />
        </horizontal>
        <horizontal>
            <text text="操作间隔(秒):" textColor="#666666" />
            <input id="操作间隔" text="5" inputType="number" layout_weight="1" />
        </horizontal>

        <button id="开始按钮" text="开始执行" style="Widget.AppCompat.Button.Colored" margin="0 16 0 0" />

        <text id="状态文本" textSize="14sp" textColor="#333333" margin="0 16 0 0" />
    </vertical>
);

// 监听操作模式选择变化
ui.无障碍模式.on("check", function (checked) {
    if (checked) {
        操作模式 = 1;
        ui.状态文本.setText("已选择无障碍模式，请确保已开启无障碍服务");
    }
});

ui.Root模式.on("check", function (checked) {
    if (checked) {
        操作模式 = 2;
        ui.状态文本.setText("已选择Root模式，请确保已获取Root权限");
    }
});

// 开始按钮点击事件
ui.开始按钮.click(function () {
    // 防止重复点击
    if (正在运行) {
        toast("任务已在运行中，请等待完成");
        return;
    }

    正在运行 = true;

    // 获取用户输入
    let 文件路径 = ui.文件路径.text();
    let 文件名 = ui.文件名.text();
    let 最大操作数 = parseInt(ui.最大操作数.text());
    let 操作间隔 = parseInt(ui.操作间隔.text());

    // 获取互动选项
    let 需要点赞 = ui.需要点赞.checked;
    let 需要收藏 = ui.需要收藏.checked;

    // 检查是否至少选择了一个互动选项
    if (!需要点赞 && !需要收藏) {
        toast("请至少选择一个互动选项（点赞或收藏）");
        正在运行 = false;
        return;
    }

    // 更新状态
    ui.状态文本.setText("开始执行，即将隐藏界面...");

    // 隐藏界面
    ui.layout(
        <frame>
            <text text="正在后台运行..." textSize="12sp" textColor="#888888" gravity="center" />
        </frame>
    );

    // 创建单个工作线程
    let 工作线程 = threads.start(function () {
        try {
            // 根据操作模式检查权限
            if (操作模式 === 1) {
                // 无障碍模式，检查无障碍服务是否已启用
                if (!auto.service) {
                    toast("无障碍服务未启用，尝试启动...");
                    auto.waitFor();
                }
            } else if (操作模式 === 2) {
                // Root模式，检查Root权限
                if (!初始化Root权限()) {
                    toast("警告：未获取到 root 权限，脚本可能无法正常工作");
                    等待(2000);
                    正在运行 = false;
                    return;
                }
                console.log("Root权限检查通过");
            }

            // 请求截屏权限
            if (!requestScreenCapture()) {
                toast("请求截屏权限失败");
                正在运行 = false;
                return;
            }

            // 执行主要功能
            执行自动互动(文件路径, 文件名, 最大操作数, 操作间隔, 需要点赞, 需要收藏);
        } catch (e) {
            console.error("执行过程中出错: " + e);
        } finally {
            // 无论如何都要重置运行状态
            正在运行 = false;
        }
    });
});

// 添加脚本退出时的清理函数
events.on("exit", function () {
    console.log("脚本即将退出，执行清理工作...");

    // 重置root会话状态
    root会话 = false;

    console.log("清理工作完成");
});

/**
 * 初始化Root权限，获取一个root会话
 * @returns {boolean} 是否成功获取root权限
 */
function 初始化Root权限() {
    // 如果已经获取过root权限，直接返回true，不再重复申请
    if (root会话) {
        console.log("已有root会话，无需重新申请权限");
        return true;
    }

    console.log("首次尝试获取root权限...");

    try {
        // 使用普通shell命令测试root权限
        let result = shell("su -c 'echo root_test'", true);

        if (result.code === 0 && result.result.includes("root_test")) {
            console.log("成功获取root权限，标记为已授权");
            root会话 = true;
            return true;
        } else {
            console.log("获取root权限失败: " + result.error);
            root会话 = false;
            return false;
        }
    } catch (e) {
        console.error("初始化Root权限出错: " + e);
        root会话 = false;
        return false;
    }
}

//---------
//=====================

// 检查操作模式函数
function 检查操作模式() {
    if (操作模式 === 1) {
        console.log("当前使用无障碍模式操作，请确保已开启无障碍服务");
        // 检查无障碍服务是否已启用
        if (!auto.service) {
            console.log("无障碍服务未启用，尝试启动...");
            auto.waitFor();
        }
    } else if (操作模式 === 2) {
        console.log("当前使用Root Shell模式操作，请确保已获取Root权限");
        // 检查Root权限，如果没有root会话，则尝试初始化一次
        if (!root会话) {
            if (!初始化Root权限()) {
                console.log("警告：未获取到 root 权限，脚本可能无法正常工作");
                return false;
            }
        }
        console.log("Root权限检查通过");
        return true;
    } else {
        console.log("错误：未知的操作模式，请设置为1(无障碍)或2(Root Shell)");
        return false;
    }
    return true;
}

/**
 * 使用 uiautomator dump 获取当前界面的 XML 结构，增加内部重试机制
 * @returns {string|null} XML 内容或 null（如果失败）
 */
function 获取界面XML() {
    console.log("开始获取界面 XML...");

    // 最大内部重试次数
    const 最大重试次数 = 3;
    let 当前重试次数 = 0;

    while (当前重试次数 < 最大重试次数) {
        // 使用执行Root命令函数，确保root权限检查
        let result = 执行Root命令('uiautomator dump /sdcard/window_dump.xml');

        if (result.code === 0) {
            console.log("界面 XML 导出成功");

            // 读取导出的 XML 文件
            try {
                let xmlContent = files.read("/sdcard/window_dump.xml");
                console.log("成功读取 XML 文件，大小: " + xmlContent.length + " 字节");
                return xmlContent;
            } catch (e) {
                console.error("读取 XML 文件失败: " + e.message);
                当前重试次数++;

                if (当前重试次数 < 最大重试次数) {
                    console.log(`读取XML文件失败，进行第${当前重试次数 + 1}次重试...`);
                    等待(1000); // 等待1秒后重试
                }
            }
        } else {
            console.error("界面 XML 导出失败: " + result.error);
            当前重试次数++;

            if (当前重试次数 < 最大重试次数) {
                console.log(`导出XML失败，进行第${当前重试次数 + 1}次重试...`);
                等待(1500); // 失败后等待更长时间再重试
            }
        }
    }

    console.error(`获取界面XML失败，已重试${最大重试次数}次`);
    return null;
}

/**
 * 从 XML 中提取所有文本元素及其坐标
 * @param {string} xmlContent - XML 内容
 * @returns {Array} 元素数组，每个元素包含文本和坐标
 */
function 提取文本元素(xmlContent) {
    console.log("开始解析 XML 中的文本元素...");

    let 元素列表 = [];
    let 文本正则 = /text="([^"]*)"[^>]*bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"/g;
    let 匹配结果;
    let 空文本计数 = 0;

    while ((匹配结果 = 文本正则.exec(xmlContent)) !== null) {
        let 文本 = 匹配结果[1];

        // 跳过空文本内容
        if (!文本 || 文本.trim() === "") {
            空文本计数++;
            continue;
        }

        let 左 = parseInt(匹配结果[2]);
        let 上 = parseInt(匹配结果[3]);
        let 右 = parseInt(匹配结果[4]);
        let 下 = parseInt(匹配结果[5]);

        // 计算中心点坐标
        let 中心X = Math.floor((左 + 右) / 2);
        let 中心Y = Math.floor((上 + 下) / 2);

        元素列表.push({
            文本: 文本,
            坐标: {
                左: 左,
                上: 上,
                右: 右,
                下: 下,
                中心X: 中心X,
                中心Y: 中心Y
            }
        });
    }

    console.log("共找到 " + 元素列表.length + " 个有效文本元素 (已过滤 " + 空文本计数 + " 个空文本元素)");
    return 元素列表;
}

/**
 * 从 XML 中提取特定文本的元素
 * @param {string} xmlContent - XML 内容
 * @param {string} 目标文本 - 要查找的文本（部分匹配）
 * @returns {Object|null} 找到的元素或 null
 */
function 查找特定文本元素(xmlContent, 目标文本) {
    console.log("查找包含文本 '" + 目标文本 + "' 的元素...");

    let 所有元素 = 提取文本元素(xmlContent);

    // 查找包含目标文本的元素
    for (let i = 0; i < 所有元素.length; i++) {
        if (所有元素[i].文本.includes(目标文本)) {
            console.log("找到匹配元素: " + JSON.stringify(所有元素[i]));
            return 所有元素[i];
        }
    }

    console.log("未找到包含文本 '" + 目标文本 + "' 的元素");
    return null;
}

/**
 * 查找并点击特定文本的元素
 * @param {string} 目标文本 - 要查找的文本
 * @returns {boolean} 是否成功
 */
function 查找并点击(目标文本) {
    let xmlContent = 获取界面XML();
    if (!xmlContent) return false;

    let 元素 = 查找特定文本元素(xmlContent, 目标文本);
    if (!元素) return false;

    return 点击(元素.坐标.中心X, 元素.坐标.中心Y);
}

/**
 * 查找点赞按钮并点击
 * @returns {boolean} 是否成功
 */
function 查找点赞按钮() {
    let xmlContent = 获取界面XML();
    if (!xmlContent) return false;

    // 先尝试查找包含"点赞"的元素
    let 点赞元素 = 查找特定文本元素(xmlContent, "点赞");

    if (点赞元素) {
        return 点击(点赞元素.坐标.中心X, 点赞元素.坐标.中心Y);
    }

    // 如果没找到，可能需要查找特定的图标或其他标识
    console.log("未找到点赞按钮");
    return false;
}

/**
 * 打印当前界面的所有文本元素
 */
function 打印所有文本元素() {
    let xmlContent = 获取界面XML();
    if (!xmlContent) return;

    let 元素列表 = 提取文本元素(xmlContent);

    console.log("==== 当前界面文本元素 ====");
    for (let i = 0; i < 元素列表.length; i++) {
        let 元素 = 元素列表[i];
        // 跳过空文本内容
        if (!元素.文本 || 元素.文本.trim() === "") continue;

        console.log((i + 1) + ". 文本: [" + 元素.文本 + "], 坐标: (" +
            元素.坐标.中心X + ", " + 元素.坐标.中心Y + ")");
    }
    console.log("==== 共 " + 元素列表.length + " 个有效元素 ====");

    // 标记核心元素
    标记核心元素(元素列表);
}

/**
 * 查找元素
 * @param {string} 选择器 - 元素选择器，如text("文本")或id("id")
 * @returns {UiObject|null} 找到的元素或null
 */
function 查找元素(选择器) {
    if (操作模式 === 1) {
        // 无障碍模式
        try {
            let 元素 = 选择器.findOne(1000);
            return 元素 || null;
        } catch (e) {
            console.error("查找元素出错: " + e.message);
            return null;
        }
    } else {
        // Root Shell模式下无法直接使用无障碍选择器
        console.log("Root Shell模式下不支持直接使用选择器查找元素");
        return null;
    }
}


/**
 * 查找文本元素并点击
 * @param {string} 文本 - 要查找的文本
 * @returns {boolean} 是否成功点击
 */
function 查找文本并点击(文本) {
    console.log("查找并点击文本: " + 文本);

    if (操作模式 === 1) {
        // 无障碍模式
        let 元素 = text(文本).findOne(3000);
        if (元素) {
            元素.click();
            return true;
        }
        return false;
    } else {
        // Root Shell模式
        return 查找并点击(文本);
    }
}


/**
 * 常用操控函数封装 - 使用 shell 命令替代无障碍服务
 * 以下函数都使用 root 权限执行 shell 命令
 */

/**
 * 返回键操作
 * @returns {boolean} 是否执行成功
 */
function 返回() {
    console.log("执行返回操作");

    if (操作模式 === 1) {
        // 无障碍模式
        back();
        return true;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('input keyevent 4'); // KEYCODE_BACK = 4
        let 成功 = result.code === 0;
        console.log(成功 ? "返回操作成功" : "返回操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 回到主页
 * @returns {boolean} 是否执行成功
 */
function 主页() {
    console.log("执行回到主页操作");

    if (操作模式 === 1) {
        // 无障碍模式
        home();
        return true;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('input keyevent 3'); // KEYCODE_HOME = 3
        let 成功 = result.code === 0;
        console.log(成功 ? "主页操作成功" : "主页操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 打开最近任务
 * @returns {boolean} 是否执行成功
 */
function 最近任务() {
    console.log("执行打开最近任务操作");

    if (操作模式 === 1) {
        // 无障碍模式
        recents();
        return true;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('input keyevent 187'); // KEYCODE_APP_SWITCH = 187
        let 成功 = result.code === 0;
        console.log(成功 ? "最近任务操作成功" : "最近任务操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 点击屏幕
 * @param {number} x - X 坐标
 * @param {number} y - Y 坐标
 * @returns {boolean} 是否执行成功
 */
function 点击(x, y) {
    console.log("执行点击操作: (" + x + ", " + y + ")");

    if (操作模式 === 1) {
        // 无障碍模式
        click(x, y);
        return true;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('input tap ' + x + ' ' + y);
        let 成功 = result.code === 0;
        console.log(成功 ? "点击操作成功" : "点击操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 长按屏幕
 * @param {number} x - X 坐标
 * @param {number} y - Y 坐标
 * @param {number} 时长 - 长按时长(毫秒)，默认1000ms
 * @returns {boolean} 是否执行成功
 */
function 长按(x, y, 时长 = 1000) {
    console.log("执行长按操作: (" + x + ", " + y + "), 时长: " + 时长 + "ms");

    if (操作模式 === 1) {
        // 无障碍模式
        press(x, y, 时长);
        return true;
    } else {
        // Root Shell模式 - 使用swipe命令在同一位置停留来模拟长按
        let result = 执行Root命令('input swipe ' + x + ' ' + y + ' ' + x + ' ' + y + ' ' + 时长);
        let 成功 = result.code === 0;
        console.log(成功 ? "长按操作成功" : "长按操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 滑动屏幕
 * @param {number} 起点x - 起点X坐标
 * @param {number} 起点y - 起点Y坐标
 * @param {number} 终点x - 终点X坐标
 * @param {number} 终点y - 终点Y坐标
 * @param {number} 时长 - 滑动时长(毫秒)，默认500ms
 * @returns {boolean} 是否执行成功
 */
function 滑动(起点x, 起点y, 终点x, 终点y, 时长 = 500) {
    console.log("执行滑动操作: (" + 起点x + ", " + 起点y + ") -> (" + 终点x + ", " + 终点y + "), 时长: " + 时长 + "ms");

    if (操作模式 === 1) {
        // 无障碍模式
        swipe(起点x, 起点y, 终点x, 终点y, 时长);
        return true;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('input swipe ' + 起点x + ' ' + 起点y + ' ' + 终点x + ' ' + 终点y + ' ' + 时长);
        let 成功 = result.code === 0;
        console.log(成功 ? "滑动操作成功" : "滑动操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 上滑屏幕
 * @param {number} 距离 - 滑动距离，默认屏幕高度的1/3
 * @param {number} 时长 - 滑动时长(毫秒)，默认500ms
 * @returns {boolean} 是否执行成功
 */
function 上滑(距离 = null, 时长 = 500) {
    let 屏幕宽度 = device.width;
    let 屏幕高度 = device.height;

    // 如果未指定距离，默认为屏幕高度的1/3
    距离 = 距离 || Math.floor(屏幕高度 / 3);

    let 起点x = Math.floor(屏幕宽度 / 2);
    let 起点y = Math.floor(屏幕高度 * 0.7);
    let 终点y = 起点y - 距离;

    return 滑动(起点x, 起点y, 起点x, 终点y, 时长);
}

/**
 * 下滑屏幕
 * @param {number} 距离 - 滑动距离，默认屏幕高度的1/3
 * @param {number} 时长 - 滑动时长(毫秒)，默认500ms
 * @returns {boolean} 是否执行成功
 */
function 下滑(距离 = null, 时长 = 500) {
    let 屏幕宽度 = device.width;
    let 屏幕高度 = device.height;

    // 如果未指定距离，默认为屏幕高度的1/3
    距离 = 距离 || Math.floor(屏幕高度 / 3);

    let 起点x = Math.floor(屏幕宽度 / 2);
    let 起点y = Math.floor(屏幕高度 * 0.3);
    let 终点y = 起点y + 距离;

    return 滑动(起点x, 起点y, 起点x, 终点y, 时长);
}

/**
 * 左滑屏幕
 * @param {number} 距离 - 滑动距离，默认屏幕宽度的1/3
 * @param {number} 时长 - 滑动时长(毫秒)，默认500ms
 * @returns {boolean} 是否执行成功
 */
function 左滑(距离 = null, 时长 = 500) {
    let 屏幕宽度 = device.width;
    let 屏幕高度 = device.height;

    // 如果未指定距离，默认为屏幕宽度的1/3
    距离 = 距离 || Math.floor(屏幕宽度 / 3);

    let 起点y = Math.floor(屏幕高度 / 2);
    let 起点x = Math.floor(屏幕宽度 * 0.7);
    let 终点x = 起点x - 距离;

    return 滑动(起点x, 起点y, 终点x, 起点y, 时长);
}

/**
 * 右滑屏幕
 * @param {number} 距离 - 滑动距离，默认屏幕宽度的1/3
 * @param {number} 时长 - 滑动时长(毫秒)，默认500ms
 * @returns {boolean} 是否执行成功
 */
function 右滑(距离 = null, 时长 = 500) {
    let 屏幕宽度 = device.width;
    let 屏幕高度 = device.height;

    // 如果未指定距离，默认为屏幕宽度的1/3
    距离 = 距离 || Math.floor(屏幕宽度 / 3);

    let 起点y = Math.floor(屏幕高度 / 2);
    let 起点x = Math.floor(屏幕宽度 * 0.3);
    let 终点x = 起点x + 距离;

    return 滑动(起点x, 起点y, 终点x, 起点y, 时长);
}

/**
 * 输入文本
 * @param {string} 文本 - 要输入的文本
 * @returns {boolean} 是否执行成功
 */
function 输入文本(文本) {
    console.log("执行输入文本操作: " + 文本);

    if (操作模式 === 1) {
        // 无障碍模式
        input(文本);
        return true;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        // 注意需要转义双引号
        let 安全文本 = 文本.replace(/"/g, '\\"');
        let result = 执行Root命令('input text "' + 安全文本 + '"');
        let 成功 = result.code === 0;
        console.log(成功 ? "输入文本成功" : "输入文本失败: " + result.error);
        return 成功;
    }
}

/**
 * 按下按键
 * @param {number} 按键码 - 按键的keycode
 * @returns {boolean} 是否执行成功
 */
function 按键(按键码) {
    console.log("执行按键操作: " + 按键码);

    if (操作模式 === 1) {
        // 无障碍模式
        keycode(按键码);
        return true;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('input keyevent ' + 按键码);
        let 成功 = result.code === 0;
        console.log(成功 ? "按键操作成功" : "按键操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 常用按键码
 */
const 按键码 = {
    返回: 4,      // KEYCODE_BACK
    主页: 3,      // KEYCODE_HOME
    菜单: 82,     // KEYCODE_MENU
    搜索: 84,     // KEYCODE_SEARCH
    电源: 26,     // KEYCODE_POWER
    相机: 27,     // KEYCODE_CAMERA
    最近任务: 187, // KEYCODE_APP_SWITCH
    音量加: 24,   // KEYCODE_VOLUME_UP
    音量减: 25,   // KEYCODE_VOLUME_DOWN
    静音: 164,    // KEYCODE_VOLUME_MUTE
    亮度加: 221,  // KEYCODE_BRIGHTNESS_UP
    亮度减: 220,  // KEYCODE_BRIGHTNESS_DOWN
    上: 19,       // KEYCODE_DPAD_UP
    下: 20,       // KEYCODE_DPAD_DOWN
    左: 21,       // KEYCODE_DPAD_LEFT
    右: 22,       // KEYCODE_DPAD_RIGHT
    确定: 23,     // KEYCODE_DPAD_CENTER
    通话: 5,      // KEYCODE_CALL
    挂断: 6,      // KEYCODE_ENDCALL
    锁屏: 223     // KEYCODE_SLEEP
};

/**
 * 启动应用
 * @param {string} 包名 - 应用包名
 * @returns {boolean} 是否执行成功
 */
function 启动应用(包名) {
    console.log("启动应用: " + 包名);

    if (操作模式 === 1) {
        // 无障碍模式
        app.launch(包名);
        return true;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('am start -n ' + 包名 + '/.MainActivity');

        // 如果上面的命令失败，尝试使用更通用的方式
        if (result.code !== 0) {
            result = 执行Root命令('monkey -p ' + 包名 + ' -c android.intent.category.LAUNCHER 1');
        }

        let 成功 = result.code === 0;
        console.log(成功 ? "启动应用成功" : "启动应用失败: " + result.error);
        return 成功;
    }
}

/**
 * 关闭应用
 * @param {string} 包名 - 应用包名
 * @returns {boolean} 是否执行成功
 */
function 关闭应用(包名) {
    console.log("关闭应用: " + 包名);

    if (操作模式 === 1) {
        // 无障碍模式
        app.openAppSetting(包名);
        等待(1000);
        let 强行停止 = text("强行停止").findOne(2000);
        if (强行停止) {
            强行停止.click();
            等待(1000);
            let 确认 = text("确定").findOne(2000) ||
                text("确认").findOne() ||
                text("强行停止").findOne();
            if (确认) {
                确认.click();
                等待(1000);
                返回();
                return true;
            }
        }
        返回();
        return false;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('am force-stop ' + 包名);
        let 成功 = result.code === 0;
        console.log(成功 ? "关闭应用成功" : "关闭应用失败: " + result.error);
        return 成功;
    }
}

/**
 * 获取当前应用包名
 * @returns {string} 当前应用包名
 */
function 获取当前应用() {
    console.log("获取当前应用包名");

    if (操作模式 === 1) {
        // 无障碍模式
        let 包名 = currentPackage();
        console.log("当前应用包名: " + 包名);
        return 包名;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('dumpsys window | grep mCurrentFocus');
        if (result.code === 0) {
            let match = result.result.match(/mCurrentFocus.+?{.+?(\S+)\/(\S+)}/);
            if (match && match[1]) {
                let 当前包名 = match[1];
                console.log("当前应用包名: " + 当前包名);
                return 当前包名;
            }
        }
        console.log("获取当前应用包名失败");
        return null;
    }
}

/**
 * 等待指定时间
 * @param {number} 毫秒 - 等待时间(毫秒)
 */
function 等待(毫秒) {
    console.log("等待 " + 毫秒 + " 毫秒");
    sleep(毫秒);
}

/**
 * 检查应用是否安装
 * @param {string} 包名 - 应用包名
 * @returns {boolean} 是否已安装
 */
function 应用已安装(包名) {
    console.log("检查应用是否安装: " + 包名);

    // 使用执行Root命令函数
    let result = 执行Root命令('pm list packages | grep ' + 包名);
    let 已安装 = result.code === 0 && result.result.includes(包名);
    console.log(已安装 ? "应用已安装" : "应用未安装");
    return 已安装;
}

//=====================
//---------









/**
 * 强制退出小红书应用
 * 使用AutoJS官方API关闭小红书应用，无需root权限
 * @returns {boolean} - 是否成功退出
 */
function 强制退出小红书() {
    console.log("强制退出小红书应用");

    try {
        if (操作模式 === 1) {
            // 无障碍模式
            // 方法1: 使用app.killApp方法(AutoJS 6.0.0+)
            if (app.killApp && typeof app.killApp === 'function') {
                let result = app.killApp("com.xingin.xhs");
                console.log("使用app.killApp退出小红书: " + (result ? "成功" : "失败"));
                等待(1000);
                return result;
            }

            // 方法2: 使用app.openAppSetting打开应用设置，然后模拟点击"强行停止"按钮
            console.log("尝试通过应用设置强制停止小红书");
            app.openAppSetting("com.xingin.xhs");
            等待(1000); // 等待设置页面打开

            // 查找"强行停止"按钮并点击
            let 强行停止按钮 = textMatches(/(强.停止|强制.*停止|结束运行|停止运行|force.*stop)/).findOne(2000);
            if (强行停止按钮) {
                强行停止按钮.click();
                等待(1000);

                // 查找确认对话框中的"确定"按钮
                let 确认按钮 = textMatches(/(确定|确认|是|OK|强行停止)/).findOne(2000);
                if (确认按钮) {
                    确认按钮.click();
                    等待(1000);
                    console.log("已成功强制停止小红书");

                    // 返回到之前的界面
                    返回();
                    等待(500);
                    return true;
                }
            } else {
                // 如果没找到强行停止按钮，可能应用已经不在运行
                console.log("未找到强行停止按钮，应用可能已经不在运行");
                返回(); // 返回到之前的界面
                等待(500);
                return true;
            }

            console.log("无法通过应用设置强制停止小红书");
            // 返回到之前的界面
            返回();
            等待(500);
            return false;
        } else {
            // Root模式 - 使用执行Root命令函数
            console.log("使用Root权限强制停止小红书");
            let result = 执行Root命令('am force-stop com.xingin.xhs');
            let 成功 = result.code === 0;
            console.log(成功 ? "Root模式强制停止小红书成功" : "Root模式强制停止小红书失败: " + result.error);
            等待(1000);
            return 成功;
        }
    } catch (e) {
        console.error("强制退出小红书出错: " + e.message);
        // 尝试返回到之前的界面
        try {
            返回();
        } catch (e2) {
            // 忽略返回键错误
        }
        等待(500);
        return false;
    }
}

/**
 * 直接对链接文章进行点赞
 * @param {string} 链接 - 要点赞的文章链接
 * @returns {boolean} - 是否成功点赞
 */
function 直接点赞链接文章(链接) {
    console.log("调用兼容版本的直接点赞链接文章: " + 链接);

    // 调用新版本的直接互动链接文章函数，只执行点赞操作
    let 操作结果 = 直接互动链接文章(链接, true, false);

    // 返回点赞是否成功
    return 操作结果.成功 && 操作结果.点赞状态;
}

/**
 * 执行自动点赞主功能
 * @param {string} 文件路径 - 文件所在路径
 * @param {string} 文件名 - 文件名
 * @param {number} 最大操作数 - 最大操作链接数
 * @param {number} 操作间隔 - 操作间隔时间(秒)
 */
function 执行自动点赞(文件路径, 文件名, 最大操作数, 操作间隔) {
    console.log("调用兼容版本的执行自动点赞");
    // 调用新版本的执行自动互动函数，只执行点赞操作
    执行自动互动(文件路径, 文件名, 最大操作数, 操作间隔, true, false);
}

/**
 * 执行自动互动主功能
 * @param {string} 文件路径 - 文件所在路径
 * @param {string} 文件名 - 文件名
 * @param {number} 最大操作数 - 最大操作链接数
 * @param {number} 操作间隔 - 操作间隔时间(秒)
 * @param {boolean} 需要点赞 - 是否需要执行点赞操作
 * @param {boolean} 需要收藏 - 是否需要执行收藏操作
 */
function 执行自动互动(文件路径, 文件名, 最大操作数, 操作间隔, 需要点赞 = true, 需要收藏 = false) {
    // 读取链接文件
    let 完整路径 = 文件路径 + 文件名;
    console.log("准备读取链接文件: " + 完整路径);

    let 链接列表 = 读取链接文件(完整路径);
    if (!链接列表 || 链接列表.length === 0) {
        toast("链接文件读取失败或为空: " + 完整路径);
        console.error("链接文件读取失败或为空: " + 完整路径);
        return;
    }

    // 限制操作数量
    let 实际操作数 = Math.min(链接列表.length, 最大操作数);
    toast(`共读取到 ${链接列表.length} 条链接，计划处理 ${实际操作数} 条`);

    // 循环处理每个链接
    for (let i = 0; i < 实际操作数; i++) {
        try {
            let 链接 = 链接列表[i];
            toast(`处理第 ${i + 1} 条链接: ${链接}`);

            // 使用直接互动链接文章函数处理
            let 结果 = 直接互动链接文章(链接, 需要点赞, 需要收藏);

            if (结果.成功) {
                let 状态消息 = "";
                if (需要点赞) {
                    // 判断是新点赞还是已经点赞过
                    if (结果.点赞成功) {
                        状态消息 += "点赞成功";
                    } else if (结果.已完成点赞) {
                        状态消息 += "已点赞，跳过";
                    } else {
                        状态消息 += "点赞失败";
                    }
                }

                if (需要收藏) {
                    // 判断是新收藏还是已经收藏过
                    if (结果.收藏成功) {
                        状态消息 += 状态消息 ? ", 收藏成功" : "收藏成功";
                    } else if (结果.已完成收藏) {
                        状态消息 += 状态消息 ? ", 已收藏，跳过" : "已收藏，跳过";
                    } else {
                        状态消息 += 状态消息 ? ", 收藏失败" : "收藏失败";
                    }
                }

                toast(状态消息);
            } else {
                toast("处理链接失败: " + (结果.原因 || "未知原因"));
            }

            // 操作间隔
            for (let i = 0; i < 操作间隔; i++) {
                console.log(`等待 ${操作间隔 - i} 秒后处理下一条链接`);
                toast(`等待 ${操作间隔 - i} 秒后处理下一条链接`);
                sleep(1000);
            }

        } catch (e) {
            console.error("处理链接时出错: " + e.message);
            toast("处理链接时出错: " + e.message);
            等待(2000);
        }
    }

    toast("所有链接处理完成");
    console.log("所有链接处理完成");
}

/**
 * 执行互动操作
 * @param {boolean} 需要点赞 - 是否需要执行点赞操作
 * @param {boolean} 需要收藏 - 是否需要执行收藏操作
 * @returns {Object} 包含操作结果的对象
 */
function 执行互动操作(需要点赞 = true, 需要收藏 = false) {
    console.log(`执行互动操作: 点赞=${需要点赞}, 收藏=${需要收藏}`);

    // 只获取一次XML和状态，避免重复操作
    let xmlContent = 获取界面XML();
    if (!xmlContent) {
        console.log("获取界面XML失败，无法执行互动操作");
        return {
            点赞成功: false,
            收藏成功: false,
            已完成点赞: false,
            已完成收藏: false
        };
    }

    let 元素列表 = 提取文本元素(xmlContent);
    let 按钮信息 = 标记核心元素(元素列表);

    if (!按钮信息) {
        console.log("未找到交互按钮位置信息，无法执行互动操作");
        return {
            点赞成功: false,
            收藏成功: false,
            已完成点赞: false,
            已完成收藏: false
        };
    }

    // 检测当前状态
    let 当前状态 = { 已点赞: false, 已收藏: false };

    // 只在需要相应操作时检测状态，节省资源
    if (需要点赞 && 按钮信息.点赞数文本) {
        // 只检测点赞状态
        let 点赞状态 = 判断点赞状态(按钮信息);
        当前状态.已点赞 = 点赞状态;
        console.log(`当前点赞状态: ${当前状态.已点赞 ? "已点赞" : "未点赞"}`);
    }

    if (需要收藏 && 按钮信息.收藏数文本) {
        // 只检测收藏状态
        let 收藏状态 = 判断收藏状态(按钮信息);
        当前状态.已收藏 = 收藏状态;
        console.log(`当前收藏状态: ${当前状态.已收藏 ? "已收藏" : "未收藏"}`);
    }

    let 结果 = {
        点赞成功: false,    // 本次操作是否成功点赞
        收藏成功: false,    // 本次操作是否成功收藏
        已完成点赞: 当前状态.已点赞,  // 当前是否已点赞（包括之前已点赞的情况）
        已完成收藏: 当前状态.已收藏   // 当前是否已收藏（包括之前已收藏的情况）
    };

    // 检查是否是带分享按钮的界面
    let 带分享按钮 = !!按钮信息.分享按钮;

    // 执行点赞操作（如果需要点赞且尚未点赞）
    if (需要点赞 && !当前状态.已点赞 && 按钮信息.点赞数文本) {
        console.log("执行点赞操作");

        // 确定点击坐标
        let 点赞X, 点赞Y;
        let 是纯文本按钮 = 按钮信息.点赞数文本.文本 === "0" || 按钮信息.点赞数文本.文本 === "点赞" || 按钮信息.点赞数文本.文本 === "赞";

        if (是纯文本按钮) {
            // 直接点击文本按钮的中心位置
            点赞X = 按钮信息.点赞数文本.坐标.中心X;
            点赞Y = 按钮信息.点赞数文本.坐标.中心Y;
            console.log(`点击文本点赞按钮中心位置 (${点赞X}, ${点赞Y})`);
        } else if (带分享按钮) {
            // 带分享按钮(视频)时，点赞按钮在点赞数上方
            点赞X = 按钮信息.点赞数文本.坐标.中心X;
            点赞Y = 按钮信息.点赞数文本.坐标.中心Y - 75; // Y向上偏移75像素
            console.log(`视频界面：点击点赞数上方Y偏移位置 (${点赞X}, ${点赞Y})`);
        } else {
            // 无分享按钮(图文)时，点赞按钮在点赞数左侧
            点赞X = 按钮信息.点赞数文本.坐标.左 - 75; // X向左偏移75像素
            点赞Y = 按钮信息.点赞数文本.坐标.中心Y;
            console.log(`图文界面：点击点赞数左侧X偏移位置 (${点赞X}, ${点赞Y})`);
        }

        点击(点赞X, 点赞Y);
        等待(1000);

        // 再次检查状态，确认是否点赞成功
        if (需要点赞) {
            xmlContent = 获取界面XML();
            if (xmlContent) {
                元素列表 = 提取文本元素(xmlContent);
                按钮信息 = 标记核心元素(元素列表);
                if (按钮信息) {
                    let 新点赞状态 = 判断点赞状态(按钮信息);
                    结果.已完成点赞 = 新点赞状态;
                    结果.点赞成功 = 新点赞状态 && !当前状态.已点赞;  // 只有从未点赞变为已点赞才算本次点赞成功
                    console.log(`点赞操作后状态: 已点赞=${新点赞状态}, 点赞成功=${结果.点赞成功}`);
                } else {
                    // 如果无法获取新状态，假设成功
                    结果.已完成点赞 = true;
                    结果.点赞成功 = true;
                }
            } else {
                // 如果无法获取XML，假设成功
                结果.已完成点赞 = true;
                结果.点赞成功 = true;
            }
        }
    } else if (需要点赞) {
        if (当前状态.已点赞) {
            console.log("已经点过赞，无需重复操作");
            结果.已完成点赞 = true;
            结果.点赞成功 = false;  // 不是本次操作成功的，而是之前已经点过赞
        } else {
            console.log("无法找到点赞数文本，无法执行点赞操作");
        }
    }

    // 执行收藏操作（如果需要收藏且尚未收藏）
    if (需要收藏 && !当前状态.已收藏 && 按钮信息.收藏数文本) {
        console.log("执行收藏操作");

        // 确定点击坐标
        let 收藏X, 收藏Y;
        let 是纯文本按钮 = 按钮信息.收藏数文本.文本 === "0" || 按钮信息.收藏数文本.文本 === "收藏";

        if (是纯文本按钮) {
            // 直接点击文本按钮的中心位置
            收藏X = 按钮信息.收藏数文本.坐标.中心X;
            收藏Y = 按钮信息.收藏数文本.坐标.中心Y;
            console.log(`点击文本收藏按钮中心位置 (${收藏X}, ${收藏Y})`);
        } else if (带分享按钮) {
            // 带分享按钮(视频)时，收藏按钮在收藏数上方
            收藏X = 按钮信息.收藏数文本.坐标.中心X;
            收藏Y = 按钮信息.收藏数文本.坐标.中心Y - 75; // Y向上偏移75像素
            console.log(`视频界面：点击收藏数上方Y偏移位置 (${收藏X}, ${收藏Y})`);
        } else {
            // 无分享按钮(图文)时，收藏按钮在收藏数左侧
            收藏X = 按钮信息.收藏数文本.坐标.左 - 75; // X向左偏移75像素
            收藏Y = 按钮信息.收藏数文本.坐标.中心Y;
            console.log(`图文界面：点击收藏数左侧X偏移位置 (${收藏X}, ${收藏Y})`);
        }

        点击(收藏X, 收藏Y);
        等待(1000);

        // 再次检查状态，确认是否收藏成功
        if (需要收藏) {
            xmlContent = 获取界面XML();
            if (xmlContent) {
                元素列表 = 提取文本元素(xmlContent);
                按钮信息 = 标记核心元素(元素列表);
                if (按钮信息) {
                    let 新收藏状态 = 判断收藏状态(按钮信息);
                    结果.已完成收藏 = 新收藏状态;
                    结果.收藏成功 = 新收藏状态 && !当前状态.已收藏;  // 只有从未收藏变为已收藏才算本次收藏成功
                    console.log(`收藏操作后状态: 已收藏=${新收藏状态}, 收藏成功=${结果.收藏成功}`);
                } else {
                    // 如果无法获取新状态，假设成功
                    结果.已完成收藏 = true;
                    结果.收藏成功 = true;
                }
            } else {
                // 如果无法获取XML，假设成功
                结果.已完成收藏 = true;
                结果.收藏成功 = true;
            }
        }
    } else if (需要收藏) {
        if (当前状态.已收藏) {
            console.log("已经收藏过，无需重复操作");
            结果.已完成收藏 = true;
            结果.收藏成功 = false;  // 不是本次操作成功的，而是之前已经收藏过
        } else {
            console.log("无法找到收藏数文本，无法执行收藏操作");
        }
    }

    console.log(`互动操作结果: 点赞=${结果.已完成点赞 ? "已完成" : "未完成"}(${结果.点赞成功 ? "本次成功" : "非本次"}), 收藏=${结果.已完成收藏 ? "已完成" : "未完成"}(${结果.收藏成功 ? "本次成功" : "非本次"})`);
    return 结果;
}

/**
 * 直接互动链接文章
 * @param {string} 链接 - 文章链接
 * @param {boolean} 需要点赞 - 是否需要点赞
 * @param {boolean} 需要收藏 - 是否需要收藏
 * @returns {Object} 包含操作结果的对象
 */
function 直接互动链接文章(链接, 需要点赞 = true, 需要收藏 = false) {
    console.log(`直接互动链接文章: ${链接}`);
    console.log(`操作选项: 点赞=${需要点赞}, 收藏=${需要收藏}`);

    // 强制退出小红书
    强制退出小红书();
    等待(1000);

    // 打开链接
    app.openUrl(链接);
    console.log("已打开链接");
    等待(3000); // 等待页面加载

    // 处理打开流程
    if (!处理打开流程()) {
        console.log("处理打开流程失败");
        return {
            成功: false,
            原因: "无法进入小红书",
            已完成点赞: false,
            已完成收藏: false,
            点赞成功: false,
            收藏成功: false
        };
    }

    等待(2000); // 等待页面完全加载

    // 获取页面信息
    let 页面信息 = 获取页面信息();
    console.log(`页面信息: 作者=[${页面信息.作者}], 标题=[${页面信息.标题}], 是否视频=${页面信息.是否视频}`);

    // 先检查当前状态，如果已经满足需求，可以直接返回
    let xmlContent = 获取界面XML();
    if (xmlContent) {
        let 元素列表 = 提取文本元素(xmlContent);
        let 按钮信息 = 标记核心元素(元素列表);

        if (按钮信息) {
            // 根据需要选择性地检查按钮状态
            let 当前状态 = { 已点赞: false, 已收藏: false };

            if (需要点赞 && 按钮信息.点赞数文本) {
                当前状态.已点赞 = 判断点赞状态(按钮信息);
                console.log(`当前点赞状态: ${当前状态.已点赞 ? "已点赞" : "未点赞"}`);
            }

            if (需要收藏 && 按钮信息.收藏数文本) {
                当前状态.已收藏 = 判断收藏状态(按钮信息);
                console.log(`当前收藏状态: ${当前状态.已收藏 ? "已收藏" : "未收藏"}`);
            }

            // 如果只需要点赞且已经点赞过，或者只需要收藏且已经收藏过，或者两者都已完成，则直接返回
            if ((需要点赞 && !需要收藏 && 当前状态.已点赞) ||
                (!需要点赞 && 需要收藏 && 当前状态.已收藏) ||
                (需要点赞 && 需要收藏 && 当前状态.已点赞 && 当前状态.已收藏)) {
                console.log("当前状态已满足需求，无需操作");
                返回主界面();
                return {
                    成功: true,
                    已完成点赞: 当前状态.已点赞,
                    已完成收藏: 当前状态.已收藏,
                    点赞成功: false,  // 不是本次操作成功的，而是之前已经点过赞
                    收藏成功: false   // 不是本次操作成功的，而是之前已经收藏过
                };
            }
        }
    }

    // 判断是视频还是图文，进行相应等待或滑动
    if (页面信息.是否视频) {
        // 如果是视频，随机等待5-10秒
        let 视频等待时间 = 5000 + Math.floor(Math.random() * 5000);
        console.log("视频内容，等待 " + (视频等待时间 / 1000) + " 秒");
        等待(视频等待时间);
    } else {
        // 如果是图文，随机上划并延时
        let 图文等待时间 = 1000 + Math.floor(Math.random() * 2000);
        console.log("图文内容，等待 " + (图文等待时间 / 1000) + " 秒");
        等待(图文等待时间);

        // 随机上划
        let 屏幕宽度 = device.width;
        let 屏幕高度 = device.height;
        let 起点Y = 屏幕高度 * (0.7 + Math.random() * 0.2);
        let 终点Y = 屏幕高度 * (0.3 + Math.random() * 0.2);
        let X坐标 = 屏幕宽度 * (0.4 + Math.random() * 0.2);
        let 滑动时间 = 300 + Math.floor(Math.random() * 500);
        滑动(X坐标, 起点Y, X坐标, 终点Y, 滑动时间);
        等待(1000 + Math.floor(Math.random() * 1000));
    }

    // 直接在当前页面执行互动操作
    console.log("直接在当前页面执行互动操作");
    let 互动结果 = 执行互动操作(需要点赞, 需要收藏);

    // 如果当前页面操作失败，尝试通过首页打开文章
    if ((需要点赞 && !互动结果.已完成点赞) || (需要收藏 && !互动结果.已完成收藏)) {
        console.log("当前页面操作未完全成功，尝试通过首页打开文章");

        // 返回主界面
        if (返回主界面()) {
            等待(2000);

            // 尝试在首页找到并点击第一篇文章
            if (点击首篇文章()) {
                等待(3000);

                // 获取新页面信息
                let 新页面信息 = 获取页面信息();
                console.log(`新页面信息: 作者=[${新页面信息.作者}], 标题=[${新页面信息.标题}]`);

                // 比较是否是同一篇文章
                if (比较页面信息(页面信息, 新页面信息)) {
                    console.log("成功通过首页打开同一篇文章");

                    // 根据内容类型进行等待或滑动
                    if (新页面信息.是否视频) {
                        let 视频等待时间 = 3000 + Math.floor(Math.random() * 3000);
                        console.log("视频内容，等待 " + (视频等待时间 / 1000) + " 秒");
                        等待(视频等待时间);
                    } else {
                        let 图文等待时间 = 1000 + Math.floor(Math.random() * 1000);
                        console.log("图文内容，等待 " + (图文等待时间 / 1000) + " 秒");
                        等待(图文等待时间);

                        // 随机上划
                        let 屏幕宽度 = device.width;
                        let 屏幕高度 = device.height;
                        滑动(
                            屏幕宽度 * 0.5,
                            屏幕高度 * 0.7,
                            屏幕宽度 * 0.5,
                            屏幕高度 * 0.3,
                            300 + Math.floor(Math.random() * 300)
                        );
                        等待(1000);
                    }

                    // 再次执行互动操作
                    console.log("在首页打开的文章中执行互动操作");
                    互动结果 = 执行互动操作(需要点赞, 需要收藏);
                } else {
                    console.log("首页打开的不是同一篇文章，尝试重新通过链接打开");

                    // 返回并重新通过链接打开
                    返回主界面();
                    等待(1000);

                    // 重新通过链接打开
                    app.openUrl(链接);
                    console.log("重新通过链接打开");
                    等待(3000);

                    if (处理打开流程()) {
                        等待(2000);

                        // 根据内容类型进行等待或滑动
                        let 重新页面信息 = 获取页面信息();
                        if (重新页面信息.是否视频) {
                            let 视频等待时间 = 3000 + Math.floor(Math.random() * 3000);
                            console.log("视频内容，等待 " + (视频等待时间 / 1000) + " 秒");
                            等待(视频等待时间);
                        } else {
                            let 图文等待时间 = 1000 + Math.floor(Math.random() * 1000);
                            console.log("图文内容，等待 " + (图文等待时间 / 1000) + " 秒");
                            等待(图文等待时间);

                            // 随机上划
                            let 屏幕宽度 = device.width;
                            let 屏幕高度 = device.height;
                            滑动(
                                屏幕宽度 * 0.5,
                                屏幕高度 * 0.7,
                                屏幕宽度 * 0.5,
                                屏幕高度 * 0.3,
                                300 + Math.floor(Math.random() * 300)
                            );
                            等待(1000);
                        }

                        // 最后一次尝试执行互动操作
                        console.log("在重新打开的链接中执行互动操作");
                        互动结果 = 执行互动操作(需要点赞, 需要收藏);
                    }
                }
            }
        }
    }

    // 返回主界面
    返回主界面();

    return {
        成功: true,
        已完成点赞: 互动结果.已完成点赞,
        已完成收藏: 互动结果.已完成收藏,
        点赞成功: 互动结果.点赞成功,
        收藏成功: 互动结果.收藏成功
    };
}

// 替换原有的执行点赞函数
/**
 * 执行点赞操作（兼容旧版本调用）
 * @returns {boolean} - 是否成功点赞
 */
function 执行点赞() {
    console.log("执行点赞操作（旧版本兼容）");
    let 操作结果 = 执行互动操作(true, false);
    return 操作结果.已完成点赞;
}

/**
 * 调试交互按钮
 * 检测按钮状态，使用新的X和Y偏移范围
 * @param {boolean} 需要点赞 - 是否需要检测点赞状态
 * @param {boolean} 需要收藏 - 是否需要检测收藏状态
 */
function 调试交互按钮(需要点赞 = true, 需要收藏 = true) {
    console.log("开始检测交互按钮状态");
    console.log(`检测选项: 点赞=${需要点赞}, 收藏=${需要收藏}`);

    // 获取界面元素
    let xmlContent = 获取界面XML();
    if (!xmlContent) {
        console.log("获取界面XML失败");
        return;
    }

    let 元素列表 = 提取文本元素(xmlContent);
    let 按钮信息 = 标记核心元素(元素列表);

    if (!按钮信息) {
        console.log("未找到按钮位置");
        return;
    }

    // 输出界面类型信息
    if (按钮信息.分享按钮) {
        console.log("检测到带有分享按钮的视频界面");
    } else {
        console.log("检测到标准界面（无分享按钮）");
    }

    // 根据需要检查点赞状态
    let 点赞状态 = false;
    if (需要点赞) {
        if (!按钮信息.点赞数文本) {
            console.log("未找到点赞数文本，无法检测点赞状态");
        } else {
            点赞状态 = 判断点赞状态(按钮信息);
        }
    } else {
        console.log("跳过点赞状态检测");
    }

    // 根据需要检查收藏状态
    let 收藏状态 = false;
    if (需要收藏) {
        if (!按钮信息.收藏数文本) {
            console.log("未找到收藏数文本，无法检测收藏状态");
        } else {
            收藏状态 = 判断收藏状态(按钮信息);
        }
    } else {
        console.log("跳过收藏状态检测");
    }

    console.log("\n===== 当前按钮状态 =====");
    if (需要点赞) {
        console.log(`点赞状态: ${点赞状态 ? "已点赞" : "未点赞"}`);
    }
    if (需要收藏) {
        console.log(`收藏状态: ${收藏状态 ? "已收藏" : "未收藏"}`);
    }
}

/**
 * 标记核心元素
 * @param {Array} 元素列表 - 元素数组
 * @returns {Object|null} 按钮信息对象或null
 */
function 标记核心元素(元素列表) {
    let 屏幕高度 = device.height;
    let 屏幕宽度 = device.width;

    // 1. 找顶部用户名（图文）
    let 顶部用户名 = null;
    for (let 元素 of 元素列表) {
        if (
            元素.坐标.中心Y < 屏幕高度 * 0.3 &&
            元素.文本.length >= 2 && 元素.文本.length <= 12 &&
            !/^\d+$/.test(元素.文本) &&
            !["关注", "说点什么...", "评论", "发弹幕", "分享"].some(k => 元素.文本.includes(k))
        ) {
            顶部用户名 = 元素;
            break;
        }
    }

    // 只在视频时找底部昵称和互动数
    let 底部用户名 = null;
    let 点赞 = null, 收藏 = null, 评论 = null;

    // 先查找分享按钮，判断是否是视频界面
    let 分享按钮 = 元素列表.find(e => e.文本 === "分享");
    let 是视频界面 = !!分享按钮;

    // 查找文本形式的点赞、收藏按钮
    let 点赞按钮 = 元素列表.find(e => e.文本 === "点赞" || e.文本 === "赞");
    let 收藏按钮 = 元素列表.find(e => e.文本 === "收藏");

    if (!顶部用户名) {
        // 1. 找"关注"或"已关注"按钮
        let 关注按钮 = 元素列表.find(e =>
            e.坐标.中心Y > 屏幕高度 * 0.5 &&
            (e.文本 === "关注" || e.文本 === "已关注")
        );

        // 2. 昵称候选
        let 昵称候选 = 元素列表.filter(e =>
            e.坐标.中心Y > 屏幕高度 * 0.5 &&
            e.文本.length >= 2 && e.文本.length <= 12 &&
            !/^[\d.]+万?$/.test(e.文本) &&
            !["关注", "说点什么...", "评论", "发弹幕", "分享", "相关搜索", "课程咨询"].some(k => e.文本.includes(k)) &&
            !e.文本.includes(":")
        );

        if (昵称候选.length > 0) {
            if (关注按钮) {
                // 只选X在关注按钮左侧的，且Y最接近
                let 左侧候选 = 昵称候选
                    .filter(e => e.坐标.中心X < 关注按钮.坐标.中心X)
                    .sort((a, b) => {
                        // Y越接近关注按钮越优先
                        let dyA = Math.abs(a.坐标.中心Y - 关注按钮.坐标.中心Y);
                        let dyB = Math.abs(b.坐标.中心Y - 关注按钮.坐标.中心Y);
                        if (dyA !== dyB) return dyA - dyB;
                        // X越靠近关注按钮越优先
                        return 关注按钮.坐标.中心X - a.坐标.中心X - (关注按钮.坐标.中心X - b.坐标.中心X);
                    });
                if (左侧候选.length > 0) {
                    底部用户名 = 左侧候选[0];
                } else {
                    // 没有左侧的，退回原有Y最大X最小
                    昵称候选.sort((a, b) => {
                        if (b.坐标.中心Y !== a.坐标.中心Y) {
                            return b.坐标.中心Y - a.坐标.中心Y;
                        }
                        return a.坐标.中心X - b.坐标.中心X;
                    });
                    底部用户名 = 昵称候选[0];
                }
            } else {
                // 没有关注按钮，退回原有Y最大X最小
                昵称候选.sort((a, b) => {
                    if (b.坐标.中心Y !== a.坐标.中心Y) {
                        return b.坐标.中心Y - a.坐标.中心Y;
                    }
                    return a.坐标.中心X - b.坐标.中心X;
                });
                底部用户名 = 昵称候选[0];
            }
        }

        // 3. 点赞/收藏/评论数：底部剩下的数字（含"万"）
        let 底部元素 = 元素列表.filter(e => e.坐标.中心Y > 屏幕高度 * 0.7);
        let 互动数字 = 底部元素.filter(e =>
            /^[\d.]+万?$/.test(e.文本)
        );
        // 排除昵称
        if (底部用户名) {
            互动数字 = 互动数字.filter(e => e.文本 !== 底部用户名.文本);
        }
        // 按X坐标排序
        互动数字.sort((a, b) => a.坐标.中心X - b.坐标.中心X);
        点赞 = 互动数字[0] || 点赞按钮; // 如果没有数字，使用文本按钮
        收藏 = 互动数字[1] || 收藏按钮; // 如果没有数字，使用文本按钮
        评论 = 互动数字[2] || null;
    } else {
        // 图文页底部数字（精准：说点什么...右侧依次为点赞/收藏/评论）
        let 说点什么 = 元素列表.find(e =>
            e.文本 === "说点什么..." && e.坐标.中心Y > 屏幕高度 * 0.8
        );
        let 分界X = 说点什么 ? 说点什么.坐标.中心X : 0;
        let 底部数字 = 元素列表.filter(e =>
            e.坐标.中心Y > 屏幕高度 * 0.8 &&
            (/^[\d.]+万$/.test(e.文本) || /^\d+$/.test(e.文本) || ["点赞", "收藏", "评论", "赞"].includes(e.文本)) &&
            e.坐标.中心X > 分界X
        );
        底部数字.sort((a, b) => a.坐标.中心X - b.坐标.中心X);
        点赞 = 底部数字[0] || 点赞按钮;
        收藏 = 底部数字[1] || 收藏按钮;
        评论 = 底部数字[2] || null;
        // 如果为文本，视为0
        if (点赞 && (点赞.文本 === "点赞" || 点赞.文本 === "赞")) 点赞.文本 = "0";
        if (收藏 && 收藏.文本 === "收藏") 收藏.文本 = "0";
        if (评论 && 评论.文本 === "评论") 评论.文本 = "0";
    }

    // 打印标记结果
    if (顶部用户名) {
        打印元素详细信息(顶部用户名, "图文用户名");
    }
    if (底部用户名) {
        打印元素详细信息(底部用户名, "用户昵称");
    }
    if (点赞) {
        打印元素详细信息(点赞, "点赞数");
    }
    if (收藏) {
        打印元素详细信息(收藏, "收藏数");
    }
    if (评论) {
        打印元素详细信息(评论, "评论数");
    }

    // 判断内容类型
    let 是图文 = !!顶部用户名;
    console.log(`内容类型: ${是图文 ? "图文" : "视频"}`);

    // 无论是否有分享按钮，都进行按钮状态检测
    // 只要有点赞数和收藏数就可以进行检测
    if (点赞 && 收藏) {
        if (分享按钮) {
            console.log("检测到分享按钮，使用上方Y偏移检测方式（视频界面）");
        } else {
            console.log("未检测到分享按钮，使用左侧X偏移检测方式（图文界面）");
        }

        let 按钮信息 = {
            点赞数文本: 点赞,
            收藏数文本: 收藏,
            评论数文本: 评论,
            分享按钮: 分享按钮
        };

        return 按钮信息;
    } else {
        console.log("未找到足够的互动元素，无法进行按钮状态检测");
        return null;
    }
}

/**
 * 打印元素详细信息
 * @param {Object} 元素 - 元素对象 
 * @param {string} 标签 - 元素标签
 */
function 打印元素详细信息(元素, 标签) {
    if (!元素) return;
    let 属性文本 = '';
    if (元素.原始属性) {
        for (let k in 元素.原始属性) {
            属性文本 += `${k}: ${元素.原始属性[k]}, `;
        }
        属性文本 = 属性文本.replace(/, $/, '');
    }
    console.log(`${标签}: [${元素.文本}], 坐标: (${元素.坐标.中心X}, ${元素.坐标.中心Y})${属性文本 ? ' | ' + 属性文本 : ''}`);
}

/**
 * 读取链接文件
 * @param {string} 完整路径 - 链接文件的完整路径
 * @returns {Array} 链接列表数组
 */
function 读取链接文件(完整路径) {
    try {
        let 文件 = files.read(完整路径);
        let 链接列表 = 文件.split("\n");

        // 过滤空行
        链接列表 = 链接列表.filter(链接 => 链接.trim() !== "");

        console.log(`成功读取链接文件，共 ${链接列表.length} 条链接`);
        return 链接列表;
    } catch (e) {
        console.error("读取链接文件失败: " + e);
        return [];
    }
}

/**
 * 处理从浏览器打开到小红书的全流程
 * @returns {boolean} 是否成功进入小红书
 */
function 处理打开流程() {
    // 设置最大尝试次数
    let 最大尝试次数 = 20;
    let 当前尝试次数 = 0;
    let 连续失败次数 = 0;

    console.log("开始处理打开流程，最大尝试次数: " + 最大尝试次数);

    while (当前尝试次数 < 最大尝试次数) {
        console.log("当前尝试次数: " + (当前尝试次数 + 1));

        // 获取界面XML
        let xmlContent = 获取界面XML();
        if (!xmlContent) {
            连续失败次数++;

            // 如果连续3次获取XML失败，尝试其他方法
            if (连续失败次数 >= 3) {
                console.log("连续多次获取XML失败，尝试其他方法处理");

                // 尝试检查当前应用是否已经是小红书
                if (检查是否进入小红书()) {
                    console.log("已检测到进入小红书应用");
                    return true;
                }

                // 尝试点击屏幕中心位置，可能会触发一些按钮
                let 屏幕中心X = Math.floor(device.width / 2);
                let 屏幕中心Y = Math.floor(device.height / 2);
                console.log(`尝试点击屏幕中心位置: (${屏幕中心X}, ${屏幕中心Y})`);
                点击(屏幕中心X, 屏幕中心Y);
                等待(2000);

                连续失败次数 = 0; // 重置连续失败次数
            }

            当前尝试次数++;
            等待(1500); // 失败后等待更长时间
            continue;
        }

        连续失败次数 = 0; // 成功获取XML，重置连续失败计数

        let 元素列表 = 提取文本元素(xmlContent);

        // 定义需要点击的关键词 - 精准匹配用
        let 关键词列表 = [
            "始终", "Chrome", "确定", "展开", "打开 APP 查看", "App内打开", "打开", "继续",
            "在不登录账号的情况下使用", "同意", "知道了",
            "浏览", "允许", "确认", "继续访问", "我同意",
            // 添加更多可能的精准按钮文本
            "打开方式", "选择浏览器", "使用浏览器打开", "使用Chrome打开",
            "仅本次", "总是", "取消", "是", "否"
        ];

        let 已点击 = false;

        // 检查每个关键词
        for (let i = 0; i < 关键词列表.length; i++) {
            let 关键词 = 关键词列表[i];

            // 寻找精准匹配关键词的文本
            for (let j = 0; j < 元素列表.length; j++) {
                let 项目 = 元素列表[j];

                // 改为精准匹配
                if (项目.文本 === 关键词) {
                    console.log("找到精准匹配关键词: " + 关键词);

                    // 获取元素中心坐标
                    let x = 项目.坐标.中心X;
                    let y = 项目.坐标.中心Y;

                    // 点击该位置
                    点击(x, y);
                    console.log("点击坐标: " + x + ", " + y);

                    已点击 = true;
                    等待(1000); // 等待点击后的反应
                    if (关键词 === "打开 APP 查看" || 关键词 === "App内打开") {
                        等待(5000);
                    }
                    break;
                }
            }

            if (已点击) {
                break;
            }
        }

        // 检查是否已经进入小红书应用
        if (检查是否进入小红书()) {
            console.log("已成功进入小红书");
            return true;
        } else {
            console.log("尚未进入小红书，继续尝试");
        }

        // 如果没有找到任何可点击的元素，增加尝试计数
        if (!已点击) {
            当前尝试次数++;
            console.log("本次尝试未找到可点击元素，尝试次数增加到: " + 当前尝试次数);
            等待(1000);
        }
        等待(1000);
    }

    console.log("达到最大尝试次数，未能成功进入小红书");
    return false;
}

/**
 * 检查是否已进入小红书
 * @returns {boolean} 是否在小红书中
 */
function 检查是否进入小红书() {
    // 使用Root命令获取当前应用包名
    let result = 执行Root命令('dumpsys window | grep mCurrentFocus');
    if (result.code === 0) {
        let match = result.result.match(/mCurrentFocus.+?{.+?(\S+)\/(\S+)}/);
        if (match && match[1]) {
            let 当前包名 = match[1];
            console.log("当前应用包名: " + 当前包名);
            return 当前包名 === "com.xingin.xhs";
        }
    }

    console.log("无法确认是否已进入小红书");
    return false;
}

/**
 * 获取当前页面信息
 * @returns {Object} 包含页面信息的对象
 */
function 获取页面信息() {
    console.log("获取当前页面信息");

    // 获取界面XML
    let xmlContent = 获取界面XML();
    if (!xmlContent) {
        console.log("获取界面XML失败");
        return {
            是否视频: false,
            作者: "未知",
            标题: "未知",
            内容: "未知"
        };
    }

    let 元素列表 = 提取文本元素(xmlContent);

    // 标记核心元素，这会识别出用户昵称、点赞数等
    标记核心元素(元素列表);

    // 判断是否为视频
    let 是否视频 = false;

    // 查找分享按钮，视频页面通常有分享按钮
    let 分享按钮 = 元素列表.find(e => e.文本 === "分享");
    if (分享按钮) {
        是否视频 = true;
        console.log("检测到分享按钮，判断为视频页面");
    }

    // 查找底部区域的元素
    let 底部元素 = 元素列表.filter(e => e.坐标.中心Y > device.height * 0.7);

    // 查找顶部区域的元素
    let 顶部元素 = 元素列表.filter(e => e.坐标.中心Y < device.height * 0.3);

    // 尝试找出作者名称
    let 作者 = "未知";
    let 可能的作者元素 = 元素列表.find(e =>
        e.文本.length >= 2 && e.文本.length <= 15 &&
        !e.文本.match(/^\d+$/) &&
        !["关注", "点赞", "收藏", "评论", "分享"].includes(e.文本) &&
        e.坐标.中心Y < device.height * 0.4
    );

    if (可能的作者元素) {
        作者 = 可能的作者元素.文本;
        console.log("找到可能的作者: " + 作者);
    }

    // 尝试找出标题
    let 标题 = "未知";
    let 可能的标题元素 = 顶部元素.find(e =>
        e.文本.length > 5 &&
        !e.文本.match(/^\d+$/) &&
        !["关注", "点赞", "收藏", "评论", "分享"].includes(e.文本)
    );

    if (可能的标题元素) {
        标题 = 可能的标题元素.文本;
        console.log("找到可能的标题: " + 标题);
    }

    // 尝试找出内容
    let 内容 = "未知";
    let 可能的内容元素 = 元素列表.filter(e =>
        e.文本.length > 10 &&
        e.坐标.中心Y > device.height * 0.3 &&
        e.坐标.中心Y < device.height * 0.7
    );

    if (可能的内容元素.length > 0) {
        // 取最长的文本作为内容
        可能的内容元素.sort((a, b) => b.文本.length - a.文本.length);
        内容 = 可能的内容元素[0].文本;
        console.log("找到可能的内容: " + 内容.substring(0, 20) + "...");
    }

    return {
        是否视频: 是否视频,
        作者: 作者,
        标题: 标题,
        内容: 内容
    };
}

/**
 * 返回小红书主界面
 * @returns {boolean} 是否成功返回主界面
 */
function 返回主界面() {
    console.log("尝试返回小红书主界面");

    // 最多尝试5次返回操作
    for (let i = 0; i < 5; i++) {
        console.log("执行第 " + (i + 1) + " 次返回操作");
        返回();
        等待(1500);

        // 检查是否已经回到主界面
        let xmlContent = 获取界面XML();
        if (!xmlContent) continue;

        let 元素列表 = 提取文本元素(xmlContent);

        // 主界面特征：底部有"首页"、"购物"、"消息"等标签
        let 首页标签 = 元素列表.find(e => e.文本 === "首页");
        let 购物标签 = 元素列表.find(e => e.文本 === "购物");
        let 消息标签 = 元素列表.find(e => e.文本 === "消息");

        if (首页标签 || 购物标签 || 消息标签) {
            console.log("已返回主界面");
            return true;
        }
    }

    console.log("无法返回主界面，尝试强制退出小红书");
    强制退出小红书();
    return false;
}

/**
 * 执行需要Root权限的shell命令
 * @param {string} 命令 - 要执行的shell命令
 * @returns {Object} 包含执行结果的对象
 */
function 执行Root命令(命令) {
    console.log("执行Root命令: " + 命令);

    // 检查是否已经获取root权限
    if (!root会话) {
        if (!初始化Root权限()) {
            console.log("未获取到root权限，无法执行命令");
            return {
                code: -1,
                result: "",
                error: "未获取root权限"
            };
        }
    }

    // 使用su -c执行命令
    try {
        let result = shell("su -c '" + 命令 + "'", true);

        if (result.code === 0) {
            console.log("命令执行成功");
            return {
                code: 0,
                result: result.result,
                error: ""
            };
        } else {
            console.log("命令执行失败: " + result.error);
            return {
                code: result.code,
                result: result.result,
                error: result.error
            };
        }
    } catch (e) {
        console.error("执行命令出错: " + e);
        return {
            code: -1,
            result: "",
            error: e.toString()
        };
    }
}

/**
 * 获取交互按钮位置（基于文本偏移）
 * @param {Array} 文本元素列表 - 文本元素数组
 * @returns {Object} 包含点赞、收藏和评论按钮位置的对象
 */
function 获取交互按钮位置(文本元素列表) {
    // 此函数已被标记核心元素函数替代，保留函数名以兼容旧代码
    console.log("警告：获取交互按钮位置函数已被标记核心元素函数替代");
    return 标记核心元素(文本元素列表);
}

/**
 * 判断按钮状态
 * @param {Object} 按钮信息 - 包含点赞数文本、收藏数文本等信息的对象
 * @returns {Object} 包含已点赞和已收藏状态的对象
 */
function 判断按钮状态(按钮信息) {
    console.log("开始判断按钮状态");

    if (!按钮信息 || !按钮信息.点赞数文本) {
        console.log("按钮信息不完整，无法判断状态");
        return {
            已点赞: false,
            已收藏: false
        };
    }

    // 检查是否是带分享按钮的界面
    let 带分享按钮 = !!按钮信息.分享按钮;
    console.log(`界面类型: ${带分享按钮 ? "带分享按钮(视频)" : "无分享按钮(图文)"}`);

    // 分别判断点赞和收藏状态
    let 已点赞 = 判断点赞状态(按钮信息);
    let 已收藏 = 按钮信息.收藏数文本 ? 判断收藏状态(按钮信息) : false;

    console.log(`按钮状态: 已点赞=${已点赞}, 已收藏=${已收藏}`);
    return {
        已点赞: 已点赞,
        已收藏: 已收藏
    };
}

/**
 * 判断点赞状态
 * @param {Object} 按钮信息 - 包含点赞数文本等信息的对象
 * @returns {boolean} 是否已点赞
 */
function 判断点赞状态(按钮信息) {
    console.log("开始判断点赞状态");

    if (!按钮信息 || !按钮信息.点赞数文本) {
        console.log("按钮信息不完整，无法判断点赞状态");
        return false;
    }

    // 检查是否是带分享按钮的界面
    let 带分享按钮 = !!按钮信息.分享按钮;

    // 截取屏幕
    let img = captureScreen();
    if (!img) {
        console.log("截图失败，无法判断点赞状态");
        return false;
    }

    // 点赞按钮状态检测
    let 已点赞 = false;

    // 检查是否是纯文本按钮
    let 是纯文本按钮 = 按钮信息.点赞数文本.文本 === "0" || 按钮信息.点赞数文本.文本 === "点赞" || 按钮信息.点赞数文本.文本 === "赞";

    if (是纯文本按钮) {
        // 纯文本按钮时，直接检测按钮本身的颜色
        let x = 按钮信息.点赞数文本.坐标.中心X;
        let y = 按钮信息.点赞数文本.坐标.中心Y;

        // 获取该点的颜色
        let color = images.pixel(img, x, y);

        // 转换为RGB
        let r = colors.red(color);
        let g = colors.green(color);
        let b = colors.blue(color);

        // 点赞颜色匹配
        if (颜色匹配(r, g, b, 颜色配置.点赞颜色, 颜色配置.颜色容差)) {
            已点赞 = true;
            let hexColor = RGB转十六进制(r, g, b);
            console.log(`检测到点赞状态为已点赞，坐标: (${x}, ${y}), 颜色: #${hexColor} rgb(${r},${g},${b})`);
        } else {
            // 输出当前颜色信息，帮助调试
            let hexColor = RGB转十六进制(r, g, b);
            console.log(`文本点赞按钮颜色: #${hexColor} rgb(${r},${g},${b})`);

            // 尝试检测周围几个像素
            for (let dx = -5; dx <= 5; dx += 5) {
                for (let dy = -5; dy <= 5; dy += 5) {
                    if (dx === 0 && dy === 0) continue; // 跳过中心点

                    let nx = x + dx;
                    let ny = y + dy;

                    // 确保坐标在屏幕范围内
                    if (nx < 0 || nx >= device.width || ny < 0 || ny >= device.height) {
                        continue;
                    }

                    // 获取该点的颜色
                    let ncolor = images.pixel(img, nx, ny);
                    let nr = colors.red(ncolor);
                    let ng = colors.green(ncolor);
                    let nb = colors.blue(ncolor);

                    // 点赞颜色匹配
                    if (颜色匹配(nr, ng, nb, 颜色配置.点赞颜色, 颜色配置.颜色容差)) {
                        已点赞 = true;
                        let hexColor = RGB转十六进制(nr, ng, nb);
                        console.log(`检测到点赞状态为已点赞，坐标: (${nx}, ${ny}), 颜色: #${hexColor} rgb(${nr},${ng},${nb})`);
                        break;
                    }
                }
                if (已点赞) break;
            }
        }
    } else {
        // 定义检测范围
        let 点赞X范围, 点赞Y范围;

        if (带分享按钮) {
            // 带分享按钮(视频)时，点赞按钮在点赞数上方
            点赞X范围 = [-10, 10]; // X偏移范围小
            点赞Y范围 = [-90, -60]; // Y向上偏移
            console.log("视频界面：检测点赞数上方Y偏移区域");
        } else {
            // 无分享按钮(图文)时，点赞按钮在点赞数左侧
            点赞X范围 = [-90, -60]; // X向左偏移
            点赞Y范围 = [-10, 10]; // Y偏移很小
            console.log("图文界面：检测点赞数左侧X偏移区域");
        }

        console.log(`点赞按钮检测范围: X偏移=${点赞X范围[0]}~${点赞X范围[1]}, Y偏移=${点赞Y范围[0]}~${点赞Y范围[1]}`);

        // 遍历可能的点赞按钮位置
        for (let x偏移 = 点赞X范围[0]; x偏移 <= 点赞X范围[1]; x偏移 += 5) {
            for (let y偏移 = 点赞Y范围[0]; y偏移 <= 点赞Y范围[1]; y偏移 += 5) {
                let x = 带分享按钮 ?
                    按钮信息.点赞数文本.坐标.中心X + x偏移 :
                    按钮信息.点赞数文本.坐标.左 + x偏移;

                let y = 按钮信息.点赞数文本.坐标.中心Y + y偏移;

                // 确保坐标在屏幕范围内
                if (x < 0 || x >= device.width || y < 0 || y >= device.height) {
                    continue;
                }

                // 获取该点的颜色
                let color = images.pixel(img, x, y);

                // 转换为RGB
                let r = colors.red(color);
                let g = colors.green(color);
                let b = colors.blue(color);

                // 点赞颜色匹配
                if (颜色匹配(r, g, b, 颜色配置.点赞颜色, 颜色配置.颜色容差)) {
                    已点赞 = true;
                    let hexColor = RGB转十六进制(r, g, b);
                    console.log(`检测到点赞状态为已点赞，坐标: (${x}, ${y}), 颜色: #${hexColor} rgb(${r},${g},${b})`);
                    break;
                }

                // 输出检测点的颜色信息，帮助调试
                if ((x偏移 % 10 === 0) && (y偏移 % 10 === 0)) {
                    let hexColor = RGB转十六进制(r, g, b);
                    console.log(`检测点(${x}, ${y})颜色: #${hexColor} rgb(${r},${g},${b})`);
                }
            }
            if (已点赞) break;
        }
    }

    // 释放图片资源
    if (img && img.recycle) {
        img.recycle();
    }

    console.log(`点赞状态: ${已点赞 ? "已点赞" : "未点赞"}`);
    return 已点赞;
}

/**
 * 判断收藏状态
 * @param {Object} 按钮信息 - 包含收藏数文本等信息的对象
 * @returns {boolean} 是否已收藏
 */
function 判断收藏状态(按钮信息) {
    console.log("开始判断收藏状态");

    if (!按钮信息 || !按钮信息.收藏数文本) {
        console.log("按钮信息不完整，无法判断收藏状态");
        return false;
    }

    // 检查是否是带分享按钮的界面
    let 带分享按钮 = !!按钮信息.分享按钮;

    // 截取屏幕
    let img = captureScreen();
    if (!img) {
        console.log("截图失败，无法判断收藏状态");
        return false;
    }

    // 收藏按钮状态检测
    let 已收藏 = false;

    // 检查是否是纯文本按钮
    let 是纯文本按钮 = 按钮信息.收藏数文本.文本 === "0" || 按钮信息.收藏数文本.文本 === "收藏";

    if (是纯文本按钮) {
        // 纯文本按钮时，直接检测按钮本身的颜色
        let x = 按钮信息.收藏数文本.坐标.中心X;
        let y = 按钮信息.收藏数文本.坐标.中心Y;

        // 获取该点的颜色
        let color = images.pixel(img, x, y);

        // 转换为RGB
        let r = colors.red(color);
        let g = colors.green(color);
        let b = colors.blue(color);

        // 收藏颜色匹配
        if (颜色匹配(r, g, b, 颜色配置.收藏颜色, 颜色配置.颜色容差)) {
            已收藏 = true;
            let hexColor = RGB转十六进制(r, g, b);
            console.log(`检测到收藏状态为已收藏，坐标: (${x}, ${y}), 颜色: #${hexColor} rgb(${r},${g},${b})`);
        } else {
            // 输出当前颜色信息，帮助调试
            let hexColor = RGB转十六进制(r, g, b);
            console.log(`文本收藏按钮颜色: #${hexColor} rgb(${r},${g},${b})`);

            // 尝试检测周围几个像素
            for (let dx = -5; dx <= 5; dx += 5) {
                for (let dy = -5; dy <= 5; dy += 5) {
                    if (dx === 0 && dy === 0) continue; // 跳过中心点

                    let nx = x + dx;
                    let ny = y + dy;

                    // 确保坐标在屏幕范围内
                    if (nx < 0 || nx >= device.width || ny < 0 || ny >= device.height) {
                        continue;
                    }

                    // 获取该点的颜色
                    let ncolor = images.pixel(img, nx, ny);
                    let nr = colors.red(ncolor);
                    let ng = colors.green(ncolor);
                    let nb = colors.blue(ncolor);

                    // 收藏颜色匹配
                    if (颜色匹配(nr, ng, nb, 颜色配置.收藏颜色, 颜色配置.颜色容差)) {
                        已收藏 = true;
                        let hexColor = RGB转十六进制(nr, ng, nb);
                        console.log(`检测到收藏状态为已收藏，坐标: (${nx}, ${ny}), 颜色: #${hexColor} rgb(${nr},${ng},${nb})`);
                        break;
                    }
                }
                if (已收藏) break;
            }
        }
    } else {
        // 定义检测范围
        let 收藏X范围, 收藏Y范围;

        if (带分享按钮) {
            // 带分享按钮(视频)时，收藏按钮在收藏数上方
            收藏X范围 = [-10, 10]; // X偏移范围小
            收藏Y范围 = [-90, -60]; // Y向上偏移
            console.log("视频界面：检测收藏数上方Y偏移区域");
        } else {
            // 无分享按钮(图文)时，收藏按钮在收藏数左侧
            收藏X范围 = [-90, -60]; // X向左偏移
            收藏Y范围 = [-10, 10]; // Y偏移很小
            console.log("图文界面：检测收藏数左侧X偏移区域");
        }

        console.log(`收藏按钮检测范围: X偏移=${收藏X范围[0]}~${收藏X范围[1]}, Y偏移=${收藏Y范围[0]}~${收藏Y范围[1]}`);

        // 遍历可能的收藏按钮位置
        for (let x偏移 = 收藏X范围[0]; x偏移 <= 收藏X范围[1]; x偏移 += 5) {
            for (let y偏移 = 收藏Y范围[0]; y偏移 <= 收藏Y范围[1]; y偏移 += 5) {
                let x = 带分享按钮 ?
                    按钮信息.收藏数文本.坐标.中心X + x偏移 :
                    按钮信息.收藏数文本.坐标.左 + x偏移;

                let y = 按钮信息.收藏数文本.坐标.中心Y + y偏移;

                // 确保坐标在屏幕范围内
                if (x < 0 || x >= device.width || y < 0 || y >= device.height) {
                    continue;
                }

                // 获取该点的颜色
                let color = images.pixel(img, x, y);

                // 转换为RGB
                let r = colors.red(color);
                let g = colors.green(color);
                let b = colors.blue(color);

                // 收藏颜色匹配
                if (颜色匹配(r, g, b, 颜色配置.收藏颜色, 颜色配置.颜色容差)) {
                    已收藏 = true;
                    let hexColor = RGB转十六进制(r, g, b);
                    console.log(`检测到收藏状态为已收藏，坐标: (${x}, ${y}), 颜色: #${hexColor} rgb(${r},${g},${b})`);
                    break;
                }

                // 输出检测点的颜色信息，帮助调试
                if ((x偏移 % 10 === 0) && (y偏移 % 10 === 0)) {
                    let hexColor = RGB转十六进制(r, g, b);
                    console.log(`检测点(${x}, ${y})颜色: #${hexColor} rgb(${r},${g},${b})`);
                }
            }
            if (已收藏) break;
        }
    }

    // 释放图片资源
    if (img && img.recycle) {
        img.recycle();
    }

    console.log(`收藏状态: ${已收藏 ? "已收藏" : "未收藏"}`);
    return 已收藏;
}

/**
 * 点击首页第一篇文章
 * @returns {boolean} 是否成功点击
 */
function 点击首篇文章() {
    console.log("尝试点击首页第一篇文章");

    // 获取界面XML
    let xmlContent = 获取界面XML();
    if (!xmlContent) {
        console.log("获取界面XML失败，无法点击首篇文章");
        return false;
    }

    // 分析界面，确定是否在首页
    let 元素列表 = 提取文本元素(xmlContent);

    // 检查是否有首页、购物等底部标签
    let 首页标签 = 元素列表.find(e => e.文本 === "首页");
    let 购物标签 = 元素列表.find(e => e.文本 === "购物");

    if (!首页标签 && !购物标签) {
        console.log("未检测到首页标签，可能不在主界面");
        return false;
    }

    // 计算点击位置（首页第一篇文章通常在屏幕中上部）
    let 屏幕宽度 = device.width;
    let 屏幕高度 = device.height;

    // 点击位置：屏幕中部偏上的位置
    let x = 屏幕宽度 * 0.5;
    let y = 屏幕高度 * 0.3;

    console.log(`点击首页文章位置: (${x}, ${y})`);
    点击(x, y);

    // 等待页面加载
    等待(2000);

    // 检查是否成功进入文章页面
    xmlContent = 获取界面XML();
    if (!xmlContent) {
        console.log("获取界面XML失败，无法确认是否进入文章页面");
        return false;
    }

    元素列表 = 提取文本元素(xmlContent);

    // 文章页面特征：底部有点赞、收藏、评论等互动元素
    let 底部元素 = 元素列表.filter(e => e.坐标.中心Y > 屏幕高度 * 0.8);
    let 互动数字 = 底部元素.filter(e => /^[\d.]+万?$/.test(e.文本));

    if (互动数字.length >= 2) {
        console.log("检测到互动元素，成功进入文章页面");
        return true;
    }

    console.log("未检测到文章页面特征，可能点击失败");
    return false;
}

/**
 * 比较两个页面信息是否为同一篇文章
 * @param {Object} 信息1 - 第一个页面信息
 * @param {Object} 信息2 - 第二个页面信息
 * @returns {boolean} 是否为同一篇文章
 */
function 比较页面信息(信息1, 信息2) {
    console.log("比较页面信息是否为同一篇文章");

    // 如果作者和标题都有效且相同，则认为是同一篇文章
    if (信息1.作者 !== "未知" && 信息2.作者 !== "未知" &&
        信息1.标题 !== "未知" && 信息2.标题 !== "未知") {

        // 作者完全相同
        if (信息1.作者 === 信息2.作者) {
            console.log("作者相同: " + 信息1.作者);

            // 标题相似度检查（允许部分匹配，因为标题可能被截断）
            if (信息1.标题 === 信息2.标题 ||
                信息1.标题.includes(信息2.标题) ||
                信息2.标题.includes(信息1.标题)) {
                console.log("标题相似: [" + 信息1.标题 + "] 和 [" + 信息2.标题 + "]");
                return true;
            }
        }
    }

    // 如果作者或标题无效，则尝试比较内容
    if ((信息1.作者 === "未知" || 信息2.作者 === "未知" ||
        信息1.标题 === "未知" || 信息2.标题 === "未知") &&
        信息1.内容 !== "未知" && 信息2.内容 !== "未知") {

        // 内容相似度检查
        if (信息1.内容 === 信息2.内容 ||
            信息1.内容.includes(信息2.内容) ||
            信息2.内容.includes(信息1.内容)) {
            console.log("内容相似，可能是同一篇文章");
            return true;
        }
    }

    console.log("页面信息不匹配，不是同一篇文章");
    return false;
}

