// 移除UI声明，改为普通脚本
// "ui";

// 全局变量，操作模式: 1=无障碍模式, 2=Root Shell模式
let 操作模式 = 2; // 默认使用Root Shell模式

// 全局变量，保存root会话状态
let root会话 = false;

// 全局变量，按钮颜色配置
let 颜色配置 = {
    点赞颜色: "FF2442",  // 红色系
    收藏颜色: "FCBD54",  // 黄色系
    颜色容差: 40         // 颜色匹配的容差值
};

/**
 * 初始化Root权限，获取一个root会话
 * @returns {boolean} 是否成功获取root权限
 */
function 初始化Root权限() {
    // 如果已经获取过root权限，直接返回true，不再重复申请
    if (root会话) {
        console.log("已有root会话，无需重新申请权限");
        return true;
    }
    
    console.log("首次尝试获取root权限...");
    
    try {
        // 使用普通shell命令测试root权限
        let result = shell("su -c 'echo root_test'", true);
        
        if (result.code === 0 && result.result.includes("root_test")) {
            console.log("成功获取root权限，标记为已授权");
            root会话 = true;
            return true;
        } else {
            console.log("获取root权限失败: " + result.error);
            root会话 = false;
            return false;
        }
    } catch (e) {
        console.error("初始化Root权限出错: " + e);
        root会话 = false;
        return false;
    }
}

/**
 * 检查操作模式函数
 */
function 检查操作模式() {
    if (操作模式 === 1) {
        console.log("当前使用无障碍模式操作，请确保已开启无障碍服务");
        // 检查无障碍服务是否已启用
        if (!auto.service) {
            console.log("无障碍服务未启用，尝试启动...");
            auto.waitFor();
        }
    } else if (操作模式 === 2) {
        console.log("当前使用Root Shell模式操作，请确保已获取Root权限");
        // 检查Root权限，如果没有root会话，则尝试初始化一次
        if (!root会话) {
            if (!初始化Root权限()) {
                console.log("警告：未获取到 root 权限，脚本可能无法正常工作");
                return false;
            }
        }
        console.log("Root权限检查通过");
        return true;
    } else {
        console.log("错误：未知的操作模式，请设置为1(无障碍)或2(Root Shell)");
        return false;
    }
    return true;
}

/**
 * 使用 uiautomator dump 获取当前界面的 XML 结构，增加内部重试机制
 * @returns {string|null} XML 内容或 null（如果失败）
 */
function 获取界面XML() {
    console.log("开始获取界面 XML...");
    
    // 最大内部重试次数
    const 最大重试次数 = 3;
    let 当前重试次数 = 0;
    
    while (当前重试次数 < 最大重试次数) {
        // 使用执行Root命令函数，确保root权限检查
        let result = 执行Root命令('uiautomator dump /sdcard/window_dump.xml');
        
        if (result.code === 0) {
            console.log("界面 XML 导出成功");
            
            // 读取导出的 XML 文件
            try {
                let xmlContent = files.read("/sdcard/window_dump.xml");
                console.log("成功读取 XML 文件，大小: " + xmlContent.length + " 字节");
                return xmlContent;
            } catch (e) {
                console.error("读取 XML 文件失败: " + e.message);
                当前重试次数++;
                
                if (当前重试次数 < 最大重试次数) {
                    console.log(`读取XML文件失败，进行第${当前重试次数 + 1}次重试...`);
                    // 使用非阻塞式等待
                    sleep(1000);
                }
            }
        } else {
            console.error("界面 XML 导出失败: " + result.error);
            当前重试次数++;
            
            if (当前重试次数 < 最大重试次数) {
                console.log(`导出XML失败，进行第${当前重试次数 + 1}次重试...`);
                // 使用非阻塞式等待
                sleep(1500);
            }
        }
    }
    
    console.error(`获取界面XML失败，已重试${最大重试次数}次`);
    return null;
}

/**
 * 执行需要Root权限的shell命令
 * @param {string} 命令 - 要执行的shell命令
 * @returns {Object} 包含执行结果的对象
 */
function 执行Root命令(命令) {
    console.log("执行Root命令: " + 命令);
    
    // 检查是否已经获取root权限
    if (!root会话) {
        if (!初始化Root权限()) {
            console.log("未获取到root权限，无法执行命令");
            return {
                code: -1,
                result: "",
                error: "未获取root权限"
            };
        }
    }
    
    // 使用su -c执行命令
    try {
        let result = shell("su -c '" + 命令 + "'", true);
        
        if (result.code === 0) {
            console.log("命令执行成功");
            return {
                code: 0,
                result: result.result,
                error: ""
            };
        } else {
            console.log("命令执行失败: " + result.error);
            return {
                code: result.code,
                result: result.result,
                error: result.error
            };
        }
    } catch (e) {
        console.error("执行命令出错: " + e);
        return {
            code: -1,
            result: "",
            error: e.toString()
        };
    }
}

/**
 * 从 XML 中提取所有文本元素及其坐标
 * @param {string} xmlContent - XML 内容
 * @returns {Array} 元素数组，每个元素包含文本和坐标
 */
function 提取文本元素(xmlContent) {
    console.log("开始解析 XML 中的文本元素...");
    
    let 元素列表 = [];
    let 文本正则 = /text="([^"]*)"[^>]*bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"/g;
    let 匹配结果;
    let 空文本计数 = 0;
    
    while ((匹配结果 = 文本正则.exec(xmlContent)) !== null) {
        let 文本 = 匹配结果[1];
        
        // 跳过空文本内容
        if (!文本 || 文本.trim() === "") {
            空文本计数++;
            continue;
        }
        
        let 左 = parseInt(匹配结果[2]);
        let 上 = parseInt(匹配结果[3]);
        let 右 = parseInt(匹配结果[4]);
        let 下 = parseInt(匹配结果[5]);
        
        // 计算中心点坐标
        let 中心X = Math.floor((左 + 右) / 2);
        let 中心Y = Math.floor((上 + 下) / 2);
        
        元素列表.push({
            文本: 文本,
            坐标: {
                左: 左,
                上: 上,
                右: 右,
                下: 下,
                中心X: 中心X,
                中心Y: 中心Y
            }
        });
    }
    
    console.log("共找到 " + 元素列表.length + " 个有效文本元素 (已过滤 " + 空文本计数 + " 个空文本元素)");
    return 元素列表;
}

/**
 * 标记核心元素
 * @param {Array} 元素列表 - 元素数组
 * @returns {Object|null} 按钮信息对象或null
 */
function 标记核心元素(元素列表) {
    let 屏幕高度 = device.height;
    let 屏幕宽度 = device.width;

    // 1. 找顶部用户名（图文）
    let 顶部用户名 = null;
    for (let 元素 of 元素列表) {
        if (
            元素.坐标.中心Y < 屏幕高度 * 0.3 &&
            元素.文本.length >= 2 && 元素.文本.length <= 12 &&
            !/^\d+$/.test(元素.文本) &&
            !["关注", "说点什么...", "评论", "发弹幕", "分享"].some(k => 元素.文本.includes(k))
        ) {
            顶部用户名 = 元素;
            break;
        }
    }

    // 只在视频时找底部昵称和互动数
    let 底部用户名 = null;
    let 点赞 = null, 收藏 = null, 评论 = null;

    // 先查找分享按钮，判断是否是视频界面
    let 分享按钮 = 元素列表.find(e => e.文本 === "分享");
    let 是视频界面 = !!分享按钮;
    
    // 查找文本形式的点赞、收藏按钮
    let 点赞按钮 = 元素列表.find(e => e.文本 === "点赞" || e.文本 === "赞");
    let 收藏按钮 = 元素列表.find(e => e.文本 === "收藏");
    
    if (!顶部用户名) {
        // 1. 找"关注"或"已关注"按钮
        let 关注按钮 = 元素列表.find(e =>
            e.坐标.中心Y > 屏幕高度 * 0.5 &&
            (e.文本 === "关注" || e.文本 === "已关注")
        );

        // 2. 昵称候选
        let 昵称候选 = 元素列表.filter(e =>
            e.坐标.中心Y > 屏幕高度 * 0.5 &&
            e.文本.length >= 2 && e.文本.length <= 12 &&
            !/^[\d.]+万?$/.test(e.文本) &&
            !["关注", "说点什么...", "评论", "发弹幕", "分享", "相关搜索", "课程咨询"].some(k => e.文本.includes(k)) &&
            !e.文本.includes(":")
        );

        if (昵称候选.length > 0) {
            if (关注按钮) {
                // 只选X在关注按钮左侧的，且Y最接近
                let 左侧候选 = 昵称候选
                    .filter(e => e.坐标.中心X < 关注按钮.坐标.中心X)
                    .sort((a, b) => {
                        // Y越接近关注按钮越优先
                        let dyA = Math.abs(a.坐标.中心Y - 关注按钮.坐标.中心Y);
                        let dyB = Math.abs(b.坐标.中心Y - 关注按钮.坐标.中心Y);
                        if (dyA !== dyB) return dyA - dyB;
                        // X越靠近关注按钮越优先
                        return 关注按钮.坐标.中心X - a.坐标.中心X - (关注按钮.坐标.中心X - b.坐标.中心X);
                    });
                if (左侧候选.length > 0) {
                    底部用户名 = 左侧候选[0];
                } else {
                    // 没有左侧的，退回原有Y最大X最小
                    昵称候选.sort((a, b) => {
                        if (b.坐标.中心Y !== a.坐标.中心Y) {
                            return b.坐标.中心Y - a.坐标.中心Y;
                        }
                        return a.坐标.中心X - b.坐标.中心X;
                    });
                    底部用户名 = 昵称候选[0];
                }
            } else {
                // 没有关注按钮，退回原有Y最大X最小
                昵称候选.sort((a, b) => {
                    if (b.坐标.中心Y !== a.坐标.中心Y) {
                        return b.坐标.中心Y - a.坐标.中心Y;
                    }
                    return a.坐标.中心X - b.坐标.中心X;
                });
                底部用户名 = 昵称候选[0];
            }
        }

        // 3. 点赞/收藏/评论数：底部剩下的数字（含"万"）
        let 底部元素 = 元素列表.filter(e => e.坐标.中心Y > 屏幕高度 * 0.7);
        let 互动数字 = 底部元素.filter(e =>
            /^[\d.]+万?$/.test(e.文本)
        );
        // 排除昵称
        if (底部用户名) {
            互动数字 = 互动数字.filter(e => e.文本 !== 底部用户名.文本);
        }
        // 按X坐标排序
        互动数字.sort((a, b) => a.坐标.中心X - b.坐标.中心X);
        点赞 = 互动数字[0] || 点赞按钮; // 如果没有数字，使用文本按钮
        收藏 = 互动数字[1] || 收藏按钮; // 如果没有数字，使用文本按钮
        评论 = 互动数字[2] || null;
    } else {
        // 图文页底部数字（精准：说点什么...右侧依次为点赞/收藏/评论）
        let 说点什么 = 元素列表.find(e =>
            e.文本 === "说点什么..." && e.坐标.中心Y > 屏幕高度 * 0.8
        );
        let 分界X = 说点什么 ? 说点什么.坐标.中心X : 0;
        let 底部数字 = 元素列表.filter(e =>
            e.坐标.中心Y > 屏幕高度 * 0.8 &&
            (/^[\d.]+万$/.test(e.文本) || /^\d+$/.test(e.文本) || ["点赞", "收藏", "评论", "赞"].includes(e.文本)) &&
            e.坐标.中心X > 分界X
        );
        底部数字.sort((a, b) => a.坐标.中心X - b.坐标.中心X);
        点赞 = 底部数字[0] || 点赞按钮;
        收藏 = 底部数字[1] || 收藏按钮;
        评论 = 底部数字[2] || null;
        // 如果为文本，视为0
        if (点赞 && (点赞.文本 === "点赞" || 点赞.文本 === "赞")) 点赞.文本 = "0";
        if (收藏 && 收藏.文本 === "收藏") 收藏.文本 = "0";
        if (评论 && 评论.文本 === "评论") 评论.文本 = "0";
    }

    // 打印标记结果
    if (顶部用户名) {
        打印元素详细信息(顶部用户名, "图文用户名");
    }
    if (底部用户名) {
        打印元素详细信息(底部用户名, "用户昵称");
    }
    if (点赞) {
        打印元素详细信息(点赞, "点赞数");
    }
    if (收藏) {
        打印元素详细信息(收藏, "收藏数");
    }
    if (评论) {
        打印元素详细信息(评论, "评论数");
    }
    
    // 判断内容类型
    let 是图文 = !!顶部用户名;
    console.log(`内容类型: ${是图文 ? "图文" : "视频"}`);
    
    // 无论是否有分享按钮，都进行按钮状态检测
    // 只要有点赞数和收藏数就可以进行检测
    if (点赞 && 收藏) {
        if (分享按钮) {
            console.log("检测到分享按钮，使用上方Y偏移检测方式（视频界面）");
        } else {
            console.log("未检测到分享按钮，使用左侧X偏移检测方式（图文界面）");
        }
        
        let 按钮信息 = {
            点赞数文本: 点赞,
            收藏数文本: 收藏,
            评论数文本: 评论,
            分享按钮: 分享按钮
        };
        
        return 按钮信息;
    } else {
        console.log("未找到足够的互动元素，无法进行按钮状态检测");
        return null;
    }
}

/**
 * 打印元素详细信息
 * @param {Object} 元素 - 元素对象 
 * @param {string} 标签 - 元素标签
 */
function 打印元素详细信息(元素, 标签) {
    if (!元素) return;
    let 属性文本 = '';
    if (元素.原始属性) {
        for (let k in 元素.原始属性) {
            属性文本 += `${k}: ${元素.原始属性[k]}, `;
        }
        属性文本 = 属性文本.replace(/, $/, '');
    }
    console.log(`${标签}: [${元素.文本}], 坐标: (${元素.坐标.中心X}, ${元素.坐标.中心Y})${属性文本 ? ' | ' + 属性文本 : ''}`);
}

/**
 * 判断颜色是否匹配
 * @param {number} r - 当前像素的红色分量
 * @param {number} g - 当前像素的绿色分量
 * @param {number} b - 当前像素的蓝色分量
 * @param {string} colorCode - 目标颜色代码，格式为RRGGBB
 * @param {number} tolerance - 容差值，默认为30
 * @returns {boolean} 是否匹配
 */
function 颜色匹配(r, g, b, colorCode, tolerance = 30) {
    // 解析颜色代码
    let targetR = parseInt(colorCode.substring(0, 2), 16);
    let targetG = parseInt(colorCode.substring(2, 4), 16);
    let targetB = parseInt(colorCode.substring(4, 6), 16);
    
    // 计算颜色差异
    let diffR = Math.abs(r - targetR);
    let diffG = Math.abs(g - targetG);
    let diffB = Math.abs(b - targetB);
    
    // 判断是否在容差范围内
    return diffR <= tolerance && diffG <= tolerance && diffB <= tolerance;
}

/**
 * 颜色调试函数，将RGB转为十六进制颜色代码
 * @param {number} r - 红色分量
 * @param {number} g - 绿色分量
 * @param {number} b - 蓝色分量
 * @returns {string} 十六进制颜色代码
 */
function RGB转十六进制(r, g, b) {
    function 转两位十六进制(n) {
        let hex = n.toString(16).toUpperCase();
        return hex.length === 1 ? '0' + hex : hex;
    }
    return 转两位十六进制(r) + 转两位十六进制(g) + 转两位十六进制(b);
}

/**
 * 判断按钮状态
 * @param {Object} 按钮信息 - 包含点赞数文本、收藏数文本等信息的对象
 * @returns {Object} 包含已点赞和已收藏状态的对象
 */
function 判断按钮状态(按钮信息) {
    console.log("开始判断按钮状态");
    
    if (!按钮信息 || !按钮信息.点赞数文本) {
        console.log("按钮信息不完整，无法判断状态");
        return {
            已点赞: false,
            已收藏: false
        };
    }
    
    // 检查是否是带分享按钮的界面
    let 带分享按钮 = !!按钮信息.分享按钮;
    console.log(`界面类型: ${带分享按钮 ? "带分享按钮(视频)" : "无分享按钮(图文)"}`);
    
    // 截取屏幕
    let img = captureScreen();
    if (!img) {
        console.log("截图失败，无法判断按钮状态");
        return {
            已点赞: false,
            已收藏: false
        };
    }
    
    // 点赞按钮状态检测
    let 已点赞 = false;
    if (按钮信息.点赞数文本) {
        // 检查是否是纯文本按钮
        let 是纯文本按钮 = 按钮信息.点赞数文本.文本 === "0" || 按钮信息.点赞数文本.文本 === "点赞" || 按钮信息.点赞数文本.文本 === "赞";
        
        if (是纯文本按钮) {
            // 纯文本按钮时，直接检测按钮本身的颜色
            let x = 按钮信息.点赞数文本.坐标.中心X;
            let y = 按钮信息.点赞数文本.坐标.中心Y;
            
            // 获取该点的颜色
            let color = images.pixel(img, x, y);
            
            // 转换为RGB
            let r = colors.red(color);
            let g = colors.green(color);
            let b = colors.blue(color);
            
            // 点赞颜色匹配
            if (颜色匹配(r, g, b, 颜色配置.点赞颜色, 颜色配置.颜色容差)) {
                已点赞 = true;
                let hexColor = RGB转十六进制(r, g, b);
                console.log(`检测到点赞状态为已点赞，坐标: (${x}, ${y}), 颜色: #${hexColor} rgb(${r},${g},${b})`);
            } else {
                // 输出当前颜色信息，帮助调试
                let hexColor = RGB转十六进制(r, g, b);
                console.log(`文本点赞按钮颜色: #${hexColor} rgb(${r},${g},${b})`);
                
                // 尝试检测周围几个像素
                for (let dx = -5; dx <= 5; dx += 5) {
                    for (let dy = -5; dy <= 5; dy += 5) {
                        if (dx === 0 && dy === 0) continue; // 跳过中心点
                        
                        let nx = x + dx;
                        let ny = y + dy;
                        
                        // 确保坐标在屏幕范围内
                        if (nx < 0 || nx >= device.width || ny < 0 || ny >= device.height) {
                            continue;
                        }
                        
                        // 获取该点的颜色
                        let ncolor = images.pixel(img, nx, ny);
                        let nr = colors.red(ncolor);
                        let ng = colors.green(ncolor);
                        let nb = colors.blue(ncolor);
                        
                        // 点赞颜色匹配
                        if (颜色匹配(nr, ng, nb, 颜色配置.点赞颜色, 颜色配置.颜色容差)) {
                            已点赞 = true;
                            let hexColor = RGB转十六进制(nr, ng, nb);
                            console.log(`检测到点赞状态为已点赞，坐标: (${nx}, ${ny}), 颜色: #${hexColor} rgb(${nr},${ng},${nb})`);
                            break;
                        }
                    }
                    if (已点赞) break;
                }
            }
        } else {
            // 定义检测范围
            let 点赞X范围, 点赞Y范围;
            
            if (带分享按钮) {
                // 带分享按钮(视频)时，点赞按钮在点赞数上方
                点赞X范围 = [-10, 10]; // X偏移范围小
                点赞Y范围 = [-90, -60]; // Y向上偏移
                console.log("视频界面：检测点赞数上方Y偏移区域");
            } else {
                // 无分享按钮(图文)时，点赞按钮在点赞数左侧
                点赞X范围 = [-90, -60]; // X向左偏移
                点赞Y范围 = [-10, 10]; // Y偏移很小
                console.log("图文界面：检测点赞数左侧X偏移区域");
            }
            
            console.log(`点赞按钮检测范围: X偏移=${点赞X范围[0]}~${点赞X范围[1]}, Y偏移=${点赞Y范围[0]}~${点赞Y范围[1]}`);
            
            // 遍历可能的点赞按钮位置
            for (let x偏移 = 点赞X范围[0]; x偏移 <= 点赞X范围[1]; x偏移 += 5) {
                for (let y偏移 = 点赞Y范围[0]; y偏移 <= 点赞Y范围[1]; y偏移 += 5) {
                    let x = 带分享按钮 ? 
                        按钮信息.点赞数文本.坐标.中心X + x偏移 : 
                        按钮信息.点赞数文本.坐标.左 + x偏移;
                        
                    let y = 按钮信息.点赞数文本.坐标.中心Y + y偏移;
                    
                    // 确保坐标在屏幕范围内
                    if (x < 0 || x >= device.width || y < 0 || y >= device.height) {
                        continue;
                    }
                    
                    // 获取该点的颜色
                    let color = images.pixel(img, x, y);
                    
                    // 转换为RGB
                    let r = colors.red(color);
                    let g = colors.green(color);
                    let b = colors.blue(color);
                    
                    // 点赞颜色匹配
                    if (颜色匹配(r, g, b, 颜色配置.点赞颜色, 颜色配置.颜色容差)) {
                        已点赞 = true;
                        let hexColor = RGB转十六进制(r, g, b);
                        console.log(`检测到点赞状态为已点赞，坐标: (${x}, ${y}), 颜色: #${hexColor} rgb(${r},${g},${b})`);
                        break;
                    }
                    
                    // 输出检测点的颜色信息，帮助调试
                    if ((x偏移 % 10 === 0) && (y偏移 % 10 === 0)) {
                        let hexColor = RGB转十六进制(r, g, b);
                        console.log(`检测点(${x}, ${y})颜色: #${hexColor} rgb(${r},${g},${b})`);
                    }
                }
                if (已点赞) break;
            }
        }
    }
    
    // 收藏按钮状态检测
    let 已收藏 = false;
    if (按钮信息.收藏数文本) {
        // 检查是否是纯文本按钮
        let 是纯文本按钮 = 按钮信息.收藏数文本.文本 === "0" || 按钮信息.收藏数文本.文本 === "收藏";
        
        if (是纯文本按钮) {
            // 纯文本按钮时，直接检测按钮本身的颜色
            let x = 按钮信息.收藏数文本.坐标.中心X;
            let y = 按钮信息.收藏数文本.坐标.中心Y;
            
            // 获取该点的颜色
            let color = images.pixel(img, x, y);
            
            // 转换为RGB
            let r = colors.red(color);
            let g = colors.green(color);
            let b = colors.blue(color);
            
            // 收藏颜色匹配
            if (颜色匹配(r, g, b, 颜色配置.收藏颜色, 颜色配置.颜色容差)) {
                已收藏 = true;
                let hexColor = RGB转十六进制(r, g, b);
                console.log(`检测到收藏状态为已收藏，坐标: (${x}, ${y}), 颜色: #${hexColor} rgb(${r},${g},${b})`);
            } else {
                // 输出当前颜色信息，帮助调试
                let hexColor = RGB转十六进制(r, g, b);
                console.log(`文本收藏按钮颜色: #${hexColor} rgb(${r},${g},${b})`);
                
                // 尝试检测周围几个像素
                for (let dx = -5; dx <= 5; dx += 5) {
                    for (let dy = -5; dy <= 5; dy += 5) {
                        if (dx === 0 && dy === 0) continue; // 跳过中心点
                        
                        let nx = x + dx;
                        let ny = y + dy;
                        
                        // 确保坐标在屏幕范围内
                        if (nx < 0 || nx >= device.width || ny < 0 || ny >= device.height) {
                            continue;
                        }
                        
                        // 获取该点的颜色
                        let ncolor = images.pixel(img, nx, ny);
                        let nr = colors.red(ncolor);
                        let ng = colors.green(ncolor);
                        let nb = colors.blue(ncolor);
                        
                        // 收藏颜色匹配
                        if (颜色匹配(nr, ng, nb, 颜色配置.收藏颜色, 颜色配置.颜色容差)) {
                            已收藏 = true;
                            let hexColor = RGB转十六进制(nr, ng, nb);
                            console.log(`检测到收藏状态为已收藏，坐标: (${nx}, ${ny}), 颜色: #${hexColor} rgb(${nr},${ng},${nb})`);
                            break;
                        }
                    }
                    if (已收藏) break;
                }
            }
        } else {
            // 定义检测范围
            let 收藏X范围, 收藏Y范围;
            
            if (带分享按钮) {
                // 带分享按钮(视频)时，收藏按钮在收藏数上方
                收藏X范围 = [-10, 10]; // X偏移范围小
                收藏Y范围 = [-90, -60]; // Y向上偏移
                console.log("视频界面：检测收藏数上方Y偏移区域");
            } else {
                // 无分享按钮(图文)时，收藏按钮在收藏数左侧
                收藏X范围 = [-90, -60]; // X向左偏移
                收藏Y范围 = [-10, 10]; // Y偏移很小
                console.log("图文界面：检测收藏数左侧X偏移区域");
            }
            
            console.log(`收藏按钮检测范围: X偏移=${收藏X范围[0]}~${收藏X范围[1]}, Y偏移=${收藏Y范围[0]}~${收藏Y范围[1]}`);
            
            // 遍历可能的收藏按钮位置
            for (let x偏移 = 收藏X范围[0]; x偏移 <= 收藏X范围[1]; x偏移 += 5) {
                for (let y偏移 = 收藏Y范围[0]; y偏移 <= 收藏Y范围[1]; y偏移 += 5) {
                    let x = 带分享按钮 ? 
                        按钮信息.收藏数文本.坐标.中心X + x偏移 : 
                        按钮信息.收藏数文本.坐标.左 + x偏移;
                        
                    let y = 按钮信息.收藏数文本.坐标.中心Y + y偏移;
                    
                    // 确保坐标在屏幕范围内
                    if (x < 0 || x >= device.width || y < 0 || y >= device.height) {
                        continue;
                    }
                    
                    // 获取该点的颜色
                    let color = images.pixel(img, x, y);
                    
                    // 转换为RGB
                    let r = colors.red(color);
                    let g = colors.green(color);
                    let b = colors.blue(color);
                    
                    // 收藏颜色匹配
                    if (颜色匹配(r, g, b, 颜色配置.收藏颜色, 颜色配置.颜色容差)) {
                        已收藏 = true;
                        let hexColor = RGB转十六进制(r, g, b);
                        console.log(`检测到收藏状态为已收藏，坐标: (${x}, ${y}), 颜色: #${hexColor} rgb(${r},${g},${b})`);
                        break;
                    }
                    
                    // 输出检测点的颜色信息，帮助调试
                    if ((x偏移 % 10 === 0) && (y偏移 % 10 === 0)) {
                        let hexColor = RGB转十六进制(r, g, b);
                        console.log(`检测点(${x}, ${y})颜色: #${hexColor} rgb(${r},${g},${b})`);
                    }
                }
                if (已收藏) break;
            }
        }
    }
    
    // 释放图片资源
    if (img && img.recycle) {
        img.recycle();
    }
    
    console.log(`按钮状态: 已点赞=${已点赞}, 已收藏=${已收藏}`);
    return {
        已点赞: 已点赞,
        已收藏: 已收藏
    };
}

/**
 * 执行互动操作
 * @param {Object} 按钮信息 - 包含点赞数文本、收藏数文本等信息的对象
 * @param {boolean} 点赞操作 - 是否执行点赞操作
 * @param {boolean} 收藏操作 - 是否执行收藏操作
 * @returns {Object} 操作结果
 */
function 执行互动操作(按钮信息, 点赞操作, 收藏操作) {
    console.log(`开始执行互动操作: 点赞=${点赞操作}, 收藏=${收藏操作}`);
    
    if (!按钮信息) {
        console.log("按钮信息不完整，无法执行互动操作");
        return {
            点赞成功: false,
            收藏成功: false
        };
    }
    
    // 检查是否是带分享按钮的界面
    let 带分享按钮 = !!按钮信息.分享按钮;
    console.log(`界面类型: ${带分享按钮 ? "带分享按钮(视频)" : "无分享按钮(图文)"}`);
    
    let 点赞成功 = false;
    let 收藏成功 = false;
    
    // 点赞操作
    if (点赞操作 && 按钮信息.点赞数文本) {
        // 计算点击坐标
        let 点赞X, 点赞Y;
        let 是纯文本按钮 = 按钮信息.点赞数文本.文本 === "0" || 按钮信息.点赞数文本.文本 === "点赞" || 按钮信息.点赞数文本.文本 === "赞";
        
        if (是纯文本按钮) {
            // 直接点击文本按钮的中心位置
            点赞X = 按钮信息.点赞数文本.坐标.中心X;
            点赞Y = 按钮信息.点赞数文本.坐标.中心Y;
            console.log(`点击文本点赞按钮中心位置 (${点赞X}, ${点赞Y})`);
        } else if (带分享按钮) {
            // 带分享按钮(视频)时，点赞按钮在点赞数上方
            点赞X = 按钮信息.点赞数文本.坐标.中心X;
            点赞Y = 按钮信息.点赞数文本.坐标.中心Y - 75; // Y向上偏移75像素
            console.log(`视频界面：点击点赞数上方Y偏移位置 (${点赞X}, ${点赞Y})`);
        } else {
            // 无分享按钮(图文)时，点赞按钮在点赞数左侧
            点赞X = 按钮信息.点赞数文本.坐标.左 - 75; // X向左偏移75像素
            点赞Y = 按钮信息.点赞数文本.坐标.中心Y;
            console.log(`图文界面：点击点赞数左侧X偏移位置 (${点赞X}, ${点赞Y})`);
        }
        
        // 执行点击
        try {
            if (操作模式 === 1) {
                // 无障碍模式点击
                click(点赞X, 点赞Y);
            } else {
                // Root模式点击
                let cmd = `input tap ${点赞X} ${点赞Y}`;
                let result = 执行Root命令(cmd);
                if (result.code === 0) {
                    点赞成功 = true;
                }
            }
            console.log(`点赞操作执行完成，坐标: (${点赞X}, ${点赞Y})`);
            点赞成功 = true;
            
            // 等待一小段时间，让UI反应
            sleep(500);
        } catch (e) {
            console.error(`点赞操作失败: ${e}`);
        }
    }
    
    // 收藏操作
    if (收藏操作 && 按钮信息.收藏数文本) {
        // 计算点击坐标
        let 收藏X, 收藏Y;
        let 是纯文本按钮 = 按钮信息.收藏数文本.文本 === "0" || 按钮信息.收藏数文本.文本 === "收藏";
        
        if (是纯文本按钮) {
            // 直接点击文本按钮的中心位置
            收藏X = 按钮信息.收藏数文本.坐标.中心X;
            收藏Y = 按钮信息.收藏数文本.坐标.中心Y;
            console.log(`点击文本收藏按钮中心位置 (${收藏X}, ${收藏Y})`);
        } else if (带分享按钮) {
            // 带分享按钮(视频)时，收藏按钮在收藏数上方
            收藏X = 按钮信息.收藏数文本.坐标.中心X;
            收藏Y = 按钮信息.收藏数文本.坐标.中心Y - 75; // Y向上偏移75像素
            console.log(`视频界面：点击收藏数上方Y偏移位置 (${收藏X}, ${收藏Y})`);
        } else {
            // 无分享按钮(图文)时，收藏按钮在收藏数左侧
            收藏X = 按钮信息.收藏数文本.坐标.左 - 75; // X向左偏移75像素
            收藏Y = 按钮信息.收藏数文本.坐标.中心Y;
            console.log(`图文界面：点击收藏数左侧X偏移位置 (${收藏X}, ${收藏Y})`);
        }
        
        // 执行点击
        try {
            if (操作模式 === 1) {
                // 无障碍模式点击
                click(收藏X, 收藏Y);
            } else {
                // Root模式点击
                let cmd = `input tap ${收藏X} ${收藏Y}`;
                let result = 执行Root命令(cmd);
                if (result.code === 0) {
                    收藏成功 = true;
                }
            }
            console.log(`收藏操作执行完成，坐标: (${收藏X}, ${收藏Y})`);
            收藏成功 = true;
            
            // 等待一小段时间，让UI反应
            sleep(500);
        } catch (e) {
            console.error(`收藏操作失败: ${e}`);
        }
    }
    
    console.log(`互动操作结果: 点赞=${点赞成功}, 收藏=${收藏成功}`);
    return {
        点赞成功: 点赞成功,
        收藏成功: 收藏成功
    };
}

// 主函数
function main() {
    console.log("===== 开始测试 Root Shell 模式 =====");
    
    // 检查操作模式
    if (!检查操作模式()) {
        console.log("操作模式检查失败，退出测试");
        return;
    }
    
    // 获取当前界面XML
    let xmlContent = 获取界面XML();
    if (!xmlContent) {
        console.log("获取界面XML失败，退出测试");
        return;
    }
    
    // 提取文本元素
    let 元素列表 = 提取文本元素(xmlContent);
    
    // 打印所有文本元素
    console.log("==== 当前界面文本元素 ====");
    for (let i = 0; i < 元素列表.length; i++) {
        let 元素 = 元素列表[i];
        // 跳过空文本内容
        if (!元素.文本 || 元素.文本.trim() === "") continue;
        
        console.log((i + 1) + ". 文本: [" + 元素.文本 + "], 坐标: (" + 
                   元素.坐标.中心X + ", " + 元素.坐标.中心Y + ")");
    }
    console.log("==== 共 " + 元素列表.length + " 个有效元素 ====");
    
    // 标记核心元素并获取按钮信息
    let 按钮信息 = 标记核心元素(元素列表);
    
    // 如果有按钮信息，判断按钮状态
    if (按钮信息) {
        console.log("\n==== 开始判断按钮状态 ====");
        let 按钮状态 = 判断按钮状态(按钮信息);
        console.log(`点赞状态: ${按钮状态.已点赞 ? "已点赞" : "未点赞"}`);
        console.log(`收藏状态: ${按钮状态.已收藏 ? "已收藏" : "未收藏"}`);
        
        // 是否执行互动操作的测试
        let 执行互动测试 = true;  // 设置为true可以测试实际点击操作
        
        if (执行互动测试) {
            console.log("\n==== 开始测试互动操作 ====");
            
            // 根据当前状态决定是否需要点赞和收藏
            // 如果已经点赞/收藏了，则不再执行相应操作
            let 需要点赞 = !按钮状态.已点赞;
            let 需要收藏 = !按钮状态.已收藏;
            
            if (!需要点赞 && !需要收藏) {
                console.log("已经点赞和收藏过了，无需执行互动操作");
            } else {
                if (需要点赞) {
                    console.log("准备执行点赞操作");
                }
                if (需要收藏) {
                    console.log("准备执行收藏操作");
                }
                
                // 执行互动操作
                let 操作结果 = 执行互动操作(按钮信息, 需要点赞, 需要收藏);
                
                // 操作后再次检测状态
                sleep(1000); // 等待一段时间让UI更新
                console.log("\n==== 互动操作后再次检测按钮状态 ====");
                
                // 重新获取界面XML
                xmlContent = 获取界面XML();
                if (xmlContent) {
                    元素列表 = 提取文本元素(xmlContent);
                    按钮信息 = 标记核心元素(元素列表);
                    
                    if (按钮信息) {
                        按钮状态 = 判断按钮状态(按钮信息);
                        console.log(`操作后点赞状态: ${按钮状态.已点赞 ? "已点赞" : "未点赞"}`);
                        console.log(`操作后收藏状态: ${按钮状态.已收藏 ? "已收藏" : "未收藏"}`);
                        
                        // 检查操作是否成功
                        if (需要点赞 && !按钮状态.已点赞) {
                            console.log("警告：点赞操作可能未成功，请检查坐标偏移或颜色识别");
                        }
                        if (需要收藏 && !按钮状态.已收藏) {
                            console.log("警告：收藏操作可能未成功，请检查坐标偏移或颜色识别");
                        }
                    }
                }
            }
        }
    } else {
        console.log("未找到足够的互动元素，无法进行按钮状态检测和互动操作");
    }
    
    console.log("\n===== 测试完成 =====");
}

// 直接执行主函数
main();

