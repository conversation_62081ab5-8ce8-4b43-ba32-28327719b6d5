﻿"ui";

// 全局变量，用于标记是否已经在运行  335
let 正在运行 = false;

// 全局变量，操作模式: 1=无障碍模式, 2=Root Shell模式
let 操作模式 = 2; // 默认使用Root Shell模式

// 全局变量，保存root会话状态
let root会话 = false;

// 创建UI界面
ui.layout(
    <vertical padding="16">
        <text textSize="24sp" textColor="#FF5722" gravity="center" margin="0 0 0 16">小红书自动互动工具</text>
        
        <text textSize="16sp" textColor="#666666" margin="0 16 0 0">操作模式</text>
        <horizontal>
            <radiogroup id="操作模式选择" orientation="horizontal">
                <radio id="无障碍模式" text="无障碍模式" textColor="#666666" />
                <radio id="Root模式" text="Root模式" textColor="#666666" checked="true" />
            </radiogroup>
        </horizontal>
        
        <text textSize="16sp" textColor="#666666" margin="0 16 0 0">互动选项</text>
        <horizontal>
            <checkbox id="需要点赞" text="点赞" checked="true" textColor="#666666" />
            <checkbox id="需要收藏" text="收藏" textColor="#666666" marginLeft="16" />
        </horizontal>
        
        <text textSize="16sp" textColor="#666666" margin="0 16 0 0">链接文件设置</text>
        <horizontal>
            <text text="文件路径:" textColor="#666666" />
            <input id="文件路径" text="/mnt/shared/Pictures/" layout_weight="1" />
        </horizontal>
        <horizontal>
            <text text="文件名:" textColor="#666666" />
            <input id="文件名" text="links.txt" layout_weight="1" />
        </horizontal>
        
        <text textSize="16sp" textColor="#666666" margin="0 16 0 0">操作设置</text>
        <horizontal>
            <text text="最大操作数:" textColor="#666666" />
            <input id="最大操作数" text="10" inputType="number" layout_weight="1" />
        </horizontal>
        <horizontal>
            <text text="操作间隔(秒):" textColor="#666666" />
            <input id="操作间隔" text="5" inputType="number" layout_weight="1" />
        </horizontal>
        
        <button id="开始按钮" text="开始执行" style="Widget.AppCompat.Button.Colored" margin="0 16 0 0" />
        
        <text id="状态文本" textSize="14sp" textColor="#333333" margin="0 16 0 0" />
    </vertical>
);

// 监听操作模式选择变化
ui.无障碍模式.on("check", function(checked) {
    if (checked) {
        操作模式 = 1;
        ui.状态文本.setText("已选择无障碍模式，请确保已开启无障碍服务");
    }
});

ui.Root模式.on("check", function(checked) {
    if (checked) {
        操作模式 = 2;
        ui.状态文本.setText("已选择Root模式，请确保已获取Root权限");
    }
});

// 开始按钮点击事件
ui.开始按钮.click(function() {
    // 防止重复点击
    if (正在运行) {
        toast("任务已在运行中，请等待完成");
        return;
    }
    
    正在运行 = true;
    
    // 获取用户输入
    let 文件路径 = ui.文件路径.text();
    let 文件名 = ui.文件名.text();
    let 最大操作数 = parseInt(ui.最大操作数.text());
    let 操作间隔 = parseInt(ui.操作间隔.text());
    
    // 获取互动选项
    let 需要点赞 = ui.需要点赞.checked;
    let 需要收藏 = ui.需要收藏.checked;
    
    // 检查是否至少选择了一个互动选项
    if (!需要点赞 && !需要收藏) {
        toast("请至少选择一个互动选项（点赞或收藏）");
        正在运行 = false;
        return;
    }
    
    // 更新状态
    ui.状态文本.setText("开始执行，即将隐藏界面...");
    
    // 隐藏界面
    ui.layout(
        <frame>
            <text text="正在后台运行..." textSize="12sp" textColor="#888888" gravity="center"/>
        </frame>
    );
    
    // 创建单个工作线程
    let 工作线程 = threads.start(function() {
        try {
            // 根据操作模式检查权限
            if (操作模式 === 1) {
                // 无障碍模式，检查无障碍服务是否已启用
                if (!auto.service) {
                    toast("无障碍服务未启用，尝试启动...");
                    auto.waitFor();
                }
            } else if (操作模式 === 2) {
                // Root模式，检查Root权限
                if (!初始化Root权限()) {
                    toast("警告：未获取到 root 权限，脚本可能无法正常工作");
                    等待(2000);
                    正在运行 = false;
                    return;
                }
                console.log("Root权限检查通过");
            }
            
            // 请求截屏权限
            if (!requestScreenCapture()) {
                toast("请求截屏权限失败");
                正在运行 = false;
                return;
            }
            
            // 执行主要功能
            执行自动互动(文件路径, 文件名, 最大操作数, 操作间隔, 需要点赞, 需要收藏);
        } catch (e) {
            console.error("执行过程中出错: " + e);
        } finally {
            // 无论如何都要重置运行状态
            正在运行 = false;
        }
    });
});

// 添加脚本退出时的清理函数
events.on("exit", function() {
    console.log("脚本即将退出，执行清理工作...");
    
    // 重置root会话状态
    root会话 = false;
    
    console.log("清理工作完成");
});

/**
 * 初始化Root权限，获取一个root会话
 * @returns {boolean} 是否成功获取root权限
 */
function 初始化Root权限() {
    // 如果已经获取过root权限，直接返回true，不再重复申请
    if (root会话) {
        console.log("已有root会话，无需重新申请权限");
        return true;
    }
    
    console.log("首次尝试获取root权限...");
    
    try {
        // 使用普通shell命令测试root权限
        let result = shell("su -c 'echo root_test'", true);
        
        if (result.code === 0 && result.result.includes("root_test")) {
            console.log("成功获取root权限，标记为已授权");
            root会话 = true;
            return true;
        } else {
            console.log("获取root权限失败: " + result.error);
            root会话 = false;
            return false;
        }
    } catch (e) {
        console.error("初始化Root权限出错: " + e);
        root会话 = false;
        return false;
    }
}

//---------
//=====================

// 检查操作模式函数
function 检查操作模式() {
    if (操作模式 === 1) {
        console.log("当前使用无障碍模式操作，请确保已开启无障碍服务");
        // 检查无障碍服务是否已启用
        if (!auto.service) {
            console.log("无障碍服务未启用，尝试启动...");
            auto.waitFor();
        }
    } else if (操作模式 === 2) {
        console.log("当前使用Root Shell模式操作，请确保已获取Root权限");
        // 检查Root权限，如果没有root会话，则尝试初始化一次
        if (!root会话) {
            if (!初始化Root权限()) {
                console.log("警告：未获取到 root 权限，脚本可能无法正常工作");
                return false;
            }
        }
        console.log("Root权限检查通过");
        return true;
    } else {
        console.log("错误：未知的操作模式，请设置为1(无障碍)或2(Root Shell)");
        return false;
    }
    return true;
}

/**
 * 使用 uiautomator dump 获取当前界面的 XML 结构
 * @returns {string|null} XML 内容或 null（如果失败）
 */
function 获取界面XML() {
    console.log("开始获取界面 XML...");
    
    // 使用执行Root命令函数，确保root权限检查
    let result = 执行Root命令('uiautomator dump /sdcard/window_dump.xml');
    
    if (result.code === 0) {
        console.log("界面 XML 导出成功");
        
        // 读取导出的 XML 文件
        try {
            let xmlContent = files.read("/sdcard/window_dump.xml");
            console.log("成功读取 XML 文件，大小: " + xmlContent.length + " 字节");
            return xmlContent;
        } catch (e) {
            console.error("读取 XML 文件失败: " + e.message);
            return null;
        }
    } else {
        console.error("界面 XML 导出失败: " + result.error);
        return null;
    }
}

/**
 * 从 XML 中提取所有文本元素及其坐标
 * @param {string} xmlContent - XML 内容
 * @returns {Array} 元素数组，每个元素包含文本和坐标
 */
function 提取文本元素(xmlContent) {
    console.log("开始解析 XML 中的文本元素...");
    
    let 元素列表 = [];
    let 文本正则 = /text="([^"]*)"[^>]*bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"/g;
    let 匹配结果;
    let 空文本计数 = 0;
    
    while ((匹配结果 = 文本正则.exec(xmlContent)) !== null) {
        let 文本 = 匹配结果[1];
        
        // 跳过空文本内容
        if (!文本 || 文本.trim() === "") {
            空文本计数++;
            continue;
        }
        
        let 左 = parseInt(匹配结果[2]);
        let 上 = parseInt(匹配结果[3]);
        let 右 = parseInt(匹配结果[4]);
        let 下 = parseInt(匹配结果[5]);
        
        // 计算中心点坐标
        let 中心X = Math.floor((左 + 右) / 2);
        let 中心Y = Math.floor((上 + 下) / 2);
        
        元素列表.push({
            文本: 文本,
            坐标: {
                左: 左,
                上: 上,
                右: 右,
                下: 下,
                中心X: 中心X,
                中心Y: 中心Y
            }
        });
    }
    
    console.log("共找到 " + 元素列表.length + " 个有效文本元素 (已过滤 " + 空文本计数 + " 个空文本元素)");
    return 元素列表;
}

/**
 * 从 XML 中提取特定文本的元素
 * @param {string} xmlContent - XML 内容
 * @param {string} 目标文本 - 要查找的文本（部分匹配）
 * @returns {Object|null} 找到的元素或 null
 */
function 查找特定文本元素(xmlContent, 目标文本) {
    console.log("查找包含文本 '" + 目标文本 + "' 的元素...");
    
    let 所有元素 = 提取文本元素(xmlContent);
    
    // 查找包含目标文本的元素
    for (let i = 0; i < 所有元素.length; i++) {
        if (所有元素[i].文本.includes(目标文本)) {
            console.log("找到匹配元素: " + JSON.stringify(所有元素[i]));
            return 所有元素[i];
        }
    }
    
    console.log("未找到包含文本 '" + 目标文本 + "' 的元素");
    return null;
}

/**
 * 查找并点击特定文本的元素
 * @param {string} 目标文本 - 要查找的文本
 * @returns {boolean} 是否成功
 */
function 查找并点击(目标文本) {
    let xmlContent = 获取界面XML();
    if (!xmlContent) return false;
    
    let 元素 = 查找特定文本元素(xmlContent, 目标文本);
    if (!元素) return false;
    
    return 点击(元素.坐标.中心X, 元素.坐标.中心Y);
}

/**
 * 查找点赞按钮并点击
 * @returns {boolean} 是否成功
 */
function 查找点赞按钮() {
    let xmlContent = 获取界面XML();
    if (!xmlContent) return false;
    
    // 先尝试查找包含"点赞"的元素
    let 点赞元素 = 查找特定文本元素(xmlContent, "点赞");
    
    if (点赞元素) {
        return 点击(点赞元素.坐标.中心X, 点赞元素.坐标.中心Y);
    }
    
    // 如果没找到，可能需要查找特定的图标或其他标识
    console.log("未找到点赞按钮");
    return false;
}

/**
 * 打印当前界面的所有文本元素
 */
function 打印所有文本元素() {
    let xmlContent = 获取界面XML();
    if (!xmlContent) return;
    
    let 元素列表 = 提取文本元素(xmlContent);
    
    console.log("==== 当前界面文本元素 ====");
    for (let i = 0; i < 元素列表.length; i++) {
        let 元素 = 元素列表[i];
        // 跳过空文本内容
        if (!元素.文本 || 元素.文本.trim() === "") continue;
        
        console.log((i + 1) + ". 文本: [" + 元素.文本 + "], 坐标: (" + 
                   元素.坐标.中心X + ", " + 元素.坐标.中心Y + ")");
    }
    console.log("==== 共 " + 元素列表.length + " 个有效元素 ====");
}

/**
 * 查找元素
 * @param {string} 选择器 - 元素选择器，如text("文本")或id("id")
 * @returns {UiObject|null} 找到的元素或null
 */
function 查找元素(选择器) {
    if (操作模式 === 1) {
        // 无障碍模式
        try {
            let 元素 = 选择器.findOne(1000);
            return 元素 || null;
        } catch (e) {
            console.error("查找元素出错: " + e.message);
            return null;
        }
    } else {
        // Root Shell模式下无法直接使用无障碍选择器
        console.log("Root Shell模式下不支持直接使用选择器查找元素");
        return null;
    }
}


/**
 * 查找文本元素并点击
 * @param {string} 文本 - 要查找的文本
 * @returns {boolean} 是否成功点击
 */
function 查找文本并点击(文本) {
    console.log("查找并点击文本: " + 文本);
    
    if (操作模式 === 1) {
        // 无障碍模式
        let 元素 = text(文本).findOne(3000);
        if (元素) {
            元素.click();
            return true;
        }
        return false;
    } else {
        // Root Shell模式
        return 查找并点击(文本);
    }
}


/**
 * 常用操控函数封装 - 使用 shell 命令替代无障碍服务
 * 以下函数都使用 root 权限执行 shell 命令
 */

/**
 * 返回键操作
 * @returns {boolean} 是否执行成功
 */
function 返回() {
    console.log("执行返回操作");
    
    if (操作模式 === 1) {
        // 无障碍模式
        back();
        return true;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('input keyevent 4'); // KEYCODE_BACK = 4
        let 成功 = result.code === 0;
        console.log(成功 ? "返回操作成功" : "返回操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 回到主页
 * @returns {boolean} 是否执行成功
 */
function 主页() {
    console.log("执行回到主页操作");
    
    if (操作模式 === 1) {
        // 无障碍模式
        home();
        return true;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('input keyevent 3'); // KEYCODE_HOME = 3
        let 成功 = result.code === 0;
        console.log(成功 ? "主页操作成功" : "主页操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 打开最近任务
 * @returns {boolean} 是否执行成功
 */
function 最近任务() {
    console.log("执行打开最近任务操作");
    
    if (操作模式 === 1) {
        // 无障碍模式
        recents();
        return true;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('input keyevent 187'); // KEYCODE_APP_SWITCH = 187
        let 成功 = result.code === 0;
        console.log(成功 ? "最近任务操作成功" : "最近任务操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 点击屏幕
 * @param {number} x - X 坐标
 * @param {number} y - Y 坐标
 * @returns {boolean} 是否执行成功
 */
function 点击(x, y) {
    console.log("执行点击操作: (" + x + ", " + y + ")");
    
    if (操作模式 === 1) {
        // 无障碍模式
        click(x, y);
        return true;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('input tap ' + x + ' ' + y);
        let 成功 = result.code === 0;
        console.log(成功 ? "点击操作成功" : "点击操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 长按屏幕
 * @param {number} x - X 坐标
 * @param {number} y - Y 坐标
 * @param {number} 时长 - 长按时长(毫秒)，默认1000ms
 * @returns {boolean} 是否执行成功
 */
function 长按(x, y, 时长 = 1000) {
    console.log("执行长按操作: (" + x + ", " + y + "), 时长: " + 时长 + "ms");
    
    if (操作模式 === 1) {
        // 无障碍模式
        press(x, y, 时长);
        return true;
    } else {
        // Root Shell模式 - 使用swipe命令在同一位置停留来模拟长按
        let result = 执行Root命令('input swipe ' + x + ' ' + y + ' ' + x + ' ' + y + ' ' + 时长);
        let 成功 = result.code === 0;
        console.log(成功 ? "长按操作成功" : "长按操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 滑动屏幕
 * @param {number} 起点x - 起点X坐标
 * @param {number} 起点y - 起点Y坐标
 * @param {number} 终点x - 终点X坐标
 * @param {number} 终点y - 终点Y坐标
 * @param {number} 时长 - 滑动时长(毫秒)，默认500ms
 * @returns {boolean} 是否执行成功
 */
function 滑动(起点x, 起点y, 终点x, 终点y, 时长 = 500) {
    console.log("执行滑动操作: (" + 起点x + ", " + 起点y + ") -> (" + 终点x + ", " + 终点y + "), 时长: " + 时长 + "ms");
    
    if (操作模式 === 1) {
        // 无障碍模式
        swipe(起点x, 起点y, 终点x, 终点y, 时长);
        return true;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('input swipe ' + 起点x + ' ' + 起点y + ' ' + 终点x + ' ' + 终点y + ' ' + 时长);
        let 成功 = result.code === 0;
        console.log(成功 ? "滑动操作成功" : "滑动操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 上滑屏幕
 * @param {number} 距离 - 滑动距离，默认屏幕高度的1/3
 * @param {number} 时长 - 滑动时长(毫秒)，默认500ms
 * @returns {boolean} 是否执行成功
 */
function 上滑(距离 = null, 时长 = 500) {
    let 屏幕宽度 = device.width;
    let 屏幕高度 = device.height;
    
    // 如果未指定距离，默认为屏幕高度的1/3
    距离 = 距离 || Math.floor(屏幕高度 / 3);
    
    let 起点x = Math.floor(屏幕宽度 / 2);
    let 起点y = Math.floor(屏幕高度 * 0.7);
    let 终点y = 起点y - 距离;
    
    return 滑动(起点x, 起点y, 起点x, 终点y, 时长);
}

/**
 * 下滑屏幕
 * @param {number} 距离 - 滑动距离，默认屏幕高度的1/3
 * @param {number} 时长 - 滑动时长(毫秒)，默认500ms
 * @returns {boolean} 是否执行成功
 */
function 下滑(距离 = null, 时长 = 500) {
    let 屏幕宽度 = device.width;
    let 屏幕高度 = device.height;
    
    // 如果未指定距离，默认为屏幕高度的1/3
    距离 = 距离 || Math.floor(屏幕高度 / 3);
    
    let 起点x = Math.floor(屏幕宽度 / 2);
    let 起点y = Math.floor(屏幕高度 * 0.3);
    let 终点y = 起点y + 距离;
    
    return 滑动(起点x, 起点y, 起点x, 终点y, 时长);
}

/**
 * 左滑屏幕
 * @param {number} 距离 - 滑动距离，默认屏幕宽度的1/3
 * @param {number} 时长 - 滑动时长(毫秒)，默认500ms
 * @returns {boolean} 是否执行成功
 */
function 左滑(距离 = null, 时长 = 500) {
    let 屏幕宽度 = device.width;
    let 屏幕高度 = device.height;
    
    // 如果未指定距离，默认为屏幕宽度的1/3
    距离 = 距离 || Math.floor(屏幕宽度 / 3);
    
    let 起点y = Math.floor(屏幕高度 / 2);
    let 起点x = Math.floor(屏幕宽度 * 0.7);
    let 终点x = 起点x - 距离;
    
    return 滑动(起点x, 起点y, 终点x, 起点y, 时长);
}

/**
 * 右滑屏幕
 * @param {number} 距离 - 滑动距离，默认屏幕宽度的1/3
 * @param {number} 时长 - 滑动时长(毫秒)，默认500ms
 * @returns {boolean} 是否执行成功
 */
function 右滑(距离 = null, 时长 = 500) {
    let 屏幕宽度 = device.width;
    let 屏幕高度 = device.height;
    
    // 如果未指定距离，默认为屏幕宽度的1/3
    距离 = 距离 || Math.floor(屏幕宽度 / 3);
    
    let 起点y = Math.floor(屏幕高度 / 2);
    let 起点x = Math.floor(屏幕宽度 * 0.3);
    let 终点x = 起点x + 距离;
    
    return 滑动(起点x, 起点y, 终点x, 起点y, 时长);
}

/**
 * 输入文本
 * @param {string} 文本 - 要输入的文本
 * @returns {boolean} 是否执行成功
 */
function 输入文本(文本) {
    console.log("执行输入文本操作: " + 文本);
    
    if (操作模式 === 1) {
        // 无障碍模式
        input(文本);
        return true;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        // 注意需要转义双引号
        let 安全文本 = 文本.replace(/"/g, '\\"');
        let result = 执行Root命令('input text "' + 安全文本 + '"');
        let 成功 = result.code === 0;
        console.log(成功 ? "输入文本成功" : "输入文本失败: " + result.error);
        return 成功;
    }
}

/**
 * 按下按键
 * @param {number} 按键码 - 按键的keycode
 * @returns {boolean} 是否执行成功
 */
function 按键(按键码) {
    console.log("执行按键操作: " + 按键码);
    
    if (操作模式 === 1) {
        // 无障碍模式
        keycode(按键码);
        return true;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('input keyevent ' + 按键码);
        let 成功 = result.code === 0;
        console.log(成功 ? "按键操作成功" : "按键操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 常用按键码
 */
const 按键码 = {
    返回: 4,      // KEYCODE_BACK
    主页: 3,      // KEYCODE_HOME
    菜单: 82,     // KEYCODE_MENU
    搜索: 84,     // KEYCODE_SEARCH
    电源: 26,     // KEYCODE_POWER
    相机: 27,     // KEYCODE_CAMERA
    最近任务: 187, // KEYCODE_APP_SWITCH
    音量加: 24,   // KEYCODE_VOLUME_UP
    音量减: 25,   // KEYCODE_VOLUME_DOWN
    静音: 164,    // KEYCODE_VOLUME_MUTE
    亮度加: 221,  // KEYCODE_BRIGHTNESS_UP
    亮度减: 220,  // KEYCODE_BRIGHTNESS_DOWN
    上: 19,       // KEYCODE_DPAD_UP
    下: 20,       // KEYCODE_DPAD_DOWN
    左: 21,       // KEYCODE_DPAD_LEFT
    右: 22,       // KEYCODE_DPAD_RIGHT
    确定: 23,     // KEYCODE_DPAD_CENTER
    通话: 5,      // KEYCODE_CALL
    挂断: 6,      // KEYCODE_ENDCALL
    锁屏: 223     // KEYCODE_SLEEP
};

/**
 * 启动应用
 * @param {string} 包名 - 应用包名
 * @returns {boolean} 是否执行成功
 */
function 启动应用(包名) {
    console.log("启动应用: " + 包名);
    
    if (操作模式 === 1) {
        // 无障碍模式
        app.launch(包名);
        return true;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('am start -n ' + 包名 + '/.MainActivity');
        
        // 如果上面的命令失败，尝试使用更通用的方式
        if (result.code !== 0) {
            result = 执行Root命令('monkey -p ' + 包名 + ' -c android.intent.category.LAUNCHER 1');
        }
        
        let 成功 = result.code === 0;
        console.log(成功 ? "启动应用成功" : "启动应用失败: " + result.error);
        return 成功;
    }
}

/**
 * 关闭应用
 * @param {string} 包名 - 应用包名
 * @returns {boolean} 是否执行成功
 */
function 关闭应用(包名) {
    console.log("关闭应用: " + 包名);
    
    if (操作模式 === 1) {
        // 无障碍模式
        app.openAppSetting(包名);
        等待(1000);
        let 强行停止 = text("强行停止").findOne(2000);
        if (强行停止) {
            强行停止.click();
            等待(1000);
            let 确认 = text("确定").findOne(2000) || 
                      text("确认").findOne() || 
                      text("强行停止").findOne();
            if (确认) {
                确认.click();
                等待(1000);
                返回();
                return true;
            }
        }
        返回();
        return false;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('am force-stop ' + 包名);
        let 成功 = result.code === 0;
        console.log(成功 ? "关闭应用成功" : "关闭应用失败: " + result.error);
        return 成功;
    }
}

/**
 * 获取当前应用包名
 * @returns {string} 当前应用包名
 */
function 获取当前应用() {
    console.log("获取当前应用包名");
    
    if (操作模式 === 1) {
        // 无障碍模式
        let 包名 = currentPackage();
        console.log("当前应用包名: " + 包名);
        return 包名;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('dumpsys window | grep mCurrentFocus');
        if (result.code === 0) {
            let match = result.result.match(/mCurrentFocus.+?{.+?(\S+)\/(\S+)}/);
            if (match && match[1]) {
                let 当前包名 = match[1];
                console.log("当前应用包名: " + 当前包名);
                return 当前包名;
            }
        }
        console.log("获取当前应用包名失败");
        return null;
    }
}

/**
 * 等待指定时间
 * @param {number} 毫秒 - 等待时间(毫秒)
 */
function 等待(毫秒) {
    console.log("等待 " + 毫秒 + " 毫秒");
    sleep(毫秒);
}

/**
 * 检查应用是否安装
 * @param {string} 包名 - 应用包名
 * @returns {boolean} 是否已安装
 */
function 应用已安装(包名) {
    console.log("检查应用是否安装: " + 包名);
    
    // 使用执行Root命令函数
    let result = 执行Root命令('pm list packages | grep ' + 包名);
    let 已安装 = result.code === 0 && result.result.includes(包名);
    console.log(已安装 ? "应用已安装" : "应用未安装");
    return 已安装;
}

//=====================
//---------









/**
 * 强制退出小红书应用
 * 使用AutoJS官方API关闭小红书应用，无需root权限
 * @returns {boolean} - 是否成功退出
 */
function 强制退出小红书() {
    console.log("强制退出小红书应用");
    
    try {
        if (操作模式 === 1) {
            // 无障碍模式
            // 方法1: 使用app.killApp方法(AutoJS 6.0.0+)
            if (app.killApp && typeof app.killApp === 'function') {
                let result = app.killApp("com.xingin.xhs");
                console.log("使用app.killApp退出小红书: " + (result ? "成功" : "失败"));
                等待(1000);
                return result;
            }
            
            // 方法2: 使用app.openAppSetting打开应用设置，然后模拟点击"强行停止"按钮
            console.log("尝试通过应用设置强制停止小红书");
            app.openAppSetting("com.xingin.xhs");
            等待(1000); // 等待设置页面打开
            
            // 查找"强行停止"按钮并点击
            let 强行停止按钮 = textMatches(/(强.停止|强制.*停止|结束运行|停止运行|force.*stop)/).findOne(2000);
            if (强行停止按钮) {
                强行停止按钮.click();
                等待(1000);
                
                // 查找确认对话框中的"确定"按钮
                let 确认按钮 = textMatches(/(确定|确认|是|OK|强行停止)/).findOne(2000);
                if (确认按钮) {
                    确认按钮.click();
                    等待(1000);
                    console.log("已成功强制停止小红书");
                    
                    // 返回到之前的界面
                    返回();
                    等待(500);
                    return true;
                }
            } else {
                // 如果没找到强行停止按钮，可能应用已经不在运行
                console.log("未找到强行停止按钮，应用可能已经不在运行");
                返回(); // 返回到之前的界面
                等待(500);
                return true;
            }
            
            console.log("无法通过应用设置强制停止小红书");
            // 返回到之前的界面
            返回();
            等待(500);
            return false;
        } else {
            // Root模式 - 使用执行Root命令函数
            console.log("使用Root权限强制停止小红书");
            let result = 执行Root命令('am force-stop com.xingin.xhs');
            let 成功 = result.code === 0;
            console.log(成功 ? "Root模式强制停止小红书成功" : "Root模式强制停止小红书失败: " + result.error);
            等待(1000);
            return 成功;
        }
    } catch (e) {
        console.error("强制退出小红书出错: " + e.message);
        // 尝试返回到之前的界面
        try {
            返回();
        } catch (e2) {
            // 忽略返回键错误
        }
        等待(500);
        return false;
    }
}

/**
 * 直接对链接文章进行点赞
 * @param {string} 链接 - 要点赞的文章链接
 * @returns {boolean} - 是否成功点赞
 */
function 直接点赞链接文章(链接) {
    console.log("调用兼容版本的直接点赞链接文章: " + 链接);
    
    // 调用新版本的直接互动链接文章函数，只执行点赞操作
    let 操作结果 = 直接互动链接文章(链接, true, false);
    
    // 返回点赞是否成功
    return 操作结果.成功 && 操作结果.点赞状态;
}

/**
 * 执行自动点赞主功能
 * @param {string} 文件路径 - 文件所在路径
 * @param {string} 文件名 - 文件名
 * @param {number} 最大操作数 - 最大操作链接数
 * @param {number} 操作间隔 - 操作间隔时间(秒)
 */
function 执行自动点赞(文件路径, 文件名, 最大操作数, 操作间隔) {
    console.log("调用兼容版本的执行自动点赞");
    // 调用新版本的执行自动互动函数，只执行点赞操作
    执行自动互动(文件路径, 文件名, 最大操作数, 操作间隔, true, false);
}

/**
 * 执行自动互动主功能
 * @param {string} 文件路径 - 文件所在路径
 * @param {string} 文件名 - 文件名
 * @param {number} 最大操作数 - 最大操作链接数
 * @param {number} 操作间隔 - 操作间隔时间(秒)
 * @param {boolean} 需要点赞 - 是否需要执行点赞操作
 * @param {boolean} 需要收藏 - 是否需要执行收藏操作
 */
function 执行自动互动(文件路径, 文件名, 最大操作数, 操作间隔, 需要点赞 = true, 需要收藏 = false) {
    // 读取链接文件
    let 完整路径 = 文件路径 + 文件名;
    console.log("准备读取链接文件: " + 完整路径);
    
    let 链接列表 = 读取链接文件(完整路径);
    if (!链接列表 || 链接列表.length === 0) {
        toast("链接文件读取失败或为空: " + 完整路径);
        console.error("链接文件读取失败或为空: " + 完整路径);
        return;
    }
    
    // 限制操作数量
    let 实际操作数 = Math.min(链接列表.length, 最大操作数);
    toast(`共读取到 ${链接列表.length} 条链接，计划处理 ${实际操作数} 条`);
    
    // 循环处理每个链接
    for (let i = 0; i < 实际操作数; i++) {
        try {
            let 链接 = 链接列表[i];
            toast(`处理第 ${i + 1} 条链接: ${链接}`);
            
            // 在打开链接前先强制退出小红书
            强制退出小红书();
            
            // 打开链接并获取页面信息
            app.openUrl(链接);
            console.log("已打开链接: " + 链接);
            等待(3000); // 等待页面加载
            
            // 处理可能的弹窗，直到进入小红书
            if (!处理打开流程()) {
                console.log("处理打开流程失败，无法进入小红书");
                continue;
            }
            
            等待(2000); // 等待页面完全加载
            
            // 首先检测当前状态
            let 当前状态 = 检测文章状态();
            console.log(`链接打开后状态检测: 已点赞=${当前状态.已点赞}, 已收藏=${当前状态.已收藏}`);
            
            // 判断是否需要继续执行
            // 如果只需要点赞且已点赞，或只需要收藏且已收藏，或两者都需要且都已完成，则跳过
            if ((需要点赞 && !需要收藏 && 当前状态.已点赞) || 
                (!需要点赞 && 需要收藏 && 当前状态.已收藏) || 
                (需要点赞 && 需要收藏 && 当前状态.已点赞 && 当前状态.已收藏)) {
                console.log("所需操作已完成，跳过此链接");
                toast("所需操作已完成，跳过此链接");
                等待(1000);
                continue;
            }
            
            // 获取链接页面信息
            let 链接页面信息 = 获取页面信息();
            // 只输出需要的信息
            console.log("链接页面信息: 标题=[" + 链接页面信息.标题 + "], 内容=[" + 链接页面信息.内容 + "], 作者=[" + 链接页面信息.用户名 + "]");
            
            // 如果需要执行操作，且当前已在文章页面，先尝试执行操作
            if ((需要点赞 && !当前状态.已点赞) || (需要收藏 && !当前状态.已收藏)) {
                console.log("直接在当前页面执行互动操作");
                
                // 判断是视频还是图文，进行相应等待或滑动
                if (链接页面信息.是否视频) {
                    // 如果是视频，随机等待5-10秒
                    let 视频等待时间 = 5000 + Math.floor(Math.random() * 5000); // 5000-10000毫秒
                    console.log("视频内容，等待 " + (视频等待时间 / 1000) + " 秒");
                    等待(视频等待时间);
                } else {
                    // 如果是图文，随机上划并延时
                    // 随机延时1-3秒
                    let 图文等待时间 = 1000 + Math.floor(Math.random() * 2000); // 1000-3000毫秒
                    console.log("图文内容，等待 " + (图文等待时间 / 1000) + " 秒");
                    等待(图文等待时间);
                    
                    // 随机上划
                    let 屏幕宽度 = device.width;
                    let 屏幕高度 = device.height;
                    
                    // 上划起点和终点的Y坐标（在屏幕下半部分上划）
                    let 起点Y = 屏幕高度 * (0.7 + Math.random() * 0.2); // 屏幕70%-90%位置
                    let 终点Y = 屏幕高度 * (0.3 + Math.random() * 0.2); // 屏幕30%-50%位置
                    
                    // X坐标在屏幕中间附近随机
                    let X坐标 = 屏幕宽度 * (0.4 + Math.random() * 0.2); // 屏幕40%-60%位置
                    
                    // 执行滑动，持续时间300-800毫秒
                    let 滑动时间 = 300 + Math.floor(Math.random() * 500);
                    console.log("执行上划操作: 从 (" + X坐标 + ", " + 起点Y + ") 到 (" + X坐标 + ", " + 终点Y + ")，持续 " + 滑动时间 + " 毫秒");
                    滑动(X坐标, 起点Y, X坐标, 终点Y, 滑动时间);
                    
                    // 滑动后再等待1-2秒
                    let 滑动后等待时间 = 1000 + Math.floor(Math.random() * 1000);
                    等待(滑动后等待时间);
                }
                
                // 执行互动操作
                let 操作结果 = 执行互动操作(需要点赞 && !当前状态.已点赞, 需要收藏 && !当前状态.已收藏);
                
                if ((需要点赞 && 操作结果.已完成点赞) || (需要收藏 && 操作结果.已完成收藏)) {
                    let 状态消息 = "";
                    if (需要点赞) 状态消息 += 操作结果.已完成点赞 ? "点赞成功" : "点赞失败";
                    if (需要收藏) 状态消息 += 操作结果.已完成收藏 ? (状态消息 ? ", 收藏成功" : "收藏成功") : (状态消息 ? ", 收藏失败" : "收藏失败");
                    toast(状态消息);
                    
                    // 如果所有需要的操作都已完成，跳过后续步骤
                    if ((!需要点赞 || 操作结果.已完成点赞) && (!需要收藏 || 操作结果.已完成收藏)) {
                        console.log("所有操作已完成，准备处理下一条链接");
                        
                        // 返回主界面，准备处理下一个链接
                        返回主界面();
                        等待(操作间隔 * 1000);
                        continue;
                    }
                }
            }
            
            // 返回主界面
            if (!返回主界面()) {
                console.log("返回主界面失败");
                
                // 如果返回主界面失败，尝试重新通过浏览器打开链接
                console.log("尝试重新通过浏览器打开链接");
                强制退出小红书();
                等待(1000);
                
                app.openUrl(链接);
                console.log("已重新打开链接: " + 链接);
                等待(3000);
                
                if (!处理打开流程()) {
                    console.log("重新处理打开流程失败，跳过此链接");
                    continue;
                }
                
                等待(2000);
                
                // 重新检测状态
                当前状态 = 检测文章状态();
                
                // 执行互动操作
                if ((需要点赞 && !当前状态.已点赞) || (需要收藏 && !当前状态.已收藏)) {
                    // 判断是视频还是图文，进行相应等待或滑动
                    let 页面信息 = 获取页面信息();
                    if (页面信息.是否视频) {
                        let 视频等待时间 = 5000 + Math.floor(Math.random() * 5000);
                        console.log("视频内容，等待 " + (视频等待时间 / 1000) + " 秒");
                        等待(视频等待时间);
                    } else {
                        let 图文等待时间 = 1000 + Math.floor(Math.random() * 2000);
                        console.log("图文内容，等待 " + (图文等待时间 / 1000) + " 秒");
                        等待(图文等待时间);
                        
                        // 随机上划
                        let 屏幕宽度 = device.width;
                        let 屏幕高度 = device.height;
                        let 起点Y = 屏幕高度 * (0.7 + Math.random() * 0.2);
                        let 终点Y = 屏幕高度 * (0.3 + Math.random() * 0.2);
                        let X坐标 = 屏幕宽度 * (0.4 + Math.random() * 0.2);
                        let 滑动时间 = 300 + Math.floor(Math.random() * 500);
                        滑动(X坐标, 起点Y, X坐标, 终点Y, 滑动时间);
                        等待(1000 + Math.floor(Math.random() * 1000));
                    }
                    
                    let 操作结果 = 执行互动操作(需要点赞 && !当前状态.已点赞, 需要收藏 && !当前状态.已收藏);
                    
                    let 状态消息 = "";
                    if (需要点赞) 状态消息 += 操作结果.已完成点赞 ? "点赞成功" : "点赞失败";
                    if (需要收藏) 状态消息 += 操作结果.已完成收藏 ? (状态消息 ? ", 收藏成功" : "收藏成功") : (状态消息 ? ", 收藏失败" : "收藏失败");
                    toast(状态消息);
                }
                
                // 返回主界面，准备处理下一个链接
                返回主界面();
                等待(操作间隔 * 1000);
                continue;
            }
            
            // 点击首页第一篇文章
            if (!点击首篇文章()) {
                console.log("点击首页文章失败");
                continue;
            }
            
            等待(3000); // 等待文章加载
            
            // 获取首页文章信息
            let 首页文章信息 = 获取页面信息();
            // 只输出需要的信息
            console.log("首页文章信息: 标题=[" + 首页文章信息.标题 + "], 内容=[" + 首页文章信息.内容 + "], 作者=[" + 首页文章信息.用户名 + "]");
            
            // 比较两篇文章是否为同一篇
            let 是同一篇文章 = 比较页面信息(链接页面信息, 首页文章信息);
            
            if (是同一篇文章) {
                console.log("确认是同一篇文章，检查互动状态");
                
                // 检测当前状态
                当前状态 = 检测文章状态();
                
                // 执行互动操作，跳过已完成的操作
                if ((需要点赞 && !当前状态.已点赞) || (需要收藏 && !当前状态.已收藏)) {
                    console.log(`执行互动操作: 需要点赞=${需要点赞 && !当前状态.已点赞}, 需要收藏=${需要收藏 && !当前状态.已收藏}`);
                    let 操作结果 = 执行互动操作(需要点赞 && !当前状态.已点赞, 需要收藏 && !当前状态.已收藏);
                    
                    let 状态消息 = "";
                    if (需要点赞) 状态消息 += (当前状态.已点赞 ? "已点赞" : (操作结果.已完成点赞 ? "点赞成功" : "点赞失败"));
                    if (需要收藏) 状态消息 += (当前状态.已收藏 ? (状态消息 ? ", 已收藏" : "已收藏") : (操作结果.已完成收藏 ? (状态消息 ? ", 收藏成功" : "收藏成功") : (状态消息 ? ", 收藏失败" : "收藏失败")));
                    toast(状态消息);
                } else {
                    console.log("所有需要的操作都已完成，无需再次操作");
                    toast("所有操作已完成");
                }
            } else {
                console.log("不是同一篇文章，尝试直接通过浏览器打开链接");
                
                // 如果不是同一篇文章，尝试直接通过浏览器打开链接
                强制退出小红书();
                等待(1000);
                
                app.openUrl(链接);
                console.log("已重新打开链接: " + 链接);
                等待(3000);
                
                if (!处理打开流程()) {
                    console.log("重新处理打开流程失败，跳过此链接");
                    continue;
                }
                
                等待(2000);
                
                // 获取页面信息
                let 页面信息 = 获取页面信息();
                
                // 判断是视频还是图文，进行相应等待或滑动
                if (页面信息.是否视频) {
                    // 如果是视频，随机等待5-10秒
                    let 视频等待时间 = 5000 + Math.floor(Math.random() * 5000);
                    console.log("视频内容，等待 " + (视频等待时间 / 1000) + " 秒");
                    等待(视频等待时间);
                } else {
                    // 如果是图文，随机上划并延时
                    let 图文等待时间 = 1000 + Math.floor(Math.random() * 2000);
                    console.log("图文内容，等待 " + (图文等待时间 / 1000) + " 秒");
                    等待(图文等待时间);
                    
                    // 随机上划
                    let 屏幕宽度 = device.width;
                    let 屏幕高度 = device.height;
                    let 起点Y = 屏幕高度 * (0.7 + Math.random() * 0.2);
                    let 终点Y = 屏幕高度 * (0.3 + Math.random() * 0.2);
                    let X坐标 = 屏幕宽度 * (0.4 + Math.random() * 0.2);
                    let 滑动时间 = 300 + Math.floor(Math.random() * 500);
                    滑动(X坐标, 起点Y, X坐标, 终点Y, 滑动时间);
                    等待(1000 + Math.floor(Math.random() * 1000));
                }
                
                // 检测当前状态
                当前状态 = 检测文章状态();
                
                // 执行互动操作，跳过已完成的操作
                if ((需要点赞 && !当前状态.已点赞) || (需要收藏 && !当前状态.已收藏)) {
                    console.log(`执行互动操作: 需要点赞=${需要点赞 && !当前状态.已点赞}, 需要收藏=${需要收藏 && !当前状态.已收藏}`);
                    let 操作结果 = 执行互动操作(需要点赞 && !当前状态.已点赞, 需要收藏 && !当前状态.已收藏);
                    
                    let 状态消息 = "";
                    if (需要点赞) 状态消息 += (当前状态.已点赞 ? "已点赞" : (操作结果.已完成点赞 ? "点赞成功" : "点赞失败"));
                    if (需要收藏) 状态消息 += (当前状态.已收藏 ? (状态消息 ? ", 已收藏" : "已收藏") : (操作结果.已完成收藏 ? (状态消息 ? ", 收藏成功" : "收藏成功") : (状态消息 ? ", 收藏失败" : "收藏失败")));
                    toast(状态消息);
                } else {
                    console.log("所有需要的操作都已完成，无需再次操作");
                    toast("所有操作已完成");
                }
            }
            
            // 返回主界面，准备处理下一个链接
            返回主界面();
            
            // 等待指定时间
            console.log(`等待${操作间隔}秒后处理下一条链接`);
            等待(操作间隔 * 1000);
            
        } catch (e) {
            console.error("处理链接时出错: " + e);
            等待(2000);
        }
    }
    
    toast("全部操作已完成");
}

/**
 * 读取链接文件
 * @param {string} 完整路径 - 文件完整路径
 * @returns {Array|null} 链接数组或null
 */
function 读取链接文件(完整路径) {
    console.log("尝试读取链接文件: " + 完整路径);
    
    try {
        // 检查文件是否存在
        if (!files.exists(完整路径)) {
            console.error("文件不存在: " + 完整路径);
            toast("链接文件不存在: " + 完整路径);
            return null;
        }
        
        // 读取文件内容
        let 文件内容 = files.read(完整路径);
        if (!文件内容 || 文件内容.trim() === "") {
            console.error("文件为空: " + 完整路径);
            toast("链接文件为空");
            return null;
        }
        
        // 按行分割并过滤空行
        let 链接列表 = 文件内容.split("\n")
            .map(行 => 行.trim())
            .filter(行 => 行 && 行.length > 0);
        
        console.log("成功读取 " + 链接列表.length + " 条链接");
        return 链接列表;
    } catch (e) {
        console.error("读取链接文件失败: " + e);
        toast("读取链接文件失败: " + e);
        return null;
    }
}

/**
 * 打开链接并处理弹窗
 * @param {string} 链接 - 要打开的链接
 */
function 打开链接(链接) {
    try {
        // 使用浏览器打开链接
        app.openUrl(链接);
        console.log("已打开链接: " + 链接);
        sleep(3000); // 等待页面加载
        
        // 处理可能的弹窗，直到进入小红书
        处理打开流程();
    } catch (e) {
        console.error("打开链接失败: " + e);
    }
}

/**
 * 处理从浏览器打开到小红书的全流程
 */
function 处理打开流程() {
    // 设置最大尝试次数
    let 最大尝试次数 = 20;
    let 当前尝试次数 = 0;
    
    console.log("开始处理打开流程，最大尝试次数: " + 最大尝试次数);
    
    while (当前尝试次数 < 最大尝试次数) {
        console.log("当前尝试次数: " + (当前尝试次数 + 1));
        
        if (操作模式 === 1) {
            // 无障碍模式
            // 获取当前所有文本元素
            let 所有文本 = textMatches(".*").find();
            let 文本数组 = [];
            
            // 收集所有文本及其位置
            所有文本.forEach(function(元素) {
                if (元素.text() && 元素.text().trim() !== "") {
                    文本数组.push({
                        文本: 元素.text().trim(), // 去除首尾空格，确保精准匹配
                        bounds: 元素.bounds()
                    });
                }
            });

            // 定义需要点击的关键词 - 精准匹配用
            let 关键词列表 = [
                "始终", "Chrome", "确定", "展开", "打开 APP 查看",  "App内打开", "打开", "继续",
                "在不登录账号的情况下使用", "同意", "知道了",
                "浏览", "允许", "确认", "继续访问", "我同意",
                // 添加更多可能的精准按钮文本
                "打开方式", "选择浏览器", "使用浏览器打开", "使用Chrome打开",
                "仅本次", "总是", "取消", "是", "否"
            ];
            
            let 已点击 = false;
            
            // 检查每个关键词
            for (let i = 0; i < 关键词列表.length; i++) {
                let 关键词 = 关键词列表[i];
                
                // 寻找精准匹配关键词的文本
                for (let j = 0; j < 文本数组.length; j++) {
                    let 项目 = 文本数组[j];
                    
                    // 改为精准匹配
                    if (项目.文本 === 关键词) {
                        console.log("找到精准匹配关键词: " + 关键词);
                        
                        // 获取元素中心坐标
                        let x = 项目.bounds.centerX();
                        let y = 项目.bounds.centerY();
                        
                        // 点击该位置
                        点击(x, y);
                        console.log("点击坐标: " + x + ", " + y);
                        
                        已点击 = true;
                        等待(1000); // 等待点击后的反应
                        if(关键词 === "打开 APP 查看" || 关键词 === "App内打开"){
                            等待(5000);
                        }
                        break;
                    }
                }
                
                if (已点击) {
                    break;
                }
            }
        } else {
            // Root模式 - 使用XML解析查找关键词
            let xmlContent = 获取界面XML();
            if (!xmlContent) {
                当前尝试次数++;
                等待(1000);
                continue;
            }
            
            let 元素列表 = 提取文本元素(xmlContent);
            
            // 定义需要点击的关键词 - 精准匹配用
            let 关键词列表 = [
                "始终", "Chrome", "确定", "展开", "打开 APP 查看",  "App内打开", "打开", "继续",
                "在不登录账号的情况下使用", "同意", "知道了",
                "浏览", "允许", "确认", "继续访问", "我同意",
                // 添加更多可能的精准按钮文本
                "打开方式", "选择浏览器", "使用浏览器打开", "使用Chrome打开",
                "仅本次", "总是", "取消", "是", "否"
            ];
            
            let 已点击 = false;
            
            // 检查每个关键词
            for (let i = 0; i < 关键词列表.length; i++) {
                let 关键词 = 关键词列表[i];
                
                // 寻找精准匹配关键词的文本
                for (let j = 0; j < 元素列表.length; j++) {
                    let 元素 = 元素列表[j];
                    
                    // 精准匹配
                    if (元素.文本 === 关键词) {
                        console.log("Root模式找到精准匹配关键词: " + 关键词);
                        
                        // 点击该位置
                        点击(元素.坐标.中心X, 元素.坐标.中心Y);
                        console.log("点击坐标: " + 元素.坐标.中心X + ", " + 元素.坐标.中心Y);
                        
                        已点击 = true;
                        等待(1000); // 等待点击后的反应
                        if(关键词 === "打开 APP 查看" || 关键词 === "App内打开"){
                            等待(5000);
                        }
                        break;
                    }
                }
                
                if (已点击) {
                    break;
                }
            }
        }
        
        // 检查是否已经进入小红书应用
        if (检查是否进入小红书()) {
            console.log("已成功进入小红书");
            return true;
        } else {
            console.log("尚未进入小红书，继续尝试");
        }
        
        // 如果没有找到任何可点击的元素，增加尝试计数
        当前尝试次数++;
        console.log("本次尝试未找到可点击元素或点击未生效，尝试次数增加到: " + 当前尝试次数);
        等待(1000);
    }
    
    console.log("达到最大尝试次数，未能成功进入小红书");
    return false;
}

/**
 * 检查是否已进入小红书
 * @returns {boolean} 是否在小红书中
 */
function 检查是否进入小红书() {
    if (操作模式 === 1) {
        // 无障碍模式 - 使用包名检查当前应用是否为小红书
        // 小红书的包名是 com.xingin.xhs
        return currentPackage() === "com.xingin.xhs" || 
               // 备用检查方法，防止包名获取失败
               getPackageName("小红书") === currentPackage();
    } else {
        // Root模式 - 使用执行Root命令函数获取当前应用包名
        let result = 执行Root命令('dumpsys window | grep mCurrentFocus');
        if (result.code === 0) {
            let match = result.result.match(/mCurrentFocus.+?{.+?(\S+)\/(\S+)}/);
            if (match && match[1]) {
                let 当前包名 = match[1];
                console.log("当前应用包名: " + 当前包名);
                return 当前包名 === "com.xingin.xhs";
            }
        }
        
        // 如果上述方法失败，尝试使用其他方法
        console.log("无法通过dumpsys获取当前应用包名，尝试其他方法");
        
        // 尝试通过界面特征判断
        let xmlContent = 获取界面XML();
        if (xmlContent) {
            // 如果XML中包含小红书特有的元素或文本，可能是在小红书中
            return xmlContent.includes("com.xingin.xhs") || 
                   xmlContent.includes("小红书") || 
                   xmlContent.includes("推荐") && xmlContent.includes("关注");
        }
        
        return false;
    }
}


//====================================


/**
 * 从文本中提取数字
 * @param {string} text - 包含数字的文本
 * @returns {number|null} - 提取的数字，失败返回null
 */
function 提取数字(text) {
    if (!text) return null;
    
    try {
        // 先移除所有空格，确保能处理"点赞 1445"这种格式
        let cleanText = text.replace(/\s+/g, "");
        
        // 匹配数字部分（包括小数和千分位）
        let 数字匹配 = cleanText.match(/\d+(\.\d+)?/);
        if (数字匹配) {
            // 转换为数字
            let 提取结果 = parseFloat(数字匹配[0]);
            return 提取结果;
        }
        
        // 如果是纯数字文本，直接解析
        if (/^\d+$/.test(text)) {
            let 提取结果 = parseInt(text);
            return 提取结果;
        }
        
        return null;
    } catch (e) {
        return null;
    }
}


/**
 * 判断是否为视频页面（Root模式，基于test.js的精准逻辑）
 * @returns {boolean} - 是否为视频页面
 */
function 判断是否为视频页面() {
    try {
        let 屏幕宽度 = device.width;
        let 屏幕高度 = device.height;
        let xmlContent = 获取界面XML();
        if (!xmlContent) return false;
        let 元素列表 = 提取文本元素(xmlContent);

        // 1. 顶部用户名（图文）
        let 顶部用户名 = null;
        for (let 元素 of 元素列表) {
            if (
                元素.坐标.中心Y < 屏幕高度 * 0.3 &&
                元素.文本.length >= 2 && 元素.文本.length <= 12 &&
                !/^\d+$/.test(元素.文本) &&
                !["关注", "说点什么...", "评论", "发弹幕", "分享"].some(k => 元素.文本.includes(k))
            ) {
                顶部用户名 = 元素;
                break;
            }
        }
        // 有顶部用户名就是图文
        if (顶部用户名) return false;

        // 2. 找"关注"或"已关注"按钮
        let 关注按钮 = 元素列表.find(e =>
            e.坐标.中心Y > 屏幕高度 * 0.5 &&
            (e.文本 === "关注" || e.文本 === "已关注")
        );
        // 3. 昵称候选
        let 昵称候选 = 元素列表.filter(e =>
            e.坐标.中心Y > 屏幕高度 * 0.5 &&
            e.文本.length >= 2 && e.文本.length <= 12 &&
            !/^[\d.]+万?$/.test(e.文本) &&
            !["关注", "说点什么...", "评论", "发弹幕", "分享", "相关搜索", "课程咨询"].some(k => e.文本.includes(k)) &&
            !e.文本.includes(":")
        );
        let 底部用户名 = null;
        if (昵称候选.length > 0) {
            if (关注按钮) {
                let 左侧候选 = 昵称候选
                    .filter(e => e.坐标.中心X < 关注按钮.坐标.中心X)
                    .sort((a, b) => {
                        let dyA = Math.abs(a.坐标.中心Y - 关注按钮.坐标.中心Y);
                        let dyB = Math.abs(b.坐标.中心Y - 关注按钮.坐标.中心Y);
                        if (dyA !== dyB) return dyA - dyB;
                        return 关注按钮.坐标.中心X - a.坐标.中心X - (关注按钮.坐标.中心X - b.坐标.中心X);
                    });
                if (左侧候选.length > 0) {
                    底部用户名 = 左侧候选[0];
                } else {
                    昵称候选.sort((a, b) => {
                        if (b.坐标.中心Y !== a.坐标.中心Y) {
                            return b.坐标.中心Y - a.坐标.中心Y;
                        }
                        return a.坐标.中心X - b.坐标.中心X;
                    });
                    底部用户名 = 昵称候选[0];
                }
            } else {
                昵称候选.sort((a, b) => {
                    if (b.坐标.中心Y !== a.坐标.中心Y) {
                        return b.坐标.中心Y - a.坐标.中心Y;
                    }
                    return a.坐标.中心X - b.坐标.中心X;
                });
                底部用户名 = 昵称候选[0];
            }
        }
        // 没有顶部用户名，且有底部昵称，判定为视频
        return !!底部用户名;
    } catch (e) {
        console.error("判断是否为视频页面出错: " + e.message);
        return false;
    }
}


/**
 * 获取互动元素（点赞、收藏、评论）
 * @returns {Object|null} - 包含点赞、收藏、评论信息的对象，获取失败返回null
 */
function 获取互动元素() {
    try {
        // 初始化返回结果
        let 结果 = {
            点赞: null,
            收藏: null,
            评论: null,
            点赞元素: null,
            收藏元素: null,
            评论元素: null,
            是否视频: false
        };
        
        // 获取屏幕尺寸
        let 屏幕宽度 = device.width;
        let 屏幕高度 = device.height;
        
        // 首先判断是否为视频页面
        let 是否视频页面 = 判断是否为视频页面();
        结果.是否视频 = 是否视频页面;
        
        if (操作模式 === 1) {
            // 无障碍模式
            if (是否视频页面) {
                // 视频页面：互动元素可能在右侧，也可能在底部，需要全面搜索
                console.log("视频页面：搜索互动元素");
                
                // 查找点赞元素 - 不限制区域，全屏搜索
                let 点赞元素 = className("android.widget.Button")
                    .descMatches(".*点赞.*")
                    .findOne(500);
                
                if (点赞元素) {
                    结果.点赞元素 = 点赞元素;
                    let desc = 点赞元素.desc();
                    结果.点赞 = 提取数字(desc);
                    console.log("找到视频点赞元素: " + desc);
                } else {
                    console.log("未找到视频点赞元素");
                }
                
                // 查找收藏元素 - 不限制区域，全屏搜索
                let 收藏元素 = className("android.widget.Button")
                    .descMatches(".*收藏.*")
                    .findOne(500);
                
                if (收藏元素) {
                    结果.收藏元素 = 收藏元素;
                    let desc = 收藏元素.desc();
                    结果.收藏 = 提取数字(desc);
                    console.log("找到视频收藏元素: " + desc);
                } else {
                    console.log("未找到视频收藏元素");
                }
            } else {
                // 图文页面：互动元素通常在底部
                let 图文互动区域 = {
                    left: 0,
                    top: Math.floor(屏幕高度 * 0.7),  // 屏幕下方30%区域
                    right: 屏幕宽度,
                    bottom: 屏幕高度
                };
                
                console.log("图文页面：搜索底部互动元素");
                
                // 查找点赞元素
                let 点赞元素 = className("android.widget.Button")
                    .boundsInside(图文互动区域.left, 图文互动区域.top, 图文互动区域.right, 图文互动区域.bottom)
                    .descMatches(".*点赞.*")
                    .findOne(500);
                
                if (点赞元素) {
                    结果.点赞元素 = 点赞元素;
                    let desc = 点赞元素.desc();
                    结果.点赞 = 提取数字(desc);
                    console.log("找到图文点赞元素: " + desc);
                } else {
                    console.log("未找到图文点赞元素");
                }
                
                // 查找收藏元素
                let 收藏元素 = className("android.widget.Button")
                    .boundsInside(图文互动区域.left, 图文互动区域.top, 图文互动区域.right, 图文互动区域.bottom)
                    .descMatches(".*收藏.*")
                    .findOne(500);
                
                if (收藏元素) {
                    结果.收藏元素 = 收藏元素;
                    let desc = 收藏元素.desc();
                    结果.收藏 = 提取数字(desc);
                    console.log("找到图文收藏元素: " + desc);
                } else {
                    console.log("未找到图文收藏元素");
                }
            }
            
            // 如果在指定区域没有找到互动元素，尝试扩大搜索范围
            if (!结果.点赞元素 && !结果.收藏元素) {
                console.log("在特定区域未找到互动元素，尝试全屏搜索");
                
                // 尝试在整个屏幕查找
                let 点赞元素 = className("android.widget.Button").descMatches(".*点赞.*").findOne(500);
                let 收藏元素 = className("android.widget.Button").descMatches(".*收藏.*").findOne(500);
                
                if (点赞元素) {
                    结果.点赞元素 = 点赞元素;
                    结果.点赞 = 提取数字(点赞元素.desc());
                    console.log("全屏搜索找到点赞元素: " + 点赞元素.desc());
                }
                
                if (收藏元素) {
                    结果.收藏元素 = 收藏元素;
                    结果.收藏 = 提取数字(收藏元素.desc());
                    console.log("全屏搜索找到收藏元素: " + 收藏元素.desc());
                }
            }
        } else {
            // Root模式 - 使用XML解析查找互动元素
            let xmlContent = 获取界面XML();
            if (!xmlContent) {
                console.log("获取界面XML失败，无法查找互动元素");
                return null;
            }
            let 元素列表 = 提取文本元素(xmlContent);
            // ==== 新增精准提取规则（参考test.js）====
            let 顶部用户名 = null;
            let 屏幕高度 = device.height;
            let 屏幕宽度 = device.width;
            for (let 元素 of 元素列表) {
                if (
                    元素.坐标.中心Y < 屏幕高度 * 0.3 &&
                    元素.文本.length >= 2 && 元素.文本.length <= 12 &&
                    !/^\d+$/.test(元素.文本) &&
                    !["关注", "说点什么...", "评论", "发弹幕", "分享"].some(k => 元素.文本.includes(k))
                ) {
                    顶部用户名 = 元素;
                    break;
                }
            }
            let 底部用户名 = null;
            let 点赞 = null, 收藏 = null, 评论 = null;
            if (!顶部用户名) {
                // 视频页底部昵称和互动数
                let 关注按钮 = 元素列表.find(e =>
                    e.坐标.中心Y > 屏幕高度 * 0.5 &&
                    (e.文本 === "关注" || e.文本 === "已关注")
                );
                let 昵称候选 = 元素列表.filter(e =>
                    e.坐标.中心Y > 屏幕高度 * 0.5 &&
                    e.文本.length >= 2 && e.文本.length <= 12 &&
                    !/^[\d.]+万?$/.test(e.文本) &&
                    !["关注", "说点什么...", "评论", "发弹幕", "分享", "相关搜索", "课程咨询"].some(k => e.文本.includes(k)) &&
                    !e.文本.includes(":")
                );
                if (昵称候选.length > 0) {
                    if (关注按钮) {
                        let 左侧候选 = 昵称候选
                            .filter(e => e.坐标.中心X < 关注按钮.坐标.中心X)
                            .sort((a, b) => {
                                let dyA = Math.abs(a.坐标.中心Y - 关注按钮.坐标.中心Y);
                                let dyB = Math.abs(b.坐标.中心Y - 关注按钮.坐标.中心Y);
                                if (dyA !== dyB) return dyA - dyB;
                                return 关注按钮.坐标.中心X - a.坐标.中心X - (关注按钮.坐标.中心X - b.坐标.中心X);
                            });
                        if (左侧候选.length > 0) {
                            底部用户名 = 左侧候选[0];
                        } else {
                            昵称候选.sort((a, b) => {
                                if (b.坐标.中心Y !== a.坐标.中心Y) {
                                    return b.坐标.中心Y - a.坐标.中心Y;
                                }
                                return a.坐标.中心X - b.坐标.中心X;
                            });
                            底部用户名 = 昵称候选[0];
                        }
                    } else {
                        昵称候选.sort((a, b) => {
                            if (b.坐标.中心Y !== a.坐标.中心Y) {
                                return b.坐标.中心Y - a.坐标.中心Y;
                            }
                            return a.坐标.中心X - b.坐标.中心X;
                        });
                        底部用户名 = 昵称候选[0];
                    }
                }
                // 互动数字
                let 底部元素 = 元素列表.filter(e => e.坐标.中心Y > 屏幕高度 * 0.7);
                let 互动数字 = 底部元素.filter(e =>
                    /^[\d.]+万?$/.test(e.文本)
                );
                if (底部用户名) {
                    互动数字 = 互动数字.filter(e => e.文本 !== 底部用户名.文本);
                }
                互动数字.sort((a, b) => a.坐标.中心X - b.坐标.中心X);
                点赞 = 互动数字[0] || null;
                收藏 = 互动数字[1] || null;
                评论 = 互动数字[2] || null;
            } else {
                // 图文页底部数字（说点什么...右侧依次为点赞/收藏/评论）
                let 说点什么 = 元素列表.find(e =>
                    e.文本 === "说点什么..." && e.坐标.中心Y > 屏幕高度 * 0.8
                );
                let 分界X = 说点什么 ? 说点什么.坐标.中心X : 0;
                let 底部数字 = 元素列表.filter(e =>
                    e.坐标.中心Y > 屏幕高度 * 0.8 &&
                    (/^[\d.]+万$/.test(e.文本) || /^\d+$/.test(e.文本) || ["点赞", "收藏", "评论", "赞"].includes(e.文本)) &&
                    e.坐标.中心X > 分界X
                );
                底部数字.sort((a, b) => a.坐标.中心X - b.坐标.中心X);
                点赞 = 底部数字[0] || null;
                收藏 = 底部数字[1] || null;
                评论 = 底部数字[2] || null;
                if (点赞 && (点赞.文本 === "点赞" || 点赞.文本 === "赞")) 点赞.文本 = "0";
                if (收藏 && 收藏.文本 === "收藏") 收藏.文本 = "0";
                if (评论 && 评论.文本 === "评论") 评论.文本 = "0";
            }
            // 结果赋值
            if (点赞) {
                结果.点赞 = /^[\d.]+万$/.test(点赞.文本) ? Math.round(parseFloat(点赞.文本) * 10000) : parseInt(点赞.文本) || 0;
                结果.点赞元素 = {
                    text: 点赞.文本,
                    bounds: 点赞.坐标,
                    desc: function() { return 点赞.文本; },
                    click: function() { return 点击(点赞.坐标.中心X, 点赞.坐标.中心Y); }
                };
            }
            if (收藏) {
                结果.收藏 = /^[\d.]+万$/.test(收藏.文本) ? Math.round(parseFloat(收藏.文本) * 10000) : parseInt(收藏.文本) || 0;
                结果.收藏元素 = {
                    text: 收藏.文本,
                    bounds: 收藏.坐标,
                    desc: function() { return 收藏.文本; },
                    click: function() { return 点击(收藏.坐标.中心X, 收藏.坐标.中心Y); }
                };
            }
            if (评论) {
                结果.评论 = /^[\d.]+万$/.test(评论.文本) ? Math.round(parseFloat(评论.文本) * 10000) : parseInt(评论.文本) || 0;
                结果.评论元素 = {
                    text: 评论.文本,
                    bounds: 评论.坐标,
                    desc: function() { return 评论.文本; },
                    click: function() { return 点击(评论.坐标.中心X, 评论.坐标.中心Y); }
                };
            }
            return 结果;
        }
        
        // 检查是否找到任何互动元素
        if (结果.点赞元素 !== null || 结果.收藏元素 !== null) {
            return 结果;
        }
        
        console.log("未找到任何互动元素");
        return null;
    } catch (e) {
        console.error("获取互动元素出错: " + e.message);
        return null;
    }
}


/**
 * 获取作者信息
 * @returns {string|null} - 作者名称，获取失败返回null
 */
function 获取作者信息() {
    try {
        // 查找包含"作者"的Button元素
        let 作者元素 = className("android.widget.Button").descMatches(".*作者.*").findOne(300);
        
        if (作者元素) {
            let desc = 作者元素.desc();
            
            // 从描述中提取作者名称
            let 作者名称匹配 = desc.match(/作者,(.+)/);
            if (作者名称匹配 && 作者名称匹配[1]) {
                let 作者名称 = 作者名称匹配[1].trim();
                return 作者名称;
            }
        }
        
        // 尝试其他方式查找作者信息
        // 查找包含@的文本
        let 包含艾特的文本 = textMatches("@.*").findOne(300);
        if (包含艾特的文本) {
            return 包含艾特的文本.text();
        }
        
        // 查找可能的作者名称（短文本，通常在页面上方）
        let 屏幕高度 = device.height;
        let 可能的作者元素列表 = className("android.widget.TextView")
            .boundsInside(0, 0, device.width, Math.floor(屏幕高度 * 0.3))
            .find();
        
        // 筛选可能的作者名称
        for (let i = 0; i < 可能的作者元素列表.length; i++) {
            let 元素 = 可能的作者元素列表[i];
            let 文本 = 元素.text();
            
            // 作者名称通常较短，且不包含特殊字符
            if (文本 && 文本.length > 0 && 文本.length < 20 && 
                !文本.includes("点赞") && !文本.includes("收藏") && 
                !文本.includes("评论") && !文本.match(/^\d+$/)) {
                return 文本;
            }
        }
        
        return null;
    } catch (e) {
        return null;
    }
}


/**
 * 获取页面文本内容
 * @returns {Array} - 包含文本内容、长度和元素的数组，按长度降序排序
 */
function 获取页面文本内容() {
    try {
        // 获取屏幕尺寸
        let 屏幕宽度 = device.width;
        let 屏幕高度 = device.height;
        
        // 获取页面所有文本元素
        let 所有文本元素 = className("android.widget.TextView")
            .boundsInside(0, 0, 屏幕宽度, 屏幕高度)
            .find();
        
        // 创建一个数组存储所有有效文本及其长度
        let 有效文本列表 = [];
        
        // 要过滤的关键词
        const 过滤关键词 = ["点赞", "收藏", "评论", "小红书", "分享", "复制链接", "举报"];
        
        for (let i = 0; i < 所有文本元素.length; i++) {
            let 元素 = 所有文本元素[i];
            let 文本 = 元素.text();
            
            // 跳过空文本或无效文本
            if (!文本 || 文本.length < 2) continue;
            
            // 跳过纯数字文本
            if (文本.match(/^\d+$/)) continue;
            
            // 跳过包含特定关键词的文本
            let 包含关键词 = false;
            for (let j = 0; j < 过滤关键词.length; j++) {
                if (文本.includes(过滤关键词[j])) {
                    包含关键词 = true;
                    break;
                }
            }
            if (包含关键词) continue;
            
            // 获取元素位置
            let 元素位置 = 元素.bounds();
            
            // 检查元素是否在屏幕可见区域内
            let 是否在屏幕内 = 元素位置.top >= 0 && 元素位置.bottom <= 屏幕高度;
            if (!是否在屏幕内) continue;
            
            // 将有效文本添加到列表
            有效文本列表.push({
                文本: 文本,
                长度: 文本.length,
                元素: 元素
            });
        }
        
        // 按文本长度降序排序
        有效文本列表.sort((a, b) => b.长度 - a.长度);
        
        return 有效文本列表;
    } catch (e) {
        return [];
    }
}


/**
 * 获取页面信息
 * @returns {Object} - 包含页面信息的对象
 */
function 获取页面信息() {
    try {
        // 初始化结果对象
        let 结果 = {
            标题: null,
            内容: null,
            用户名: null,
            点赞数: null,
            收藏数: null,
            评论数: null,
            是否视频: false
        };
        
        // 获取互动信息（点赞、收藏、评论）
        let 互动信息 = 获取互动元素();
        if (互动信息) {
            结果.点赞数 = 互动信息.点赞;
            结果.收藏数 = 互动信息.收藏;
            结果.评论数 = 互动信息.评论;
            结果.是否视频 = 互动信息.是否视频;
        } else {
            // 如果获取互动信息失败，尝试直接判断是否为视频页面
            结果.是否视频 = 判断是否为视频页面();
        }
        
        if (操作模式 === 1) {
            // 无障碍模式
            // 获取用户名/作者信息
            结果.用户名 = 获取作者信息();
            
            // 获取页面文本内容
            let 有效文本列表 = 获取页面文本内容();
            
            // 处理视频页面的特殊情况
            if (结果.是否视频) {
                // 对于视频页面，用户名通常是最短的有效文本之一
                if (有效文本列表.length > 0) {
                    // 如果还没有获取到用户名，使用最短的有效文本
                    if (!结果.用户名 && 有效文本列表.length > 0) {
                        let 最短文本 = 有效文本列表[有效文本列表.length - 1].文本;
                        结果.用户名 = 最短文本;
                    }
                    
                    // 视频标题可能是最长的文本
                    if (有效文本列表.length >= 1) {
                        let 可能的标题 = 有效文本列表[0].文本;
                        结果.标题 = 可能的标题;
                        
                        // 视频内容使用最长文本，但限制在50个字符以内
                        if (可能的标题.length > 50) {
                            结果.内容 = 可能的标题.substring(0, 50) + "...";
                        } else {
                            结果.内容 = 可能的标题;
                        }
                    }
                }
            } else {
                // 非视频页面（图文）的处理逻辑
                
                // 标题通常是较长的文本，但不是最长的
                if (有效文本列表.length > 0) {
                    // 使用最长的文本作为内容
                    let 最长文本 = 有效文本列表[0].文本;
                    结果.内容 = 最长文本.length > 50 ? 最长文本.substring(0, 50) + "..." : 最长文本;
                    
                    // 如果有第二长的文本，使用它作为标题
                    if (有效文本列表.length > 1) {
                        结果.标题 = 有效文本列表[1].文本;
                    }
                }
            }
        } else {
            // Root模式 - 使用XML解析获取页面信息
            let xmlContent = 获取界面XML();
            if (!xmlContent) {
                console.log("获取界面XML失败，无法获取页面信息");
                return 结果;
            }
            
            let 元素列表 = 提取文本元素(xmlContent);
            
            // 按文本长度排序，最长的可能是内容
            元素列表.sort((a, b) => b.文本.length - a.文本.length);
            
            // 过滤掉可能的数字元素（点赞、收藏数等）
            let 有效文本列表 = 元素列表.filter(元素 => {
                // 如果文本是纯数字，跳过
                if (/^\d+$/.test(元素.文本)) return false;
                
                // 如果文本包含特定关键词，跳过
                const 过滤关键词 = ["点赞", "收藏", "评论", "小红书", "分享", "复制链接", "举报"];
                for (let i = 0; i < 过滤关键词.length; i++) {
                    if (元素.文本.includes(过滤关键词[i])) return false;
                }
                
                return true;
            });
            
            // 处理视频页面的特殊情况
            if (结果.是否视频) {
                // 对于视频页面，用户名通常位于屏幕上方
                for (let i = 0; i < 有效文本列表.length; i++) {
                    let 元素 = 有效文本列表[i];
                    // 用户名通常较短且在屏幕上方
                    if (元素.文本.length < 20 && 元素.坐标.中心Y < device.height * 0.3) {
                        结果.用户名 = 元素.文本;
                        break;
                    }
                }
                
                // 视频标题和内容可能是较长的文本
                if (有效文本列表.length > 0) {
                    let 可能的标题 = 有效文本列表[0].文本;
                    结果.标题 = 可能的标题;
                    
                    // 视频内容使用最长文本，但限制在50个字符以内
                    if (可能的标题.length > 50) {
                        结果.内容 = 可能的标题.substring(0, 50) + "...";
                    } else {
                        结果.内容 = 可能的标题;
                    }
                }
            } else {
                // 非视频页面（图文）的处理逻辑
                
                // 查找可能的用户名（通常在屏幕上方）
                for (let i = 0; i < 有效文本列表.length; i++) {
                    let 元素 = 有效文本列表[i];
                    // 用户名通常较短且在屏幕上方
                    if (元素.文本.length < 20 && 元素.坐标.中心Y < device.height * 0.3) {
                        结果.用户名 = 元素.文本;
                        break;
                    }
                }
                
                // 标题和内容处理
                if (有效文本列表.length > 0) {
                    // 使用最长的文本作为内容
                    let 最长文本 = 有效文本列表[0].文本;
                    结果.内容 = 最长文本.length > 50 ? 最长文本.substring(0, 50) + "..." : 最长文本;
                    
                    // 如果有第二长的文本，使用它作为标题
                    if (有效文本列表.length > 1) {
                        结果.标题 = 有效文本列表[1].文本;
                    }
                }
            }
        }
        
        // 简化输出，只返回需要的信息
        return {
            标题: 结果.标题,
            内容: 结果.内容,
            用户名: 结果.用户名,
            点赞数: 结果.点赞数,
            收藏数: 结果.收藏数,
            是否视频: 结果.是否视频
        };
    } catch (e) {
        return {
            标题: null,
            内容: null,
            用户名: null,
            点赞数: null,
            收藏数: null,
            是否视频: false,
            error: e.message
        };
    }
}


/**
 * 比较两个页面信息，判断是否为同一篇文章
 * @param {Object} 信息1 - 第一个页面信息
 * @param {Object} 信息2 - 第二个页面信息
 * @returns {boolean} - 是否为同一篇文章
 */
function 比较页面信息(信息1, 信息2) {
    // 如果任一信息为空，返回false
    if (!信息1 || !信息2) return false;
    
    // 1. 检查作者是否相同
    let 作者相同 = 信息1.用户名 && 信息2.用户名 && 信息1.用户名 === 信息2.用户名;
    
    // 2. 检查内容是否相似（如果内容存在）
    let 内容相似 = false;
    if (信息1.内容 && 信息2.内容) {
        // 如果内容长度超过20个字符，比较前20个字符是否相同
        if (信息1.内容.length > 20 && 信息2.内容.length > 20) {
            内容相似 = 信息1.内容.substring(0, 20) === 信息2.内容.substring(0, 20);
        } else {
            // 否则比较整个内容
            内容相似 = 信息1.内容 === 信息2.内容;
        }
    }
    
    // 3. 检查点赞数和收藏数是否在合理范围内
    let 点赞数合理 = false;
    let 收藏数合理 = false;
    
    if (信息1.点赞数 !== null && 信息2.点赞数 !== null) {
        // 允许点赞数有小幅增长（其他用户可能点赞）或小幅减少（最多减少5个，可能是其他用户取消点赞）
        let 点赞差值 = 信息2.点赞数 - 信息1.点赞数;
        点赞数合理 = 点赞差值 >= -5 && 点赞差值 < 10; // 允许减少最多5个，增加不超过10个
    }
    
    if (信息1.收藏数 !== null && 信息2.收藏数 !== null) {
        // 允许收藏数有小幅增长或小幅减少
        let 收藏差值 = 信息2.收藏数 - 信息1.收藏数;
        收藏数合理 = 收藏差值 >= -5 && 收藏差值 < 10; // 允许减少最多5个，增加不超过10个
    }
    
    // 4. 综合判断是否是同一篇文章
    // 如果作者相同且内容相似，几乎可以确定是同一篇文章
    if (作者相同 && 内容相似) {
        return true;
    }
    
    // 如果作者相同且点赞数和收藏数变化合理，可能是同一篇文章
    if (作者相同 && 点赞数合理 && 收藏数合理) {
        return true;
    }
    
    // 如果内容相似且点赞数和收藏数变化合理，可能是同一篇文章
    if (内容相似 && 点赞数合理 && 收藏数合理) {
        return true;
    }
    
    return false;
}


/**
 * 返回主界面
 * @returns {boolean} - 是否成功返回主界面
 */
function 返回主界面() {
    console.log("返回主界面");
    
    // 首页可能出现的关键词
    const 首页关键词 = ["首页", "发现", "关注", "直播","视频","购物","消息", "短剧", "附近", "热门", "消息", "我", "推荐", "穿搭", "情感"];
    
    // 最多按5次返回键
    for (let i = 0; i < 5; i++) {
        // 检查是否已在主界面
        let 找到的关键词 = 0;
        
        try {
            if (操作模式 === 1) {
                // 无障碍模式
                // 查找所有文本元素，减少超时时间
                let 所有文本 = textMatches(".*").find();
                
                // 检查有多少关键词存在
                for (let j = 0; j < 所有文本.length; j++) {
                    let 文本内容 = 所有文本[j].text();
                    if (!文本内容) continue;
                    
                    // 使用some方法更高效地检查是否包含任何关键词
                    if (首页关键词.some(关键词 => 文本内容.includes(关键词))) {
                        找到的关键词++;
                        
                        // 如果找到5个或以上关键词，认为已在首页
                        if (找到的关键词 >= 5) {
                            console.log(`已找到 ${找到的关键词} 个首页关键词，确认在首页`);
                            return true;
                        }
                    }
                }
            } else {
                // Root模式
                let xmlContent = 获取界面XML();
                if (xmlContent) {
                    let 元素列表 = 提取文本元素(xmlContent);
                    
                    // 检查有多少关键词存在
                    for (let j = 0; j < 元素列表.length; j++) {
                        let 文本内容 = 元素列表[j].文本;
                        if (!文本内容) continue;
                        
                        // 使用some方法检查是否包含任何关键词
                        if (首页关键词.some(关键词 => 文本内容.includes(关键词))) {
                            找到的关键词++;
                            
                            // 如果找到3个或以上关键词，认为已在首页
                            if (找到的关键词 >= 3) {
                                console.log(`Root模式已找到 ${找到的关键词} 个首页关键词，确认在首页`);
                                return true;
                            }
                        }
                    }
                }
            }
        } catch (e) {
            console.error("检查首页元素出错: " + e.message);
        }
        
        // 按返回键
        console.log("按返回键");
        返回();
        等待(1500);
    }
    
    // 最后检查一次
    let 找到的关键词 = 0;
    
    if (操作模式 === 1) {
        // 无障碍模式
        let 所有文本 = textMatches(".*").find();
        for (let j = 0; j < 所有文本.length; j++) {
            let 文本内容 = 所有文本[j].text();
            if (!文本内容) continue;
            
            if (首页关键词.some(关键词 => 文本内容.includes(关键词))) {
                找到的关键词++;
            }
        }
        
        if (找到的关键词 >= 3) {
            console.log(`已找到 ${找到的关键词} 个首页关键词，确认在首页`);
            return true;
        }
    } else {
        // Root模式
        let xmlContent = 获取界面XML();
        if (xmlContent) {
            let 元素列表 = 提取文本元素(xmlContent);
            
            for (let j = 0; j < 元素列表.length; j++) {
                let 文本内容 = 元素列表[j].文本;
                if (!文本内容) continue;
                
                if (首页关键词.some(关键词 => 文本内容.includes(关键词))) {
                    找到的关键词++;
                }
            }
            
            if (找到的关键词 >= 3) {
                console.log(`Root模式已找到 ${找到的关键词} 个首页关键词，确认在首页`);
                return true;
            }
        }
    }
    
    console.log("未能确认返回首页");
    return false;
}


/**
 * 点击首篇文章
 * @returns {boolean} - 是否成功点击
 */
function 点击首篇文章() {
    console.log("点击首页文章");
    
    try {
        // 等待首页加载
        等待(2000);
        
        if (操作模式 === 1) {
            // 无障碍模式 - 尝试查找文章元素
            // 先尝试查找文章容器
            let 文章容器 = className("android.widget.FrameLayout")
                .depth(13)
                .findOne(2000);
            
            if (文章容器) {
                console.log("找到文章容器，点击");
                文章容器.click();
                等待(3000);
                return true;
            } else {
                // 如果找不到特定容器，使用随机坐标点击
                console.log("未找到文章容器，使用随机坐标点击");
                // 随机化点击坐标
                let x = 250 + Math.floor(Math.random() * 40) - 20;  // 250 ± 20
                let y = 500 + Math.floor(Math.random() * 40) - 20;  // 500 ± 20
                
                console.log(`点击坐标: (${x}, ${y})`);
                点击(x, y);
                
                // 等待文章加载
                等待(3000);
                return true;
            }
        } else {
            // Root模式 - 使用坐标点击
            // 随机化点击坐标
            let x = 250 + Math.floor(Math.random() * 40) - 20;  // 250 ± 20
            let y = 500 + Math.floor(Math.random() * 40) - 20;  // 500 ± 20
            
            console.log(`Root模式点击坐标: (${x}, ${y})`);
            点击(x, y);
            
            // 等待文章加载
            等待(3000);
            return true;
        }
    } catch (e) {
        console.error("点击首篇文章出错: " + e.message);
        return false;
    }
}


/**
 * 执行点赞操作
 * @returns {boolean} - 是否成功点赞
 */
function 执行点赞() {
    console.log("执行点赞操作");
    
    try {
        // 获取互动元素
        let 互动元素 = 获取互动元素();
        if (!互动元素 || !互动元素.点赞元素) {
            console.log("未找到点赞元素，尝试通过其他方式查找");
            
            // 判断是否为视频页面
            let 是视频页面 = 判断是否为视频页面();
            
            if (是视频页面) {
                console.log("检测到视频页面，尝试通过坐标点击右侧点赞按钮");
                
                // 视频页面点赞按钮通常在右侧
                let 屏幕宽度 = device.width;
                let 屏幕高度 = device.height;
                
                // 点击右侧中部位置，通常是点赞按钮区域
                let x = Math.floor(屏幕宽度 * 0.9);
                let y = Math.floor(屏幕高度 * 0.4);
                
                console.log("点击视频点赞区域: (" + x + ", " + y + ")");
                点击(x, y);
                等待(1000);
                
                return true;
            } else {
                console.log("检测到图文页面，尝试通过坐标点击底部点赞按钮");
                
                // 图文页面点赞按钮通常在底部
                let 屏幕宽度 = device.width;
                let 屏幕高度 = device.height;
                
                // 点击底部左侧位置，通常是点赞按钮区域
                let x = Math.floor(屏幕宽度 * 0.15);
                let y = Math.floor(屏幕高度 * 0.9);
                
                console.log("点击图文点赞区域: (" + x + ", " + y + ")");
                点击(x, y);
                等待(1000);
                
                return true;
            }
        }
        
        // 检查是否已点赞
        let desc = 互动元素.点赞元素.desc();
        if (desc && desc.includes("已点赞")) {
            console.log("已经点过赞了");
            return true;
        }
        
        // 点击点赞按钮
        console.log("点击点赞按钮");
        
        if (操作模式 === 1) {
            // 无障碍模式直接点击元素
            互动元素.点赞元素.click();
        } else {
            // Root模式使用点击函数
            if (typeof 互动元素.点赞元素.click === 'function') {
                互动元素.点赞元素.click();
            } else {
                // 如果没有自定义点击函数，使用坐标点击
                let bounds = 互动元素.点赞元素.bounds || 互动元素.点赞元素.坐标;
                if (bounds) {
                    let x = bounds.centerX ? bounds.centerX() : bounds.中心X;
                    let y = bounds.centerY ? bounds.centerY() : bounds.中心Y;
                    点击(x, y);
                } else {
                    console.log("无法获取点赞元素坐标");
                    return false;
                }
            }
        }
        
        等待(1000);
        
        // 验证是否点赞成功
        let 验证互动元素 = 获取互动元素();
        if (验证互动元素 && 验证互动元素.点赞元素) {
            let 新desc = 验证互动元素.点赞元素.desc();
            if (新desc && 新desc.includes("已点赞")) {
                console.log("点赞成功，状态已变为已点赞");
                return true;
            } else {
                console.log("点赞可能成功，但未检测到已点赞状态");
            }
        }
        
        return true; // 假设点击成功
    } catch (e) {
        console.error("执行点赞出错: " + e.message);
        return false;
    }
}

/**
 * 执行root命令的通用函数，确保先检查root会话状态
 * @param {string} cmd - 要执行的命令（不含su -c部分）
 * @param {boolean} root - 是否需要root权限执行，默认为true
 * @returns {Object} 包含执行结果的对象
 */
function 执行Root命令(cmd, root = true) {
    // 如果不需要root权限，直接执行命令
    if (!root) {
        return shell(cmd, true);
    }
    
    // 需要root权限时，先检查root会话状态
    if (!root会话) {
        console.log("执行命令前检查root权限: " + cmd);
        if (!初始化Root权限()) {
            console.error("执行命令失败: 无法获取root权限");
            return { code: -1, error: "无法获取root权限", result: "" };
        }
    }
    
    // 使用su -c执行命令
    try {
        // 在命令中添加随机标记，减少重复授权提示
        let randomTag = Math.random().toString(36).substring(2, 10);
        let taggedCmd = cmd + " #" + randomTag;
        
        // 执行命令并返回结果
        return shell("su -c '" + taggedCmd + "'", true);
    } catch (e) {
        console.error("执行root命令出错: " + e);
        
        // 尝试重新初始化root会话
        console.log("尝试重新初始化root会话...");
        root会话 = false;
        
        if (初始化Root权限()) {
            console.log("重新初始化成功，再次尝试执行命令");
            try {
                return shell("su -c '" + cmd + "'", true);
            } catch (e2) {
                console.error("重试执行命令仍然失败: " + e2);
                return { code: -1, error: e2.toString(), result: "" };
            }
        } else {
            return { code: -1, error: "重新初始化root会话失败", result: "" };
        }
    }
}

// 在文件末尾添加以下函数

/**
 * 获取交互按钮位置（基于文本偏移）
 * @param {Array} 文本元素列表 - 文本元素数组
 * @returns {Object} 包含点赞、收藏和评论按钮位置的对象
 */
function 获取交互按钮位置(文本元素列表) {
    console.log("开始获取交互按钮位置（基于文本偏移）");
    
    // 从文本元素中找出点赞、收藏、评论数字
    let 点赞数文本 = null, 收藏数文本 = null, 评论数文本 = null;
    
    // 筛选底部区域的数字文本
    let 底部元素 = 文本元素列表.filter(e => e.坐标.中心Y > device.height * 0.7);
    let 互动数字 = 底部元素.filter(e =>
        /^[\d.]+万?$/.test(e.文本) || e.文本 === "评论"
    );
    
    // 按X坐标排序，一般从左到右是点赞、收藏、评论
    互动数字.sort((a, b) => a.坐标.中心X - b.坐标.中心X);
    点赞数文本 = 互动数字[0] || null;
    收藏数文本 = 互动数字[1] || null;
    评论数文本 = 互动数字[2] || null;
    
    // 根据文本位置计算按钮位置（使用固定偏移）
    let 点赞按钮 = null, 收藏按钮 = null, 评论按钮 = null;
    
    // 使用X偏移-70，Y偏移+25
    let 主偏移 = { X: -70, Y: 25 };
    console.log(`使用偏移值: X=${主偏移.X}, Y=${主偏移.Y}`);
    
    if (点赞数文本) {
        // 计算点赞按钮位置
        let x = 点赞数文本.坐标.左 + 主偏移.X;
        let y = 点赞数文本.坐标.中心Y + 主偏移.Y;
        点赞按钮 = {
            坐标: {
                中心X: x,
                中心Y: y
            }
        };
        console.log(`点赞按钮位置: (${x}, ${y}), 偏移值: X=${主偏移.X}, Y=${主偏移.Y}`);
        console.log(`点赞数文本位置: 左=${点赞数文本.坐标.左}, 中心=${点赞数文本.坐标.中心X},${点赞数文本.坐标.中心Y}, 右=${点赞数文本.坐标.右}`);
    }
    
    if (收藏数文本) {
        // 计算收藏按钮位置
        let x = 收藏数文本.坐标.左 + 主偏移.X;
        let y = 收藏数文本.坐标.中心Y + 主偏移.Y;
        收藏按钮 = {
            坐标: {
                中心X: x,
                中心Y: y
            }
        };
        console.log(`收藏按钮位置: (${x}, ${y}), 偏移值: X=${主偏移.X}, Y=${主偏移.Y}`);
        console.log(`收藏数文本位置: 左=${收藏数文本.坐标.左}, 中心=${收藏数文本.坐标.中心X},${收藏数文本.坐标.中心Y}, 右=${收藏数文本.坐标.右}`);
    }
    
    if (评论数文本) {
        // 计算评论按钮位置
        let x = 评论数文本.坐标.左 + 主偏移.X;
        let y = 评论数文本.坐标.中心Y + 主偏移.Y;
        评论按钮 = {
            坐标: {
                中心X: x,
                中心Y: y
            }
        };
        console.log(`评论按钮位置: (${x}, ${y}), 偏移值: X=${主偏移.X}, Y=${主偏移.Y}`);
        console.log(`评论数文本位置: 左=${评论数文本.坐标.左}, 中心=${评论数文本.坐标.中心X},${评论数文本.坐标.中心Y}, 右=${评论数文本.坐标.右}`);
    }
    
    return {
        点赞按钮,
        收藏按钮,
        评论按钮,
        点赞数文本,
        收藏数文本,
        评论数文本
    };
}

/**
 * 判断按钮状态（通过单点取色获取按钮颜色）
 * @param {Object} 按钮信息 - 包含按钮位置的对象
 * @returns {Object} 包含各按钮状态的对象
 */
function 判断按钮状态(按钮信息) {
    if (!按钮信息) {
        console.log("未提供按钮信息，无法判断状态");
        return null;
    }
    
    if (!requestScreenCapture()) {
        console.log("请求截图权限失败");
        return null;
    }
    
    let img = captureScreen();
    let 屏幕宽度 = img.getWidth();
    let 屏幕高度 = img.getHeight();
    console.log(`当前屏幕分辨率: ${屏幕宽度}x${屏幕高度}`);
    
    // 使用提供的精确颜色
    let 点赞颜色 = "#FF2442"; // 点赞后的红色
    let 收藏颜色 = "#FCBD54"; // 收藏后的黄色
    
    console.log(`目标点赞颜色: ${点赞颜色}`);
    console.log(`目标收藏颜色: ${收藏颜色}`);
    
    let 状态 = {
        已点赞: false,
        已收藏: false
    };
    
    // X偏移范围
    let X偏移范围 = [-60, -65, -70, -75, -80, -85, -90];
    // Y偏移固定为+25
    let Y偏移 = 25;
    
    // 判断点赞按钮状态
    if (按钮信息.点赞数文本) {
        console.log("===== 点赞按钮检测 =====");
        console.log(`点赞数文本位置: 左=${按钮信息.点赞数文本.坐标.左}, 中心Y=${按钮信息.点赞数文本.坐标.中心Y}`);
        console.log(`使用Y偏移: +${Y偏移}`);
        
        // 遍历X偏移范围
        for (let i = 0; i < X偏移范围.length; i++) {
            let X偏移 = X偏移范围[i];
            let x = 按钮信息.点赞数文本.坐标.左 + X偏移;
            let y = 按钮信息.点赞数文本.坐标.中心Y + Y偏移;
            
            // 确保坐标在屏幕范围内
            if (x < 0 || x >= 屏幕宽度 || y < 0 || y >= 屏幕高度) {
                console.log(`坐标(${x}, ${y})超出屏幕范围，跳过`);
                continue;
            }
            
            // 获取颜色
            let color = images.pixel(img, x, y);
            let colorHex = colors.toString(color).toUpperCase();
            let r = colors.red(color), g = colors.green(color), b = colors.blue(color);
            
            // 判断颜色相似度
            let 与点赞颜色相似 = colors.isSimilar(color, 点赞颜色, 30);
            console.log(`X偏移${X偏移}检测: 位置(${x}, ${y}), 颜色: ${colorHex}, RGB(${r},${g},${b}), ${与点赞颜色相似 ? "匹配" : "不匹配"}`);
            
            if (与点赞颜色相似) {
                状态.已点赞 = true;
                console.log(`在X偏移${X偏移}处找到点赞颜色匹配，确认已点赞`);
                break; // 找到匹配颜色，跳出循环
            }
        }
        
        console.log(`点赞状态: ${状态.已点赞 ? "已点赞" : "未点赞"}`);
    }
    
    // 判断收藏按钮状态
    if (按钮信息.收藏数文本) {
        console.log("\n===== 收藏按钮检测 =====");
        console.log(`收藏数文本位置: 左=${按钮信息.收藏数文本.坐标.左}, 中心Y=${按钮信息.收藏数文本.坐标.中心Y}`);
        console.log(`使用Y偏移: +${Y偏移}`);
        
        // 遍历X偏移范围
        for (let i = 0; i < X偏移范围.length; i++) {
            let X偏移 = X偏移范围[i];
            let x = 按钮信息.收藏数文本.坐标.左 + X偏移;
            let y = 按钮信息.收藏数文本.坐标.中心Y + Y偏移;
            
            // 确保坐标在屏幕范围内
            if (x < 0 || x >= 屏幕宽度 || y < 0 || y >= 屏幕高度) {
                console.log(`坐标(${x}, ${y})超出屏幕范围，跳过`);
                continue;
            }
            
            // 获取颜色
            let color = images.pixel(img, x, y);
            let colorHex = colors.toString(color).toUpperCase();
            let r = colors.red(color), g = colors.green(color), b = colors.blue(color);
            
            // 判断颜色相似度
            let 与收藏颜色相似 = colors.isSimilar(color, 收藏颜色, 30);
            console.log(`X偏移${X偏移}检测: 位置(${x}, ${y}), 颜色: ${colorHex}, RGB(${r},${g},${b}), ${与收藏颜色相似 ? "匹配" : "不匹配"}`);
            
            if (与收藏颜色相似) {
                状态.已收藏 = true;
                console.log(`在X偏移${X偏移}处找到收藏颜色匹配，确认已收藏`);
                break; // 找到匹配颜色，跳出循环
            }
        }
        
        console.log(`收藏状态: ${状态.已收藏 ? "已收藏" : "未收藏"}`);
    }
    
    return 状态;
}

/**
 * 检测当前文章点赞和收藏状态
 * @returns {Object} 包含点赞和收藏状态的对象
 */
function 检测文章状态() {
    console.log("检测当前文章状态");
    
    // 获取界面元素
    let xmlContent = 获取界面XML();
    if (!xmlContent) {
        console.log("获取界面XML失败");
        return {
            已点赞: false,
            已收藏: false
        };
    }
    
    let 元素列表 = 提取文本元素(xmlContent);
    let 按钮信息 = 获取交互按钮位置(元素列表);
    
    if (!按钮信息) {
        console.log("未找到交互按钮");
        return {
            已点赞: false,
            已收藏: false
        };
    }
    
    // 判断按钮状态
    let 状态 = 判断按钮状态(按钮信息);
    console.log(`文章状态: 已点赞=${状态.已点赞}, 已收藏=${状态.已收藏}`);
    
    return 状态;
}

/**
 * 执行点赞操作（更新版）
 * @param {boolean} 需要点赞 - 是否需要执行点赞操作
 * @param {boolean} 需要收藏 - 是否需要执行收藏操作
 * @returns {Object} 包含操作结果的对象
 */
function 执行互动操作(需要点赞 = true, 需要收藏 = false) {
    console.log(`执行互动操作: 点赞=${需要点赞}, 收藏=${需要收藏}`);
    
    // 首先检测当前状态
    let 当前状态 = 检测文章状态();
    let 结果 = {
        点赞成功: false,
        收藏成功: false,
        已完成点赞: 当前状态.已点赞,
        已完成收藏: 当前状态.已收藏
    };
    
    // 如果已经点赞且收藏，或者不需要任何操作，直接返回
    if ((当前状态.已点赞 && !需要收藏) || (!需要点赞 && !需要收藏)) {
        console.log("无需执行任何操作，当前状态已满足需求");
        return 结果;
    }
    
    // 获取交互按钮位置
    let xmlContent = 获取界面XML();
    if (!xmlContent) {
        console.log("获取界面XML失败，无法执行互动操作");
        return 结果;
    }
    
    let 元素列表 = 提取文本元素(xmlContent);
    let 按钮信息 = 获取交互按钮位置(元素列表);
    
    if (!按钮信息) {
        console.log("未找到交互按钮位置信息，无法执行互动操作");
        return 结果;
    }
    
    // 执行点赞操作
    if (需要点赞 && !当前状态.已点赞 && 按钮信息.点赞按钮) {
        console.log("执行点赞操作");
        点击(按钮信息.点赞按钮.坐标.中心X, 按钮信息.点赞按钮.坐标.中心Y);
        等待(1000);
        结果.点赞成功 = true;
    } else if (需要点赞) {
        console.log("已经点过赞或无法找到点赞按钮");
        结果.已完成点赞 = 当前状态.已点赞;
    }
    
    // 执行收藏操作
    if (需要收藏 && !当前状态.已收藏 && 按钮信息.收藏按钮) {
        console.log("执行收藏操作");
        点击(按钮信息.收藏按钮.坐标.中心X, 按钮信息.收藏按钮.坐标.中心Y);
        等待(1000);
        结果.收藏成功 = true;
    } else if (需要收藏) {
        console.log("已经收藏过或无法找到收藏按钮");
        结果.已完成收藏 = 当前状态.已收藏;
    }
    
    // 再次检测状态，确认操作结果
    let 操作后状态 = 检测文章状态();
    结果.已完成点赞 = 操作后状态.已点赞;
    结果.已完成收藏 = 操作后状态.已收藏;
    
    console.log(`互动操作结果: 点赞=${结果.已完成点赞 ? "已完成" : "未完成"}, 收藏=${结果.已完成收藏 ? "已完成" : "未完成"}`);
    return 结果;
}

/**
 * 直接互动链接文章
 * @param {string} 链接 - 文章链接
 * @param {boolean} 需要点赞 - 是否需要点赞
 * @param {boolean} 需要收藏 - 是否需要收藏
 * @returns {Object} 包含操作结果的对象
 */
function 直接互动链接文章(链接, 需要点赞 = true, 需要收藏 = false) {
    console.log(`直接互动链接文章: ${链接}`);
    console.log(`操作选项: 点赞=${需要点赞}, 收藏=${需要收藏}`);
    
    // 强制退出小红书
    强制退出小红书();
    等待(1000);
    
    // 打开链接
    app.openUrl(链接);
    console.log("已打开链接");
    等待(3000); // 等待页面加载
    
    // 处理打开流程
    if (!处理打开流程()) {
        console.log("处理打开流程失败");
        return {
            成功: false,
            原因: "无法进入小红书"
        };
    }
    
    等待(2000); // 等待页面完全加载
    
    // 检测当前状态
    let 当前状态 = 检测文章状态();
    console.log(`当前状态: 已点赞=${当前状态.已点赞}, 已收藏=${当前状态.已收藏}`);
    
    // 判断是否需要继续执行
    if ((需要点赞 && !需要收藏 && 当前状态.已点赞) || 
        (!需要点赞 && 需要收藏 && 当前状态.已收藏) || 
        (需要点赞 && 需要收藏 && 当前状态.已点赞 && 当前状态.已收藏)) {
        console.log("所需操作已完成，无需再次操作");
        return {
            成功: true,
            已完成点赞: 当前状态.已点赞,
            已完成收藏: 当前状态.已收藏
        };
    }
    
    // 获取页面信息
    let 页面信息 = 获取页面信息();
    
    // 判断是视频还是图文，进行相应等待或滑动
    if (页面信息.是否视频) {
        // 如果是视频，随机等待5-10秒
        let 视频等待时间 = 5000 + Math.floor(Math.random() * 5000);
        console.log("视频内容，等待 " + (视频等待时间 / 1000) + " 秒");
        等待(视频等待时间);
    } else {
        // 如果是图文，随机上划并延时
        let 图文等待时间 = 1000 + Math.floor(Math.random() * 2000);
        console.log("图文内容，等待 " + (图文等待时间 / 1000) + " 秒");
        等待(图文等待时间);
        
        // 随机上划
        let 屏幕宽度 = device.width;
        let 屏幕高度 = device.height;
        let 起点Y = 屏幕高度 * (0.7 + Math.random() * 0.2);
        let 终点Y = 屏幕高度 * (0.3 + Math.random() * 0.2);
        let X坐标 = 屏幕宽度 * (0.4 + Math.random() * 0.2);
        let 滑动时间 = 300 + Math.floor(Math.random() * 500);
        滑动(X坐标, 起点Y, X坐标, 终点Y, 滑动时间);
        等待(1000 + Math.floor(Math.random() * 1000));
    }
    
    // 执行互动操作
    let 操作结果 = 执行互动操作(需要点赞 && !当前状态.已点赞, 需要收藏 && !当前状态.已收藏);
    
    return {
        成功: true,
        已完成点赞: 操作结果.已完成点赞 || 当前状态.已点赞,
        已完成收藏: 操作结果.已完成收藏 || 当前状态.已收藏
    };
}

// 替换原有的执行点赞函数
/**
 * 执行点赞操作（兼容旧版本调用）
 * @returns {boolean} - 是否成功点赞
 */
function 执行点赞() {
    console.log("执行点赞操作（旧版本兼容）");
    let 操作结果 = 执行互动操作(true, false);
    return 操作结果.已完成点赞;
}

// 更新执行自动点赞函数
/**
 * 执行自动点赞主功能（更新版）
 * @param {string} 文件路径 - 文件所在路径
 * @param {string} 文件名 - 文件名
 * @param {number} 最大操作数 - 最大操作链接数
 * @param {number} 操作间隔 - 操作间隔时间(秒)
 * @param {boolean} 需要点赞 - 是否需要执行点赞操作
 * @param {boolean} 需要收藏 - 是否需要执行收藏操作
 */
function 执行自动互动(文件路径, 文件名, 最大操作数, 操作间隔, 需要点赞 = true, 需要收藏 = false) {
    // 读取链接文件
    let 完整路径 = 文件路径 + 文件名;
    console.log("准备读取链接文件: " + 完整路径);
    
    let 链接列表 = 读取链接文件(完整路径);
    if (!链接列表 || 链接列表.length === 0) {
        toast("链接文件读取失败或为空: " + 完整路径);
        console.error("链接文件读取失败或为空: " + 完整路径);
        return;
    }
    
    // 限制操作数量
    let 实际操作数 = Math.min(链接列表.length, 最大操作数);
    toast(`共读取到 ${链接列表.length} 条链接，计划处理 ${实际操作数} 条`);
    
    // 循环处理每个链接
    for (let i = 0; i < 实际操作数; i++) {
        try {
            let 链接 = 链接列表[i];
            toast(`处理第 ${i + 1} 条链接: ${链接}`);
            
            // 在打开链接前先强制退出小红书
            强制退出小红书();
            
            // 打开链接并获取页面信息
            app.openUrl(链接);
            console.log("已打开链接: " + 链接);
            等待(3000); // 等待页面加载
            
            // 处理可能的弹窗，直到进入小红书
            if (!处理打开流程()) {
                console.log("处理打开流程失败，无法进入小红书");
                continue;
            }
            
            等待(2000); // 等待页面完全加载
            
            // 首先检测当前状态
            let 当前状态 = 检测文章状态();
            console.log(`链接打开后状态检测: 已点赞=${当前状态.已点赞}, 已收藏=${当前状态.已收藏}`);
            
            // 判断是否需要继续执行
            // 如果只需要点赞且已点赞，或只需要收藏且已收藏，或两者都需要且都已完成，则跳过
            if ((需要点赞 && !需要收藏 && 当前状态.已点赞) || 
                (!需要点赞 && 需要收藏 && 当前状态.已收藏) || 
                (需要点赞 && 需要收藏 && 当前状态.已点赞 && 当前状态.已收藏)) {
                console.log("所需操作已完成，跳过此链接");
                toast("所需操作已完成，跳过此链接");
                等待(1000);
                continue;
            }
            
            // 获取链接页面信息
            let 链接页面信息 = 获取页面信息();
            // 只输出需要的信息
            console.log("链接页面信息: 标题=[" + 链接页面信息.标题 + "], 内容=[" + 链接页面信息.内容 + "], 作者=[" + 链接页面信息.用户名 + "]");
            
            // 如果需要执行操作，且当前已在文章页面，先尝试执行操作
            if ((需要点赞 && !当前状态.已点赞) || (需要收藏 && !当前状态.已收藏)) {
                console.log("直接在当前页面执行互动操作");
                
                // 判断是视频还是图文，进行相应等待或滑动
                if (链接页面信息.是否视频) {
                    // 如果是视频，随机等待5-10秒
                    let 视频等待时间 = 5000 + Math.floor(Math.random() * 5000); // 5000-10000毫秒
                    console.log("视频内容，等待 " + (视频等待时间 / 1000) + " 秒");
                    等待(视频等待时间);
                } else {
                    // 如果是图文，随机上划并延时
                    // 随机延时1-3秒
                    let 图文等待时间 = 1000 + Math.floor(Math.random() * 2000); // 1000-3000毫秒
                    console.log("图文内容，等待 " + (图文等待时间 / 1000) + " 秒");
                    等待(图文等待时间);
                    
                    // 随机上划
                    let 屏幕宽度 = device.width;
                    let 屏幕高度 = device.height;
                    
                    // 上划起点和终点的Y坐标（在屏幕下半部分上划）
                    let 起点Y = 屏幕高度 * (0.7 + Math.random() * 0.2); // 屏幕70%-90%位置
                    let 终点Y = 屏幕高度 * (0.3 + Math.random() * 0.2); // 屏幕30%-50%位置
                    
                    // X坐标在屏幕中间附近随机
                    let X坐标 = 屏幕宽度 * (0.4 + Math.random() * 0.2); // 屏幕40%-60%位置
                    
                    // 执行滑动，持续时间300-800毫秒
                    let 滑动时间 = 300 + Math.floor(Math.random() * 500);
                    console.log("执行上划操作: 从 (" + X坐标 + ", " + 起点Y + ") 到 (" + X坐标 + ", " + 终点Y + ")，持续 " + 滑动时间 + " 毫秒");
                    滑动(X坐标, 起点Y, X坐标, 终点Y, 滑动时间);
                    
                    // 滑动后再等待1-2秒
                    let 滑动后等待时间 = 1000 + Math.floor(Math.random() * 1000);
                    等待(滑动后等待时间);
                }
                
                // 执行互动操作
                let 操作结果 = 执行互动操作(需要点赞 && !当前状态.已点赞, 需要收藏 && !当前状态.已收藏);
                
                if ((需要点赞 && 操作结果.已完成点赞) || (需要收藏 && 操作结果.已完成收藏)) {
                    let 状态消息 = "";
                    if (需要点赞) 状态消息 += 操作结果.已完成点赞 ? "点赞成功" : "点赞失败";
                    if (需要收藏) 状态消息 += 操作结果.已完成收藏 ? (状态消息 ? ", 收藏成功" : "收藏成功") : (状态消息 ? ", 收藏失败" : "收藏失败");
                    toast(状态消息);
                    
                    // 如果所有需要的操作都已完成，跳过后续步骤
                    if ((!需要点赞 || 操作结果.已完成点赞) && (!需要收藏 || 操作结果.已完成收藏)) {
                        console.log("所有操作已完成，准备处理下一条链接");
                        
                        // 返回主界面，准备处理下一个链接
                        返回主界面();
                        等待(操作间隔 * 1000);
                        continue;
                    }
                }
            }
            
            // 返回主界面
            if (!返回主界面()) {
                console.log("返回主界面失败");
                
                // 如果返回主界面失败，尝试重新通过浏览器打开链接
                console.log("尝试重新通过浏览器打开链接");
                强制退出小红书();
                等待(1000);
                
                app.openUrl(链接);
                console.log("已重新打开链接: " + 链接);
                等待(3000);
                
                if (!处理打开流程()) {
                    console.log("重新处理打开流程失败，跳过此链接");
                    continue;
                }
                
                等待(2000);
                
                // 重新检测状态
                当前状态 = 检测文章状态();
                
                // 执行互动操作
                if ((需要点赞 && !当前状态.已点赞) || (需要收藏 && !当前状态.已收藏)) {
                    // 判断是视频还是图文，进行相应等待或滑动
                    let 页面信息 = 获取页面信息();
                    if (页面信息.是否视频) {
                        let 视频等待时间 = 5000 + Math.floor(Math.random() * 5000);
                        console.log("视频内容，等待 " + (视频等待时间 / 1000) + " 秒");
                        等待(视频等待时间);
                    } else {
                        let 图文等待时间 = 1000 + Math.floor(Math.random() * 2000);
                        console.log("图文内容，等待 " + (图文等待时间 / 1000) + " 秒");
                        等待(图文等待时间);
                        
                        // 随机上划
                        let 屏幕宽度 = device.width;
                        let 屏幕高度 = device.height;
                        let 起点Y = 屏幕高度 * (0.7 + Math.random() * 0.2);
                        let 终点Y = 屏幕高度 * (0.3 + Math.random() * 0.2);
                        let X坐标 = 屏幕宽度 * (0.4 + Math.random() * 0.2);
                        let 滑动时间 = 300 + Math.floor(Math.random() * 500);
                        滑动(X坐标, 起点Y, X坐标, 终点Y, 滑动时间);
                        等待(1000 + Math.floor(Math.random() * 1000));
                    }
                    
                    let 操作结果 = 执行互动操作(需要点赞 && !当前状态.已点赞, 需要收藏 && !当前状态.已收藏);
                    
                    let 状态消息 = "";
                    if (需要点赞) 状态消息 += 操作结果.已完成点赞 ? "点赞成功" : "点赞失败";
                    if (需要收藏) 状态消息 += 操作结果.已完成收藏 ? (状态消息 ? ", 收藏成功" : "收藏成功") : (状态消息 ? ", 收藏失败" : "收藏失败");
                    toast(状态消息);
                }
                
                // 返回主界面，准备处理下一个链接
                返回主界面();
                等待(操作间隔 * 1000);
                continue;
            }
            
            // 点击首页第一篇文章
            if (!点击首篇文章()) {
                console.log("点击首页文章失败");
                continue;
            }
            
            等待(3000); // 等待文章加载
            
            // 获取首页文章信息
            let 首页文章信息 = 获取页面信息();
            // 只输出需要的信息
            console.log("首页文章信息: 标题=[" + 首页文章信息.标题 + "], 内容=[" + 首页文章信息.内容 + "], 作者=[" + 首页文章信息.用户名 + "]");
            
            // 比较两篇文章是否为同一篇
            let 是同一篇文章 = 比较页面信息(链接页面信息, 首页文章信息);
            
            if (是同一篇文章) {
                console.log("确认是同一篇文章，检查互动状态");
                
                // 检测当前状态
                当前状态 = 检测文章状态();
                
                // 执行互动操作，跳过已完成的操作
                if ((需要点赞 && !当前状态.已点赞) || (需要收藏 && !当前状态.已收藏)) {
                    console.log(`执行互动操作: 需要点赞=${需要点赞 && !当前状态.已点赞}, 需要收藏=${需要收藏 && !当前状态.已收藏}`);
                    let 操作结果 = 执行互动操作(需要点赞 && !当前状态.已点赞, 需要收藏 && !当前状态.已收藏);
                    
                    let 状态消息 = "";
                    if (需要点赞) 状态消息 += (当前状态.已点赞 ? "已点赞" : (操作结果.已完成点赞 ? "点赞成功" : "点赞失败"));
                    if (需要收藏) 状态消息 += (当前状态.已收藏 ? (状态消息 ? ", 已收藏" : "已收藏") : (操作结果.已完成收藏 ? (状态消息 ? ", 收藏成功" : "收藏成功") : (状态消息 ? ", 收藏失败" : "收藏失败")));
                    toast(状态消息);
                } else {
                    console.log("所有需要的操作都已完成，无需再次操作");
                    toast("所有操作已完成");
                }
            } else {
                console.log("不是同一篇文章，尝试直接通过浏览器打开链接");
                
                // 如果不是同一篇文章，尝试直接通过浏览器打开链接
                强制退出小红书();
                等待(1000);
                
                app.openUrl(链接);
                console.log("已重新打开链接: " + 链接);
                等待(3000);
                
                if (!处理打开流程()) {
                    console.log("重新处理打开流程失败，跳过此链接");
                    continue;
                }
                
                等待(2000);
                
                // 获取页面信息
                let 页面信息 = 获取页面信息();
                
                // 判断是视频还是图文，进行相应等待或滑动
                if (页面信息.是否视频) {
                    // 如果是视频，随机等待5-10秒
                    let 视频等待时间 = 5000 + Math.floor(Math.random() * 5000);
                    console.log("视频内容，等待 " + (视频等待时间 / 1000) + " 秒");
                    等待(视频等待时间);
                } else {
                    // 如果是图文，随机上划并延时
                    let 图文等待时间 = 1000 + Math.floor(Math.random() * 2000);
                    console.log("图文内容，等待 " + (图文等待时间 / 1000) + " 秒");
                    等待(图文等待时间);
                    
                    // 随机上划
                    let 屏幕宽度 = device.width;
                    let 屏幕高度 = device.height;
                    let 起点Y = 屏幕高度 * (0.7 + Math.random() * 0.2);
                    let 终点Y = 屏幕高度 * (0.3 + Math.random() * 0.2);
                    let X坐标 = 屏幕宽度 * (0.4 + Math.random() * 0.2);
                    let 滑动时间 = 300 + Math.floor(Math.random() * 500);
                    滑动(X坐标, 起点Y, X坐标, 终点Y, 滑动时间);
                    等待(1000 + Math.floor(Math.random() * 1000));
                }
                
                // 检测当前状态
                当前状态 = 检测文章状态();
                
                // 执行互动操作，跳过已完成的操作
                if ((需要点赞 && !当前状态.已点赞) || (需要收藏 && !当前状态.已收藏)) {
                    console.log(`执行互动操作: 需要点赞=${需要点赞 && !当前状态.已点赞}, 需要收藏=${需要收藏 && !当前状态.已收藏}`);
                    let 操作结果 = 执行互动操作(需要点赞 && !当前状态.已点赞, 需要收藏 && !当前状态.已收藏);
                    
                    let 状态消息 = "";
                    if (需要点赞) 状态消息 += (当前状态.已点赞 ? "已点赞" : (操作结果.已完成点赞 ? "点赞成功" : "点赞失败"));
                    if (需要收藏) 状态消息 += (当前状态.已收藏 ? (状态消息 ? ", 已收藏" : "已收藏") : (操作结果.已完成收藏 ? (状态消息 ? ", 收藏成功" : "收藏成功") : (状态消息 ? ", 收藏失败" : "收藏失败")));
                    toast(状态消息);
                } else {
                    console.log("所有需要的操作都已完成，无需再次操作");
                    toast("所有操作已完成");
                }
            }
            
            // 返回主界面，准备处理下一个链接
            返回主界面();
            
            // 等待指定时间
            console.log(`等待${操作间隔}秒后处理下一条链接`);
            等待(操作间隔 * 1000);
            
        } catch (e) {
            console.error("处理链接时出错: " + e);
            等待(2000);
        }
    }
    
    toast("全部操作已完成");
}

// 更新UI部分，添加收藏选项
// 在ui.layout中添加收藏选项的代码需要单独编辑

